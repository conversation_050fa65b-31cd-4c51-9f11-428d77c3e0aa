# Sansar Script Patterns - <PERSON><PERSON> Learned

## Player Input - Proper Command Subscription

### The Wrong Way (Doesn't Work)
```csharp
// These methods don't exist or don't work in Sansar
SubscribeToScriptEvent("Keypad4", HandleMoveLeft);
SubscribeToAll(HandleAllCommands);
client.SubscribeToCommand("A", ...); // Wrong key names
```

### The Right Way (Tested and Working)
```csharp
// Step 1: Get the agent when they sit down
private void OnPlayerSitDown(SitObjectData data)
{
    currentPlayer = ScenePrivate.FindAgent(data.ObjectId);
    SubscribeToPlayerControls();
}

// Step 2: Subscribe to specific Sansar command events
private void SubscribeToPlayerControls()
{
    if (currentPlayer == null || !currentPlayer.IsValid) return;
    
    // Use the actual Sansar command names
    commandSubscriptions[0] = currentPlayer.Client.SubscribeToCommand("Keypad4", CommandAction.Pressed, HandleMoveLeft, null);     // A key
    commandSubscriptions[1] = currentPlayer.Client.SubscribeToCommand("Keypad6", CommandAction.Pressed, HandleMoveRight, null);    // D key
    commandSubscriptions[2] = currentPlayer.Client.SubscribeToCommand("Keypad2", CommandAction.Pressed, HandleSoftDrop, null);     // S key
    commandSubscriptions[3] = currentPlayer.Client.SubscribeToCommand("SecondaryAction", CommandAction.Pressed, HandleRotate, null); // R key
    commandSubscriptions[4] = currentPlayer.Client.SubscribeToCommand("PrimaryAction", CommandAction.Pressed, HandleHardDrop, null); // F key
}

// Step 3: Use CommandData in handlers (not ScriptEventData)
private void HandleMoveLeft(CommandData data)
{
    // Your game logic here
}

// Step 4: Clean up when player leaves
private void OnPlayerStandUp(SitObjectData data)
{
    UnsubscribeFromPlayerControls();
    currentPlayer = null;
}
```

### Sansar Command Mappings (Discovered Through Testing)
- **Keypad4** = A key → Move left
- **Keypad6** = D key → Move right  
- **Keypad2** = S key → Soft drop
- **Keypad8** = W key → Slow down
- **SecondaryAction** = R key → Rotate
- **PrimaryAction** = F key → Hard drop
- **Action1** = 1 key → Rotate counter-clockwise

## Material Control - Direct Mesh Access

### The Right Way (Immediate Response)
```csharp
// Step 1: Store MeshComponent references when spawning
private MeshComponent[,] blockMeshes = new MeshComponent[GRID_WIDTH, GRID_HEIGHT];

// Step 2: Get mesh reference after cluster spawn
private void OnBlockSpawned(Cluster cluster, int gridX, int gridY)
{
    foreach (var obj in cluster.GetClusterObjects())
    {
        if (obj.TryGetFirstComponent(out MeshComponent mesh) && mesh.IsScriptable)
        {
            blockMeshes[gridX, gridY] = mesh;
            break;
        }
    }
}

// Step 3: Direct material control for immediate visual feedback
public void SetBlockVisible(int gridX, int gridY, int pieceType, bool isActivePiece = false)
{
    MeshComponent mesh = blockMeshes[gridX, gridY];
    if (mesh != null && mesh.IsScriptable)
    {
        foreach (var material in mesh.GetRenderMaterials())
        {
            var props = material.GetProperties();
            
            if (material.HasTint)
            {
                props.Tint = PieceColors[pieceType];
            }
            
            if (material.HasEmissiveIntensity)
            {
                props.EmissiveIntensity = isActivePiece ? 2.0f : 0.0f;
            }
            
            material.SetProperties(props);
        }
    }
}
```

## Line Clearing - Proper Drop Logic

### The Wrong Way (Caused Invisible Occupied Blocks)
```csharp
// Processing from bottom to top with complex index adjustment
clearedLines.Sort(); // Bottom to top
for (int clearIndex = 0; clearIndex < clearedLines.Count; clearIndex++)
{
    // Complex index adjustment logic that fails with multiple lines
    for (int i = clearIndex + 1; i < clearedLines.Count; i++)
    {
        clearedLines[i]--; // This breaks with multiple cleared lines
    }
}
```

### The Right Way (Fixed Grid State Consistency)
```csharp
private void DropLinesAfterClear(List<int> clearedLines)
{
    // Sort cleared lines from top to bottom (highest Y first)
    clearedLines.Sort();
    clearedLines.Reverse();
    
    // Process each cleared line from top to bottom
    foreach (int clearedY in clearedLines)
    {
        // Move all lines above this cleared line down by one
        for (int y = clearedY; y < GRID_HEIGHT - 1; y++)
        {
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                // Copy the line above down to current position
                gridState[x, y] = gridState[x, y + 1];
                
                // Update visual representation immediately
                if (gridState[x, y] != null)
                {
                    SetBlock(x, y, gridState[x, y].Value, false);
                }
                else
                {
                    ClearBlock(x, y);
                }
            }
        }
        
        // Clear the top row since everything shifted down
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            gridState[x, GRID_HEIGHT - 1] = null;
            ClearBlock(x, GRID_HEIGHT - 1);
        }
    }
}
```

## Architecture Lessons

### Single Script vs Multi-Script
**Problem**: Multiple scripts communicating via events had timing issues and complexity.

**Solution**: Unified single script approach eliminates:
- Inter-script communication delays
- Event timing issues  
- Complex state synchronization
- Hard-to-debug event chains

### Collision Detection Consistency
**Problem**: Grid state in one script, collision detection in another caused desync.

**Solution**: Keep `gridState[,]` array and collision detection in the same script:
```csharp
public bool IsBlockOccupied(int x, int y)
{
    if (x < 0 || x >= GRID_WIDTH || y < 0 || y >= GRID_HEIGHT)
        return true; // Out of bounds = occupied
    
    return gridState[x, y] != null;
}
```

## C# 5 Compatibility Notes

Sansar uses C# 5, so avoid:
- Inline variable declarations: `if (obj.TryGet(out var component))` 
- String interpolation: `$"Block at {x}, {y}"`
- Expression bodied members: `public bool IsValid => value != null;`

Use instead:
- Separate declarations: `MeshComponent component; if (obj.TryGet(out component))`
- String.Format: `string.Format("Block at {0}, {1}", x, y)`
- Full method bodies: `public bool IsValid { get { return value != null; } }`

## Performance Patterns

### Batch Spawning
```csharp
// Spawn blocks in batches to prevent throttling
private void SpawnGridBlocks()
{
    for (int i = 0; i < totalBlocksToSpawn; i += BatchSize)
    {
        for (int j = 0; j < BatchSize && (i + j) < totalBlocksToSpawn; j++)
        {
            SpawnSingleBlock(i + j);
        }
        
        // Wait between batches
        StartCoroutine(DelayedSpawn, BatchDelay);
    }
}
```

### Material Property Caching
```csharp
// Check material capabilities once and cache
if (material.HasTint && material.HasEmissiveIntensity)
{
    // Cache the capability check result
    materialCapabilities[gridX, gridY] = true;
}
```