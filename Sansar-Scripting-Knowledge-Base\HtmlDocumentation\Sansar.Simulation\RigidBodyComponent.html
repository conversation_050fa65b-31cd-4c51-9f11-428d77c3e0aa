<html>
  <head>
    <title>Sansar.Simulation.RigidBodyComponent</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.RigidBodyComponent">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.RigidBodyComponent:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.RigidBodyComponent:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.RigidBodyComponent:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.RigidBodyComponent">RigidBodyComponent  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.RigidBodyComponent:Summary">The RigidBodyComponent handles interactions with rigid body physics.</p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.RigidBodyComponent:Signature">[Sansar.Script.Interface]<br />public class  <b>RigidBodyComponent</b> : <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.RigidBodyComponent:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.RigidBodyComponent:Docs:Remarks">
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.RigidBodyComponent:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.
							</p>
        <h2 class="Section">Public Fields</h2>
        <div class="SectionBox" id="Public Fields">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.RigidBodyComponent.ComponentType">ComponentType</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/ComponentType.html">ComponentType</a>
                  </i>. The <a href="../Sansar.Simulation/RigidBodyComponent.html#F:Sansar.Simulation.RigidBodyComponent.ComponentType">RigidBodyComponent.ComponentType</a> of this component</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.RigidBodyComponent.ComponentId">ComponentId</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a>
                  </i>. Retrieves the component id for this RigidBodyComponent.</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/InstanceInterface.html#P:Sansar.Script.InstanceInterface.IsValid">IsValid</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            Whether or not this interface is valid.
             (<i>Inherited from <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.RigidBodyComponent.Name">Name</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. This RigidBodyComponent name, as specified in the editor.</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector)">AddAngularImpulse</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>)<blockquote>Adds an angular impulse to this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">AddAngularImpulse</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Adds an angular impulse to this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">AddAngularImpulse</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Adds an angular impulse to this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector)">AddLinearImpulse</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>)<blockquote>Adds a linear impulse the the center of mass of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">AddLinearImpulse</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Adds a linear impulse the the center of mass of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">AddLinearImpulse</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Adds a linear impulse the the center of mass of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetAngularDamping()">GetAngularDamping</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>Retrieves the amount of damping applied to this object's angular motion.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetAngularVelocity()">GetAngularVelocity</a>
                  </b>()<nobr> : <a href="../Sansar/Vector.html">Sansar.Vector</a></nobr><blockquote>Retrieves the angular velocity for this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetBounce()">GetBounce</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>Retrieves the bounce of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetCanGrab()">GetCanGrab</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a></nobr><blockquote>The can grab state for this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetCenterOfMass()">GetCenterOfMass</a>
                  </b>()<nobr> : <a href="../Sansar/Vector.html">Sansar.Vector</a></nobr><blockquote>Retrieves the center of mass of this object in local space.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetColliderCenter()">GetColliderCenter</a>
                  </b>()<nobr> : <a href="../Sansar/Vector.html">Sansar.Vector</a></nobr><blockquote>Retrieves the center of the collision shape for this object in local space.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetColliderExtents()">GetColliderExtents</a>
                  </b>()<nobr> : <a href="../Sansar/Vector.html">Sansar.Vector</a></nobr><blockquote>Retrieves the extents of the collision shape for this object in local space.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetDynamicFriction()">GetDynamicFriction</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>Retrieves the dynamic friction of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetGravityFactor()">GetGravityFactor</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>Retrieves what factor of the world's gravity is applied to this object.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetHeldObjectInfo()">GetHeldObjectInfo</a>
                  </b>()<nobr> : <a href="../Sansar.Simulation/HeldObjectInfo.html">HeldObjectInfo</a></nobr><blockquote>Data about who is holding this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetInverseInertia()">GetInverseInertia</a>
                  </b>()<nobr> : <a href="../Sansar/Vector.html">Sansar.Vector</a></nobr><blockquote>Retrieves the moment of inertia for this object.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetLinearDamping()">GetLinearDamping</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>Retrieves the amount of damping applied to this object's linear motion.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetLinearVelocity()">GetLinearVelocity</a>
                  </b>()<nobr> : <a href="../Sansar/Vector.html">Sansar.Vector</a></nobr><blockquote>Retrieves the linear velocity for this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetMass()">GetMass</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>Retrieves the mass of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetMotionType()">GetMotionType</a>
                  </b>()<nobr> : <a href="../Sansar.Simulation/RigidBodyMotionType.html">RigidBodyMotionType</a></nobr><blockquote>The motion type for this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetOrientation()">GetOrientation</a>
                  </b>()<nobr> : <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a></nobr><blockquote>Retrieves the orientation of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetPosition()">GetPosition</a>
                  </b>()<nobr> : <a href="../Sansar/Vector.html">Sansar.Vector</a></nobr><blockquote>Retrieves the position of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetSitObjectInfo()">GetSitObjectInfo</a>
                  </b>()<nobr> : <a href="../Sansar.Simulation/SitObjectInfo.html">SitObjectInfo</a>[]</nobr><blockquote>Data about who is sitting on this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.GetStaticFriction()">GetStaticFriction</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>Retrieves the static friction of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.IsDynamic()">IsDynamic</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a></nobr><blockquote>Whether this rigidbody is dynamic.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.IsTriggerVolume()">IsTriggerVolume</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a></nobr><blockquote>Whether this RigidBodyComponent is a TriggerVolume.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.ReleaseHeldObject()">ReleaseHeldObject</a>
                  </b>()<blockquote>Force any agent holding this object to drop it.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.ReleaseHeldObject(System.Action{Sansar.Script.OperationCompleteEvent})">ReleaseHeldObject</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Force any agent holding this object to drop it.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single)">SetAngularDamping</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<blockquote>Sets the amount of damping to apply to this object's angular motion.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetAngularDamping</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the amount of damping to apply to this object's angular motion.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetAngularDamping</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the amount of damping to apply to this object's angular motion.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector)">SetAngularVelocity</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>)<blockquote>Sets the angular velocity for this RigidBodyComponent. Angular velocity is clamped between -100m/s and 100m/s.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">SetAngularVelocity</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the angular velocity for this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetAngularVelocity</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the angular velocity for this RigidBodyComponent. Angular velocity is clamped between -100m/s and 100m/s.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single)">SetBounce</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<blockquote>Sets the bounce of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetBounce</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the bounce of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetBounce</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the bounce of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean)">SetCanGrab</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<blockquote>Sets the can grab state for this rigidbody.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent})">SetCanGrab</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the can grab state for this rigidbody.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector)">SetCenterOfMass</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>)<blockquote>Sets the center of mass of this object in local space.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">SetCenterOfMass</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the center of mass of this object in local space.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetCenterOfMass</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the center of mass of this object in local space.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single)">SetDynamicFriction</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<blockquote>Sets the dynamic friction of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetDynamicFriction</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the dynamic friction of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetDynamicFriction</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the dynamic friction of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single)">SetGravityFactor</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<blockquote>Sets what factor of the world's gravity is applied to this object.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetGravityFactor</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets what factor of the world's gravity is applied to this object.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetGravityFactor</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets what factor of the world's gravity is applied to this object.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector)">SetInverseInertia</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>)<blockquote>Sets the moment of inertia for this object.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetInverseInertia</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the moment of inertia for this object.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single)">SetLinearDamping</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<blockquote>Sets the amount of damping to apply to this object's linear motion.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetLinearDamping</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the amount of damping to apply to this object's linear motion.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetLinearDamping</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the amount of damping to apply to this object's linear motion.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector)">SetLinearVelocity</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>)<blockquote>Sets the linear velocity for this RigidBodyComponent. Linear velocity is clamped between -200m/s and 200m/s.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">SetLinearVelocity</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the linear velocity for this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetLinearVelocity</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the linear velocity for this RigidBodyComponent. Linear velocity is clamped between -200m/s and 200m/s.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single)">SetMass</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<blockquote>Sets the mass of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetMass</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the mass of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetMass</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the mass of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType)">SetMotionType</a>
                  </b>(<a href="../Sansar.Simulation/RigidBodyMotionType.html">RigidBodyMotionType</a>)<blockquote>Sets the motion type for this rigidbody.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,Sansar.Script.ScriptBase.OperationComplete)">SetMotionType</a>
                  </b>(<a href="../Sansar.Simulation/RigidBodyMotionType.html">RigidBodyMotionType</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the motion type for this rigidbody.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,System.Action{Sansar.Script.OperationCompleteEvent})">SetMotionType</a>
                  </b>(<a href="../Sansar.Simulation/RigidBodyMotionType.html">RigidBodyMotionType</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the motion type for this rigidbody.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion)">SetOrientation</a>
                  </b>(<a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>)<blockquote>Sets the orientation of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector)">SetOrientation</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the orientation of this $className$.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,Sansar.Script.ScriptBase.OperationComplete)">SetOrientation</a>
                  </b>(<a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the orientation of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,System.Action{Sansar.Script.OperationCompleteEvent})">SetOrientation</a>
                  </b>(<a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the orientation of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">SetOrientation</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the orientation of this $className$.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetOrientation</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the orientation of this $className$.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector)">SetPosition</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>)<blockquote>Sets the position of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">SetPosition</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the position of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetPosition</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the position of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single)">SetStaticFriction</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<blockquote>Sets the static friction of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetStaticFriction</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the static friction of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetStaticFriction</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the static friction of this RigidBodyComponent.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Simulation.RigidBodyComponent.SubscriptionHandler,System.Boolean)">Subscribe</a>
                  </b>(<a href="../Sansar.Simulation/CollisionEventType.html">CollisionEventType</a>, <a href="../Sansar.Simulation/RigidBodyComponent+SubscriptionHandler.html">RigidBodyComponent.SubscriptionHandler</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<blockquote>Subscribes to Collision Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,System.Action{Sansar.Simulation.CollisionData},System.Boolean)">Subscribe</a>
                  </b>(<a href="../Sansar.Simulation/CollisionEventType.html">CollisionEventType</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;CollisionData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Collision Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,Sansar.Simulation.RigidBodyComponent.SubscriptionHandler,System.Boolean)">Subscribe</a>
                  </b>(<a href="../Sansar.Simulation/CollisionEventType.html">CollisionEventType</a>, <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a>, <a href="../Sansar.Simulation/RigidBodyComponent+SubscriptionHandler.html">RigidBodyComponent.SubscriptionHandler</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<blockquote>Subscribes to Collision Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,System.Action{Sansar.Simulation.CollisionData},System.Boolean)">Subscribe</a>
                  </b>(<a href="../Sansar.Simulation/CollisionEventType.html">CollisionEventType</a>, <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;CollisionData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Collision Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SubscribeToHeldObject(Sansar.Simulation.HeldObjectEventType,System.Action{Sansar.Simulation.HeldObjectData},System.Boolean)">SubscribeToHeldObject</a>
                  </b>(<a href="../Sansar.Simulation/HeldObjectEventType.html">HeldObjectEventType</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;HeldObjectData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to HeldObject Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.SubscribeToSitObject(Sansar.Simulation.SitEventType,System.Action{Sansar.Simulation.SitObjectData},System.Boolean)">SubscribeToSitObject</a>
                  </b>(<a href="../Sansar.Simulation/SitEventType.html">SitEventType</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;SitObjectData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to SitObject Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.RigidBodyComponent.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote> A string representation of this object. </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.RigidBodyComponent:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector)">AddAngularImpulse Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector):member">
          <div class="msummary">Adds an angular impulse to this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>AddAngularImpulse</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> impulse)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>impulse</i>
              </dt>
              <dd>The impulse.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">AddAngularImpulse Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Adds an angular impulse to this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>AddAngularImpulse</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> impulse, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>impulse</i>
              </dt>
              <dd>The impulse.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">AddAngularImpulse Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Adds an angular impulse to this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>AddAngularImpulse</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> impulse, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>impulse</i>
              </dt>
              <dd>The impulse.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddAngularImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector)">AddLinearImpulse Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector):member">
          <div class="msummary">Adds a linear impulse the the center of mass of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>AddLinearImpulse</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> impulse)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>impulse</i>
              </dt>
              <dd>The impulse.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">AddLinearImpulse Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Adds a linear impulse the the center of mass of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>AddLinearImpulse</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> impulse, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>impulse</i>
              </dt>
              <dd>The impulse.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">AddLinearImpulse Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Adds a linear impulse the the center of mass of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>AddLinearImpulse</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> impulse, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>impulse</i>
              </dt>
              <dd>The impulse.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.AddLinearImpulse(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.RigidBodyComponent.ComponentId">ComponentId Property</h3>
        <blockquote id="P:Sansar.Simulation.RigidBodyComponent.ComponentId:member">
          <div class="msummary">Retrieves the component id for this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a> <b>ComponentId</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.RigidBodyComponent.ComponentId:Value">The id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.RigidBodyComponent.ComponentId:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.RigidBodyComponent.ComponentId:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.RigidBodyComponent.ComponentType">ComponentType Field</h3>
        <blockquote id="F:Sansar.Simulation.RigidBodyComponent.ComponentType:member">
          <div class="msummary">The <a href="../Sansar.Simulation/RigidBodyComponent.html#F:Sansar.Simulation.RigidBodyComponent.ComponentType">RigidBodyComponent.ComponentType</a> of this component</div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="../Sansar.Simulation/ComponentType.html">ComponentType</a> <b>ComponentType</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.RigidBodyComponent.ComponentType:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.RigidBodyComponent.ComponentType:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetAngularDamping()">GetAngularDamping Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetAngularDamping():member">
          <div class="msummary">Retrieves the amount of damping applied to this object's angular motion.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>GetAngularDamping</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetAngularDamping():Returns">The angular damping factor.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetAngularDamping():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetAngularDamping():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetAngularVelocity()">GetAngularVelocity Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetAngularVelocity():member">
          <div class="msummary">Retrieves the angular velocity for this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar/Vector.html">Sansar.Vector</a> <b>GetAngularVelocity</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetAngularVelocity():Returns">The velocity.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetAngularVelocity():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetAngularVelocity():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetBounce()">GetBounce Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetBounce():member">
          <div class="msummary">Retrieves the bounce of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>GetBounce</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetBounce():Returns">The bounce factor.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetBounce():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetBounce():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetCanGrab()">GetCanGrab Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetCanGrab():member">
          <div class="msummary">The can grab state for this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>GetCanGrab</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetCanGrab():Returns">A bool indicating if the object can be picked up.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetCanGrab():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetCanGrab():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetCenterOfMass()">GetCenterOfMass Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetCenterOfMass():member">
          <div class="msummary">Retrieves the center of mass of this object in local space.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar/Vector.html">Sansar.Vector</a> <b>GetCenterOfMass</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetCenterOfMass():Returns">A Vector to the center of mass of this object, in the objects local space.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetCenterOfMass():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetCenterOfMass():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetColliderCenter()">GetColliderCenter Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetColliderCenter():member">
          <div class="msummary">Retrieves the center of the collision shape for this object in local space.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar/Vector.html">Sansar.Vector</a> <b>GetColliderCenter</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetColliderCenter():Returns">The center of the collision shape.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetColliderCenter():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetColliderCenter():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetColliderExtents()">GetColliderExtents Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetColliderExtents():member">
          <div class="msummary">Retrieves the extents of the collision shape for this object in local space.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar/Vector.html">Sansar.Vector</a> <b>GetColliderExtents</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetColliderExtents():Returns">The extents of the collision shape.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetColliderExtents():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetColliderExtents():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetDynamicFriction()">GetDynamicFriction Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetDynamicFriction():member">
          <div class="msummary">Retrieves the dynamic friction of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>GetDynamicFriction</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetDynamicFriction():Returns">The dynamic friction.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetDynamicFriction():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetDynamicFriction():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetGravityFactor()">GetGravityFactor Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetGravityFactor():member">
          <div class="msummary">Retrieves what factor of the world's gravity is applied to this object.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>GetGravityFactor</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetGravityFactor():Returns">The gravity factor.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetGravityFactor():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetGravityFactor():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetHeldObjectInfo()">GetHeldObjectInfo Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetHeldObjectInfo():member">
          <div class="msummary">Data about who is holding this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/HeldObjectInfo.html">HeldObjectInfo</a> <b>GetHeldObjectInfo</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetHeldObjectInfo():Returns">A <a href="../Sansar.Simulation/HeldObjectInfo.html">Sansar.Simulation.HeldObjectInfo</a> struct.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetHeldObjectInfo():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetHeldObjectInfo():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetInverseInertia()">GetInverseInertia Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetInverseInertia():member">
          <div class="msummary">Retrieves the moment of inertia for this object.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar/Vector.html">Sansar.Vector</a> <b>GetInverseInertia</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetInverseInertia():Returns">A Vector representation of the inverse moment of inertia.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetInverseInertia():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetInverseInertia():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetLinearDamping()">GetLinearDamping Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetLinearDamping():member">
          <div class="msummary">Retrieves the amount of damping applied to this object's linear motion.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>GetLinearDamping</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetLinearDamping():Returns">The linear damping factor.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetLinearDamping():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetLinearDamping():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetLinearVelocity()">GetLinearVelocity Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetLinearVelocity():member">
          <div class="msummary">Retrieves the linear velocity for this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar/Vector.html">Sansar.Vector</a> <b>GetLinearVelocity</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetLinearVelocity():Returns">The velocity.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetLinearVelocity():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetLinearVelocity():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetMass()">GetMass Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetMass():member">
          <div class="msummary">Retrieves the mass of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>GetMass</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetMass():Returns">The mass.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetMass():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetMass():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetMotionType()">GetMotionType Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetMotionType():member">
          <div class="msummary">The motion type for this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RigidBodyMotionType.html">RigidBodyMotionType</a> <b>GetMotionType</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetMotionType():Returns">A RigidBodyMotionType enum for the motion type.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetMotionType():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetMotionType():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetOrientation()">GetOrientation Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetOrientation():member">
          <div class="msummary">Retrieves the orientation of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> <b>GetOrientation</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetOrientation():Returns">The orientation.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetOrientation():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetOrientation():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetPosition()">GetPosition Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetPosition():member">
          <div class="msummary">Retrieves the position of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar/Vector.html">Sansar.Vector</a> <b>GetPosition</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetPosition():Returns">The position.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetPosition():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetPosition():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetSitObjectInfo()">GetSitObjectInfo Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetSitObjectInfo():member">
          <div class="msummary">Data about who is sitting on this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/SitObjectInfo.html">SitObjectInfo</a>[] <b>GetSitObjectInfo</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetSitObjectInfo():Returns">A <a href="../Sansar.Simulation/SitObjectInfo.html">Sansar.Simulation.SitObjectInfo</a> struct.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetSitObjectInfo():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetSitObjectInfo():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.GetStaticFriction()">GetStaticFriction Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.GetStaticFriction():member">
          <div class="msummary">Retrieves the static friction of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>GetStaticFriction</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetStaticFriction():Returns">The static friction.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetStaticFriction():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.GetStaticFriction():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.IsDynamic()">IsDynamic Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.IsDynamic():member">
          <div class="msummary">Whether this rigidbody is dynamic.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Use GetMotionType()", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>IsDynamic</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.IsDynamic():Returns">True if this is a dynamic object.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.IsDynamic():Remarks">Deprecated. Use GetMotionType() instead.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.IsDynamic():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.IsTriggerVolume()">IsTriggerVolume Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.IsTriggerVolume():member">
          <div class="msummary">Whether this RigidBodyComponent is a TriggerVolume.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>IsTriggerVolume</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.IsTriggerVolume():Returns">True if this RigidBodyComponent is a TriggerVolume.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.IsTriggerVolume():Remarks">TriggerVolumes can be moved through and generate <a href="../Sansar.Simulation/CollisionEventType.html#F:Sansar.Simulation.CollisionEventType.cTrigger">CollisionEventType.cTrigger</a> collision events instead of normal collision events.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.IsTriggerVolume():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.RigidBodyComponent.Name">Name Property</h3>
        <blockquote id="P:Sansar.Simulation.RigidBodyComponent.Name:member">
          <div class="msummary">This RigidBodyComponent name, as specified in the editor.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>Name</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.RigidBodyComponent.Name:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.RigidBodyComponent.Name:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.RigidBodyComponent.Name:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.ReleaseHeldObject()">ReleaseHeldObject Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.ReleaseHeldObject():member">
          <div class="msummary">Force any agent holding this object to drop it.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>ReleaseHeldObject</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.ReleaseHeldObject():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.ReleaseHeldObject():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.ReleaseHeldObject(System.Action{Sansar.Script.OperationCompleteEvent})">ReleaseHeldObject Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.ReleaseHeldObject(System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Force any agent holding this object to drop it.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>ReleaseHeldObject</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.ReleaseHeldObject(System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.ReleaseHeldObject(System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.ReleaseHeldObject(System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single)">SetAngularDamping Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single):member">
          <div class="msummary">Sets the amount of damping to apply to this object's angular motion.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetAngularDamping</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> angularDamping)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single):Parameters">
            <dl>
              <dt>
                <i>angularDamping</i>
              </dt>
              <dd>The angular damping factor. Default value is 0.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetAngularDamping Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the amount of damping to apply to this object's angular motion.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetAngularDamping</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> angularDamping, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>angularDamping</i>
              </dt>
              <dd>The angular damping factor. Default value is 0.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetAngularDamping Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the amount of damping to apply to this object's angular motion.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetAngularDamping</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> angularDamping, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>angularDamping</i>
              </dt>
              <dd>The angular damping factor. Default value is 0.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector)">SetAngularVelocity Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector):member">
          <div class="msummary">Sets the angular velocity for this RigidBodyComponent. Angular velocity is clamped between -100m/s and 100m/s.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetAngularVelocity</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> velocity)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>velocity</i>
              </dt>
              <dd>The velocity.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector):Remarks">This asynchronous method queues the write then returns. Subsequent calls to <a href="../Sansar.Simulation/RigidBodyComponent.html#M:Sansar.Simulation.RigidBodyComponent.GetAngularVelocity">RigidBodyComponent.GetAngularVelocity</a> will return the previous velocity until the write occurs.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">SetAngularVelocity Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the angular velocity for this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetAngularVelocity</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> velocity, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>velocity</i>
              </dt>
              <dd>The velocity.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns. Subsequent calls to <a href="../Sansar.Simulation/RigidBodyComponent.html#M:Sansar.Simulation.RigidBodyComponent.GetAngularVelocity">RigidBodyComponent.GetAngularVelocity</a> will return the previous velocity until the write occurs.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetAngularVelocity Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the angular velocity for this RigidBodyComponent. Angular velocity is clamped between -100m/s and 100m/s.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetAngularVelocity</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> velocity, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>velocity</i>
              </dt>
              <dd>The velocity.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns. Subsequent calls to <a href="../Sansar.Simulation/RigidBodyComponent.html#M:Sansar.Simulation.RigidBodyComponent.GetAngularVelocity">RigidBodyComponent.GetAngularVelocity</a> will return the previous velocity until the write occurs.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetAngularVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single)">SetBounce Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single):member">
          <div class="msummary">Sets the bounce of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetBounce</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> bounce)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single):Parameters">
            <dl>
              <dt>
                <i>bounce</i>
              </dt>
              <dd>The bounce factor. Value should be between 0 and 1 (inclusive)</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetBounce Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the bounce of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetBounce</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> bounce, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>bounce</i>
              </dt>
              <dd>The bounce factor. Value should be between 0 and 1 (inclusive)</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetBounce Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the bounce of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetBounce</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> bounce, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>bounce</i>
              </dt>
              <dd>The bounce factor. Value should be between 0 and 1 (inclusive)</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetBounce(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean)">SetCanGrab Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean):member">
          <div class="msummary">Sets the can grab state for this rigidbody.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetCanGrab</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> canGrab)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean):Parameters">
            <dl>
              <dt>
                <i>canGrab</i>
              </dt>
              <dd>Boolean value to indicate if the rigid body can be grabbed.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean):Remarks">This method will throw an exception if the rigid body has the static motion type.  This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent})">SetCanGrab Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the can grab state for this rigidbody.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetCanGrab</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> canGrab, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>canGrab</i>
              </dt>
              <dd>Boolean value to indicate if the rigid body can be grabbed.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This method will throw an exception if the rigid body has the static motion type.  This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCanGrab(System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector)">SetCenterOfMass Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector):member">
          <div class="msummary">Sets the center of mass of this object in local space.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetCenterOfMass</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> centerOfMass)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>centerOfMass</i>
              </dt>
              <dd>A Vector to the center of mass of this object, in the objects local space.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">SetCenterOfMass Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the center of mass of this object in local space.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetCenterOfMass</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> centerOfMass, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>centerOfMass</i>
              </dt>
              <dd>A Vector to the center of mass of this object, in the objects local space.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetCenterOfMass Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the center of mass of this object in local space.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetCenterOfMass</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> centerOfMass, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>centerOfMass</i>
              </dt>
              <dd>A Vector to the center of mass of this object, in the objects local space.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetCenterOfMass(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single)">SetDynamicFriction Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single):member">
          <div class="msummary">Sets the dynamic friction of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetDynamicFriction</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> dynamicFriction)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single):Parameters">
            <dl>
              <dt>
                <i>dynamicFriction</i>
              </dt>
              <dd>The dynamic friction. Value should be between 0 and 1 (inclusive)</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetDynamicFriction Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the dynamic friction of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetDynamicFriction</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> dynamicFriction, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>dynamicFriction</i>
              </dt>
              <dd>The dynamic friction. Value should be between 0 and 1 (inclusive)</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetDynamicFriction Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the dynamic friction of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetDynamicFriction</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> dynamicFriction, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>dynamicFriction</i>
              </dt>
              <dd>The dynamic friction. Value should be between 0 and 1 (inclusive)</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetDynamicFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single)">SetGravityFactor Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single):member">
          <div class="msummary">Sets what factor of the world's gravity is applied to this object.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetGravityFactor</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> gravityFactor)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single):Parameters">
            <dl>
              <dt>
                <i>gravityFactor</i>
              </dt>
              <dd>The gravityFactor. Default value is 1, range is -2 to 2</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetGravityFactor Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets what factor of the world's gravity is applied to this object.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetGravityFactor</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> gravityFactor, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>gravityFactor</i>
              </dt>
              <dd>The gravityFactor. Default value is 1, range is -2 to 2</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetGravityFactor Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets what factor of the world's gravity is applied to this object.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetGravityFactor</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> gravityFactor, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>gravityFactor</i>
              </dt>
              <dd>The gravityFactor. Default value is 1, range is -2 to 2</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetGravityFactor(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector)">SetInverseInertia Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector):member">
          <div class="msummary">Sets the moment of inertia for this object.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetInverseInertia</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> inverseInertia)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>inverseInertia</i>
              </dt>
              <dd>A Vector representation of the inverse moment of inertia for this object in world space.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector):Remarks">Setting this to zero in the x and y components, for example, will give this object
            infinite inertia on the x and y axes and only permit it to rotate around the z axis.
            This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetInverseInertia Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the moment of inertia for this object.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetInverseInertia</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> inverseInertia, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>inverseInertia</i>
              </dt>
              <dd>A Vector representation of the inverse moment of inertia for this object in world space.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">Setting this to zero in the x and y components, for example, will give this object
            infinite inertia on the x and y axes and only permit it to rotate around the z axis.
            This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetInverseInertia(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single)">SetLinearDamping Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single):member">
          <div class="msummary">Sets the amount of damping to apply to this object's linear motion.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetLinearDamping</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> linearDamping)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single):Parameters">
            <dl>
              <dt>
                <i>linearDamping</i>
              </dt>
              <dd>The linear damping factor. Default value is 0.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetLinearDamping Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the amount of damping to apply to this object's linear motion.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetLinearDamping</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> linearDamping, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>linearDamping</i>
              </dt>
              <dd>The linear damping factor. Default value is 0.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetLinearDamping Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the amount of damping to apply to this object's linear motion.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetLinearDamping</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> linearDamping, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>linearDamping</i>
              </dt>
              <dd>The linear damping factor. Default value is 0.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearDamping(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector)">SetLinearVelocity Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector):member">
          <div class="msummary">Sets the linear velocity for this RigidBodyComponent. Linear velocity is clamped between -200m/s and 200m/s.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetLinearVelocity</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> velocity)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>velocity</i>
              </dt>
              <dd>The velocity.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector):Remarks">This asynchronous method queues the write then returns. Subsequent calls to <a href="../Sansar.Simulation/RigidBodyComponent.html#M:Sansar.Simulation.RigidBodyComponent.GetLinearVelocity">RigidBodyComponent.GetLinearVelocity</a> will return the previous velocity until the write occurs.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">SetLinearVelocity Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the linear velocity for this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetLinearVelocity</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> velocity, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>velocity</i>
              </dt>
              <dd>The velocity.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns. Subsequent calls to <a href="../Sansar.Simulation/RigidBodyComponent.html#M:Sansar.Simulation.RigidBodyComponent.GetLinearVelocity">RigidBodyComponent.GetLinearVelocity</a> will return the previous velocity until the write occurs.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetLinearVelocity Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the linear velocity for this RigidBodyComponent. Linear velocity is clamped between -200m/s and 200m/s.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetLinearVelocity</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> velocity, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>velocity</i>
              </dt>
              <dd>The velocity.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns. Subsequent calls to <a href="../Sansar.Simulation/RigidBodyComponent.html#M:Sansar.Simulation.RigidBodyComponent.GetLinearVelocity">RigidBodyComponent.GetLinearVelocity</a> will return the previous velocity until the write occurs.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetLinearVelocity(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single)">SetMass Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single):member">
          <div class="msummary">Sets the mass of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetMass</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> mass)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single):Parameters">
            <dl>
              <dt>
                <i>mass</i>
              </dt>
              <dd>The mass.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetMass Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the mass of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetMass</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> mass, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>mass</i>
              </dt>
              <dd>The mass.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetMass Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the mass of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetMass</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> mass, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>mass</i>
              </dt>
              <dd>The mass.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMass(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType)">SetMotionType Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType):member">
          <div class="msummary">Sets the motion type for this rigidbody.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetMotionType</b> (<a href="../Sansar.Simulation/RigidBodyMotionType.html">RigidBodyMotionType</a> motionType)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType):Parameters">
            <dl>
              <dt>
                <i>motionType</i>
              </dt>
              <dd>The motion type.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,Sansar.Script.ScriptBase.OperationComplete)">SetMotionType Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the motion type for this rigidbody.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetMotionType</b> (<a href="../Sansar.Simulation/RigidBodyMotionType.html">RigidBodyMotionType</a> motionType, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>motionType</i>
              </dt>
              <dd>The motion type.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,System.Action{Sansar.Script.OperationCompleteEvent})">SetMotionType Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the motion type for this rigidbody.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetMotionType</b> (<a href="../Sansar.Simulation/RigidBodyMotionType.html">RigidBodyMotionType</a> motionType, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>motionType</i>
              </dt>
              <dd>The motion type.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetMotionType(Sansar.Simulation.RigidBodyMotionType,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion)">SetOrientation Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion):member">
          <div class="msummary">Sets the orientation of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetOrientation</b> (<a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> orientation)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion):Parameters">
            <dl>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector)">SetOrientation Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector):member">
          <div class="msummary">Sets the orientation of this $className$.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Use SetOrientation(Quaternion)", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetOrientation</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> orientation)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector):Remarks">Deprecated. Use SetOrientation(Quaternion) instead.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,Sansar.Script.ScriptBase.OperationComplete)">SetOrientation Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the orientation of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetOrientation</b> (<a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> orientation, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,System.Action{Sansar.Script.OperationCompleteEvent})">SetOrientation Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the orientation of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetOrientation</b> (<a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> orientation, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Quaternion,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">SetOrientation Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the orientation of this $className$.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Use SetOrientation(Quaternion)", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetOrientation</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> orientation, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Remarks">Deprecated. Use SetOrientation(Quaternion) instead.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetOrientation Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the orientation of this $className$.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Use SetOrientation(Quaternion)", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetOrientation</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> orientation, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">Deprecated. Use SetOrientation(Quaternion) instead.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetOrientation(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector)">SetPosition Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector):member">
          <div class="msummary">Sets the position of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetPosition</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> position)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>position</i>
              </dt>
              <dd>The position.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">SetPosition Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the position of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetPosition</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> position, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>position</i>
              </dt>
              <dd>The position.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetPosition Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the position of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetPosition</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> position, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>position</i>
              </dt>
              <dd>The position.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single)">SetStaticFriction Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single):member">
          <div class="msummary">Sets the static friction of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetStaticFriction</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> staticFriction)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single):Parameters">
            <dl>
              <dt>
                <i>staticFriction</i>
              </dt>
              <dd>The static friction. Value should be between 0 and 1 (inclusive)</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetStaticFriction Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the static friction of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetStaticFriction</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> staticFriction, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>staticFriction</i>
              </dt>
              <dd>The static friction. Value should be between 0 and 1 (inclusive)</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete):Remarks">This asynchronous method queues the write then returns.
             To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetStaticFriction Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the static friction of this RigidBodyComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetStaticFriction</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> staticFriction, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>staticFriction</i>
              </dt>
              <dd>The static friction. Value should be between 0 and 1 (inclusive)</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.
            To block until the write has occurred, pass this method to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> while in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SetStaticFriction(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Simulation.RigidBodyComponent.SubscriptionHandler,System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Simulation.RigidBodyComponent.SubscriptionHandler,System.Boolean):member">
          <div class="msummary">Subscribes to Collision Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Use subscription callbacks of type Action&lt;Sansar.Simulation.CollisionData&gt;", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Subscribe</b> (<a href="../Sansar.Simulation/CollisionEventType.html">CollisionEventType</a> EventType, <a href="../Sansar.Simulation/RigidBodyComponent+SubscriptionHandler.html">RigidBodyComponent.SubscriptionHandler</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Simulation.RigidBodyComponent.SubscriptionHandler,System.Boolean):Parameters">
            <dl>
              <dt>
                <i>EventType</i>
              </dt>
              <dd> The type of collision which occurred.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Simulation.RigidBodyComponent.SubscriptionHandler,System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Simulation.RigidBodyComponent.SubscriptionHandler,System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,System.Action{Sansar.Simulation.CollisionData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,System.Action{Sansar.Simulation.CollisionData},System.Boolean):member">
          <div class="msummary">Subscribes to Collision Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="../Sansar.Simulation/CollisionEventType.html">CollisionEventType</a> EventType, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;CollisionData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,System.Action{Sansar.Simulation.CollisionData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/CollisionData.html">CollisionData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,System.Action{Sansar.Simulation.CollisionData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>EventType</i>
              </dt>
              <dd> The type of collision which occurred.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,System.Action{Sansar.Simulation.CollisionData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,System.Action{Sansar.Simulation.CollisionData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,System.Action{Sansar.Simulation.CollisionData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,Sansar.Simulation.RigidBodyComponent.SubscriptionHandler,System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,Sansar.Simulation.RigidBodyComponent.SubscriptionHandler,System.Boolean):member">
          <div class="msummary">Subscribes to Collision Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Use subscription callbacks of type Action&lt;Sansar.Simulation.CollisionData&gt;", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Subscribe</b> (<a href="../Sansar.Simulation/CollisionEventType.html">CollisionEventType</a> EventType, <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a> HitComponentId, <a href="../Sansar.Simulation/RigidBodyComponent+SubscriptionHandler.html">RigidBodyComponent.SubscriptionHandler</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,Sansar.Simulation.RigidBodyComponent.SubscriptionHandler,System.Boolean):Parameters">
            <dl>
              <dt>
                <i>EventType</i>
              </dt>
              <dd> The type of collision which occurred.</dd>
              <dt>
                <i>HitComponentId</i>
              </dt>
              <dd> The id of the rigid body component or character that was hit.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,Sansar.Simulation.RigidBodyComponent.SubscriptionHandler,System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,Sansar.Simulation.RigidBodyComponent.SubscriptionHandler,System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,System.Action{Sansar.Simulation.CollisionData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,System.Action{Sansar.Simulation.CollisionData},System.Boolean):member">
          <div class="msummary">Subscribes to Collision Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="../Sansar.Simulation/CollisionEventType.html">CollisionEventType</a> EventType, <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a> HitComponentId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;CollisionData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,System.Action{Sansar.Simulation.CollisionData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/CollisionData.html">CollisionData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,System.Action{Sansar.Simulation.CollisionData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>EventType</i>
              </dt>
              <dd> The type of collision which occurred.</dd>
              <dt>
                <i>HitComponentId</i>
              </dt>
              <dd> The id of the rigid body component or character that was hit.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,System.Action{Sansar.Simulation.CollisionData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,System.Action{Sansar.Simulation.CollisionData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.Subscribe(Sansar.Simulation.CollisionEventType,Sansar.Script.ComponentId,System.Action{Sansar.Simulation.CollisionData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToHeldObject(Sansar.Simulation.HeldObjectEventType,System.Action{Sansar.Simulation.HeldObjectData},System.Boolean)">SubscribeToHeldObject Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToHeldObject(Sansar.Simulation.HeldObjectEventType,System.Action{Sansar.Simulation.HeldObjectData},System.Boolean):member">
          <div class="msummary">Subscribes to HeldObject Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>SubscribeToHeldObject</b> (<a href="../Sansar.Simulation/HeldObjectEventType.html">HeldObjectEventType</a> HeldObjectEvent, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;HeldObjectData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToHeldObject(Sansar.Simulation.HeldObjectEventType,System.Action{Sansar.Simulation.HeldObjectData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/HeldObjectData.html">HeldObjectData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToHeldObject(Sansar.Simulation.HeldObjectEventType,System.Action{Sansar.Simulation.HeldObjectData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>HeldObjectEvent</i>
              </dt>
              <dd> The type of held object event which occurred.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToHeldObject(Sansar.Simulation.HeldObjectEventType,System.Action{Sansar.Simulation.HeldObjectData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToHeldObject(Sansar.Simulation.HeldObjectEventType,System.Action{Sansar.Simulation.HeldObjectData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToHeldObject(Sansar.Simulation.HeldObjectEventType,System.Action{Sansar.Simulation.HeldObjectData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToSitObject(Sansar.Simulation.SitEventType,System.Action{Sansar.Simulation.SitObjectData},System.Boolean)">SubscribeToSitObject Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToSitObject(Sansar.Simulation.SitEventType,System.Action{Sansar.Simulation.SitObjectData},System.Boolean):member">
          <div class="msummary">Subscribes to SitObject Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>SubscribeToSitObject</b> (<a href="../Sansar.Simulation/SitEventType.html">SitEventType</a> SitObjectEvent, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;SitObjectData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToSitObject(Sansar.Simulation.SitEventType,System.Action{Sansar.Simulation.SitObjectData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/SitObjectData.html">SitObjectData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToSitObject(Sansar.Simulation.SitEventType,System.Action{Sansar.Simulation.SitObjectData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>SitObjectEvent</i>
              </dt>
              <dd> The type of sit event which occurred.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToSitObject(Sansar.Simulation.SitEventType,System.Action{Sansar.Simulation.SitObjectData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToSitObject(Sansar.Simulation.SitEventType,System.Action{Sansar.Simulation.SitObjectData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.SubscribeToSitObject(Sansar.Simulation.SitEventType,System.Action{Sansar.Simulation.SitObjectData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.RigidBodyComponent.ToString()">ToString Method</h3>
        <blockquote id="M:Sansar.Simulation.RigidBodyComponent.ToString():member">
          <div class="msummary"> A string representation of this object. </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.RigidBodyComponent.ToString():Returns">A string representation of this object.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.ToString():Remarks">The format of this string may change between releases.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.RigidBodyComponent.ToString():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>