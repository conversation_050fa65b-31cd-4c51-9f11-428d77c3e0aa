<html>
  <head>
    <title>Sansar.Simulation.RigidBodyComponent.SubscriptionHandler</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.RigidBodyComponent.SubscriptionHandler">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.RigidBodyComponent.SubscriptionHandler:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.RigidBodyComponent.SubscriptionHandler:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.RigidBodyComponent.SubscriptionHandler:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.RigidBodyComponent.SubscriptionHandler">RigidBodyComponent.SubscriptionHandler  Delegate</h1>
    <p class="Summary" id="T:Sansar.Simulation.RigidBodyComponent.SubscriptionHandler:Summary">Handler for rigid body events.</p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.RigidBodyComponent.SubscriptionHandler:Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Use subscription callbacks of type Action&lt;Sansar.Simulation.CollisionData&gt;", false)]<br />public delegate <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>RigidBodyComponent.SubscriptionHandler</b> (<a href="../Sansar.Simulation/CollisionEventType.html">CollisionEventType</a> EventType, <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a> ComponentId, <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a> HitComponentId, <a href="../Sansar.Simulation/ObjectPublic.html">ObjectPublic</a> HitObject, <a href="../Sansar.Simulation/CollisionEventPhase.html">CollisionEventPhase</a> Phase, <a href="../Sansar.Simulation/ControlPointType.html">ControlPointType</a> HitControlPoint)</div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.RigidBodyComponent.SubscriptionHandler:Docs">
      <h4 class="Subsection">Parameters</h4>
      <blockquote class="SubsectionBox" id="T:Sansar.Simulation.RigidBodyComponent.SubscriptionHandler:Docs:Parameters">
        <dl>
          <dt>
            <i>EventType</i>
          </dt>
          <dd> The type of collision which occurred.</dd>
          <dt>
            <i>ComponentId</i>
          </dt>
          <dd> The id of the rigid body component.</dd>
          <dt>
            <i>HitComponentId</i>
          </dt>
          <dd> The id of the rigid body component or character that was hit.</dd>
          <dt>
            <i>HitObject</i>
          </dt>
          <dd> The <a href="../Sansar.Simulation/ObjectPublic.html">Sansar.Simulation.ObjectPublic</a> that was hit.</dd>
          <dt>
            <i>Phase</i>
          </dt>
          <dd> used by trigger events to determine if trigger enter or exit occurred</dd>
          <dt>
            <i>HitControlPoint</i>
          </dt>
          <dd> The specific control point hit, if any</dd>
        </dl>
      </blockquote>
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.RigidBodyComponent.SubscriptionHandler:Docs:Remarks">
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.RigidBodyComponent.SubscriptionHandler:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.RigidBodyComponent.SubscriptionHandler:Members">
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>