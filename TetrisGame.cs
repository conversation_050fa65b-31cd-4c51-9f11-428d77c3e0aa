using Sansar;
using Sansar.Script;
using Sansar.Simulation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Diagnostics;

/// <summary>
/// Unified Tetris Game Script - Single script combining all game functionality
/// Place this script on the Tetris seat object in your scene
/// </summary>
public class TetrisGame : SceneObjectScript
{
    #region Editor Properties
    
    [Tooltip("Generic block to spawn for each grid position")]
    public ClusterResource GenericBlock;
    
    [Toolt<PERSON>("Grid origin point in world space")]
    public Sansar.Vector GridOrigin;
    
    [Tooltip("Spawn blocks in batches to prevent throttling")]
    [DefaultValue(10)]
    public int BatchSize = 10;
    
    [Tooltip("Delay between batches in seconds")]
    [DefaultValue(0.1f)]
    public float BatchDelay = 0.1f;
    
    [Tooltip("Enable rainbow debug mode for testing material control")]
    [DefaultValue(false)]
    public bool DebugRainbowMode = false;
    
    #endregion
    
    #region Game Constants and Enums
    
    public const int GRID_WIDTH = 10;
    public const int GRID_HEIGHT = 20;
    public const float BLOCK_SIZE = 1.0f;
    
    public enum PieceType
    {
        I, O, T, S, Z, J, L
    }
    
    public enum GameState
    {
        WaitingForPlayer,
        WaitingForGrid,
        Playing,
        GameOver
    }
    
    public enum FlashType
    {
        None = 0,
        LineClear = 1,
        GameOver = 2
    }
    
    #endregion
    
    #region Block State Structure
    
    /// <summary>
    /// Comprehensive block state with history tracking and edge detection
    /// </summary>
    public struct BlockState
    {
        // Current state
        public int? PieceType;
        public bool IsVisible;
        public bool IsActivePiece;
        public FlashType FlashMode;
        public float FlashRate;
        
        // Previous state (for edge detection)
        public int? PreviousPieceType;
        public bool PreviousIsVisible;
        public FlashType PreviousFlashMode;
        
        // Timestamps
        public float StateChangeTime;
        public float FlashStartTime;
        public float VisibilityChangeTime;
        
        // Edge detection helpers
        public bool HasPieceTypeChanged()
        {
            return PieceType != PreviousPieceType;
        }
        
        public bool JustBecameVisible()
        {
            return IsVisible && !PreviousIsVisible;
        }
        
        public bool JustBecameHidden()
        {
            return !IsVisible && PreviousIsVisible;
        }
        
        public bool JustStartedFlashing()
        {
            return FlashMode != FlashType.None && PreviousFlashMode == FlashType.None;
        }
        
        public bool JustStoppedFlashing()
        {
            return FlashMode == FlashType.None && PreviousFlashMode != FlashType.None;
        }
        
        // Update state and track history
        public void UpdateHistory(float currentTime)
        {
            // Track timestamp changes
            if (PieceType != PreviousPieceType)
                StateChangeTime = currentTime;
            if (IsVisible != PreviousIsVisible)
                VisibilityChangeTime = currentTime;
            if (FlashMode != PreviousFlashMode && FlashMode != FlashType.None)
                FlashStartTime = currentTime;
                
            // Update history
            PreviousPieceType = PieceType;
            PreviousIsVisible = IsVisible;
            PreviousFlashMode = FlashMode;
        }
        
        // Helper to get time since state changes
        public float GetTimeSinceFlashStart(float currentTime)
        {
            return currentTime - FlashStartTime;
        }
        
        public float GetTimeSinceStateChange(float currentTime)
        {
            return currentTime - StateChangeTime;
        }
    }
    
    #endregion
    
    #region Game State Variables
    
    private GameState currentState = GameState.WaitingForPlayer;
    private AgentPrivate currentPlayer = null;
    
    // Game scoring
    private int score = 0;
    private int level = 1;
    private int linesCleared = 0;
    private float dropInterval = 1.0f;
    
    // Current active piece state
    private PieceType currentPieceType;
    private Vector currentPiecePosition;
    private int currentPieceRotation = 0;
    private Vector[] currentPieceBlocks;
    
    private long lastDropTime = Stopwatch.GetTimestamp();
    
    // Pre-initialized Random to avoid blocking on entropy
    private Random randomGenerator = new Random();
    
    #endregion
    
    #region Grid Management Variables
    
    // Unified block state array - single source of truth for all block properties
    private BlockState[,] blockStates = new BlockState[GRID_WIDTH, GRID_HEIGHT];
    
    // Object references to spawned blocks for direct control
    private ObjectPrivate[,] blockObjects = new ObjectPrivate[GRID_WIDTH, GRID_HEIGHT];
    
    // Mesh component references for direct material control
    private MeshComponent[,] blockMeshes = new MeshComponent[GRID_WIDTH, GRID_HEIGHT];
    
    // Background grid - black blocks that stay visible for visual reference
    private ObjectPrivate[,] backgroundBlockObjects = new ObjectPrivate[GRID_WIDTH, GRID_HEIGHT];
    private MeshComponent[,] backgroundBlockMeshes = new MeshComponent[GRID_WIDTH, GRID_HEIGHT];
    
    // Spawned cluster references for cleanup
    private List<Cluster> spawnedClusters = new List<Cluster>();
    private List<Cluster> spawnedBackgroundClusters = new List<Cluster>();
    
    // Spawn tracking
    private int blocksSpawned = 0;
    private int backgroundBlocksSpawned = 0;
    private int totalBlocksToSpawn = GRID_WIDTH * GRID_HEIGHT;
    private int totalBackgroundBlocksToSpawn = GRID_WIDTH * GRID_HEIGHT;
    private bool gridInitialized = false;
    
    #endregion
    
    #region Seat Detection Variables
    
    private RigidBodyComponent rigidBody;
    private IEventSubscription sitSubscription;
    private IEventSubscription standSubscription;
    
    // Command subscriptions for player input (expanded for press/release pairs)
    private IEventSubscription[] commandSubscriptions = new IEventSubscription[10];
    
    // Key repeat system for smooth movement
    private bool leftKeyHeld = false;
    private bool rightKeyHeld = false;
    private bool softDropKeyHeld = false;
    private int leftKeyRepeatCount = 0;
    private int rightKeyRepeatCount = 0;
    private int softDropKeyRepeatCount = 0;
    private const int KEY_INITIAL_DELAY_CYCLES = 3;  // 3 cycles (300ms) before repeat starts
    private const int KEY_REPEAT_RATE_CYCLES = 1;    // 1 cycle (100ms) between repeats
    
    #endregion
        
    #region Piece Shape Definitions
    
    private static readonly Dictionary<PieceType, Vector[][]> PieceShapes = new Dictionary<PieceType, Vector[][]>
    {
        {PieceType.I, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(2, 0, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(0, 2, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(2, 0, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(0, 2, 0) }
        }},
        
        {PieceType.O, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) }
        }},
        
        {PieceType.T, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(1, 0, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(0, -1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(-1, 0, 0) }
        }},
        
        {PieceType.S, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(-1, 0, 0), new Vector(-1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(-1, 0, 0), new Vector(-1, 1, 0) }
        }},
        
        {PieceType.Z, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(-1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(1, 0, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(-1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(1, 0, 0), new Vector(1, 1, 0) }
        }},
        
        {PieceType.J, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(-1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(1, -1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(-1, -1, 0) }
        }},
        
        {PieceType.L, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(1, -1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(-1, -1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(-1, 1, 0) }
        }}
    };
    
    #endregion
    
    #region Color Definitions
    
    // Tetris piece colors
    private static readonly Dictionary<int, Sansar.Color> PieceColors = CreatePieceColors();
    
    private static Dictionary<int, Sansar.Color> CreatePieceColors()
    {
        var colors = new Dictionary<int, Sansar.Color>();
        colors.Add(0, new Sansar.Color(0.0f, 1.0f, 1.0f, 1.0f)); // I - Cyan
        colors.Add(1, new Sansar.Color(1.0f, 1.0f, 0.0f, 1.0f)); // O - Yellow
        colors.Add(2, new Sansar.Color(0.5f, 0.0f, 1.0f, 1.0f)); // T - Purple
        colors.Add(3, new Sansar.Color(0.0f, 1.0f, 0.0f, 1.0f)); // S - Green
        colors.Add(4, new Sansar.Color(1.0f, 0.0f, 0.0f, 1.0f)); // Z - Red
        colors.Add(5, new Sansar.Color(0.0f, 0.0f, 1.0f, 1.0f)); // J - Blue
        colors.Add(6, new Sansar.Color(1.0f, 0.5f, 0.0f, 1.0f)); // L - Orange
        return colors;
    }
    
    // Hidden color (completely transparent)
    private static readonly Sansar.Color HiddenColor = new Sansar.Color(0.0f, 0.0f, 0.0f, 0.0f);
    
    // Background grid color (solid black)
    private static readonly Sansar.Color BackgroundColor = new Sansar.Color(0.1f, 0.1f, 0.1f, 1.0f);
    
    #endregion
    
    #region Initialization
    
    public override void Init()
    {
        Log.Write("TetrisGame V2 - Unified Single Script Architecture");
        
        // Initialize grid state
        InitializeGridState();
        
        // Initialize seat detection
        InitializeSeatDetection();
        
        // Validate resources
        if (GenericBlock == null)
        {
            Log.Write(LogLevel.Error, "TetrisGame: GenericBlock resource not set");
            return;
        }
        
        // Start spawning background grid first, then main grid blocks
        StartCoroutine(SpawnBackgroundGrid);
        StartCoroutine(SpawnGridBlocks);
        
        // Start main game loop
        StartCoroutine(GameLoop);
        
        Log.Write(string.Format("TetrisGame initialized - will spawn {0} background blocks and {1} game blocks", totalBackgroundBlocksToSpawn, totalBlocksToSpawn));
        Log.Write(string.Format("TetrisGame: GridOrigin set to: {0}", GridOrigin));
    }
    
    private void InitializeSeatDetection()
    {
        if (!ObjectPrivate.TryGetFirstComponent(out rigidBody))
        {
            Log.Write(LogLevel.Error, "TetrisGame: No RigidBodyComponent found on seat object");
            return;
        }
        
        // Subscribe to sit/stand events using Sansar's native API
        sitSubscription = rigidBody.SubscribeToSitObject(SitEventType.Start, OnPlayerSitDown);
        standSubscription = rigidBody.SubscribeToSitObject(SitEventType.End, OnPlayerStandUp);
        
        // Player input handled via SubscribeToScriptEvent - no additional components needed
        
        Log.Write("TetrisGame: Seat detection and interaction initialized");
    }
    
    private void InitializeGridState()
    {
        // Initialize empty grid with default block states
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                // Initialize block state to empty/default values
                blockStates[x, y] = new BlockState
                {
                    PieceType = null,
                    IsVisible = false,
                    IsActivePiece = false,
                    FlashMode = FlashType.None,
                    FlashRate = 2.0f, // Default flash rate
                    PreviousPieceType = null,
                    PreviousIsVisible = false,
                    PreviousFlashMode = FlashType.None,
                    StateChangeTime = 0f,
                    FlashStartTime = 0f,
                    VisibilityChangeTime = 0f
                };
                
                blockObjects[x, y] = null;
                blockMeshes[x, y] = null;
                backgroundBlockObjects[x, y] = null;
                backgroundBlockMeshes[x, y] = null;
            }
        }
        
        Log.Write("TetrisGame: Grid state initialized for new game");
    }
    
    private void ResetGameVariables()
    {
        // Reset block states while preserving object references
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);
        
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                // Reset block state but preserve the history for edge detection
                BlockState block = blockStates[x, y];
                block.UpdateHistory(currentTime); // Save current state as previous
                
                // Clear current state
                block.PieceType = null;
                block.IsVisible = false;
                block.IsActivePiece = false;
                block.FlashMode = FlashType.None;
                
                blockStates[x, y] = block;
            }
        }
        
        // Force clear spawn area to prevent stuck pieces (top 4 rows)
        Log.Write("TetrisGame: Force clearing spawn area (Y=16-19) for clean restart");
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = GRID_HEIGHT - 4; y < GRID_HEIGHT; y++)
            {
                // Ensure spawn area is completely clear
                BlockState block = blockStates[x, y];
                block.PieceType = null;
                block.IsVisible = false;
                blockStates[x, y] = block;
                
                // Also ensure visual is hidden
                if (gridInitialized)
                {
                    SetBlockHidden(x, y);
                }
            }
        }
        
        // Reset current piece state
        currentPieceBlocks = null;
        currentPieceRotation = 0;
        
        // Reset drop timer to current time
        lastDropTime = Stopwatch.GetTimestamp();
        
        // Reset key repeat states
        leftKeyHeld = false;
        rightKeyHeld = false;
        softDropKeyHeld = false;
        leftKeyRepeatCount = 0;
        rightKeyRepeatCount = 0;
        softDropKeyRepeatCount = 0;
        
        Log.Write("TetrisGame: Game variables reset for clean restart");
    }
    
    #endregion
    
    #region Player Session Management
    
    private void OnPlayerSitDown(SitObjectData data)
    {
        // Player sat down - start game session
        Log.Write(string.Format("TetrisGame: Player sat down - previous state: {0}", currentState));
        
        // Get the player who sat down
        currentPlayer = ScenePrivate.FindAgent(data.ObjectId);
        
        Log.Write(string.Format("TetrisGame: Grid status - blocksSpawned: {0}/{1}, gridInitialized: {2}", blocksSpawned, totalBlocksToSpawn, gridInitialized));
        
        if (gridInitialized)
        {
            Log.Write(string.Format("TetrisGame: Grid ready - current state is {0}, starting new game", currentState));
            StartNewGame();
        }
        else
        {
            currentState = GameState.WaitingForGrid;
            Log.Write("TetrisGame: Grid not ready yet - waiting for grid initialization");
        }
    }
    
    private void OnPlayerStandUp(SitObjectData data)
    {
        // Player stood up - end game session
        Log.Write(string.Format("TetrisGame: Player stood up - current state: {0}", currentState));
        
        // Reset key repeat states
        leftKeyHeld = false;
        rightKeyHeld = false;
        softDropKeyHeld = false;
        leftKeyRepeatCount = 0;
        rightKeyRepeatCount = 0;
        softDropKeyRepeatCount = 0;
        
        // Unsubscribe from all commands
        UnsubscribeFromPlayerControls();
        
        Log.Write("TetrisGame: Calling EndGame() due to player stand up");
        EndGame();
        currentPlayer = null;
        
        Log.Write("TetrisGame: Player session cleanup complete");
    }
    
    private void UnsubscribeFromPlayerControls()
    {
        for (int i = 0; i < commandSubscriptions.Length; i++)
        {
            if (commandSubscriptions[i] != null)
            {
                commandSubscriptions[i].Unsubscribe();
                commandSubscriptions[i] = null;
            }
        }
        Log.Write("TetrisGame: Unsubscribed from all player commands");
    }
    
    #endregion
    
    #region Background Grid Spawning
    
    private void SpawnBackgroundGrid()
    {
        Log.Write("TetrisGame: Starting background grid spawning...");
        
        // Spawn background blocks in batches to prevent throttling
        for (int batchStart = 0; batchStart < totalBackgroundBlocksToSpawn; batchStart += BatchSize)
        {
            SpawnBackgroundBatch(batchStart);
            
            // Wait between batches
            if (batchStart + BatchSize < totalBackgroundBlocksToSpawn)
            {
                Wait(TimeSpan.FromSeconds(BatchDelay));
            }
        }
    }
    
    private void SpawnBackgroundBatch(int startIndex)
    {
        int endIndex = Math.Min(startIndex + BatchSize, totalBackgroundBlocksToSpawn);
        
        for (int i = startIndex; i < endIndex; i++)
        {
            int x = i % GRID_WIDTH;
            int y = i / GRID_WIDTH;
            
            SpawnBackgroundBlockAt(x, y);
        }
        
        Log.Write(string.Format("TetrisGame: Spawned background batch: blocks {0} to {1}", startIndex, endIndex - 1));
    }
    
    private void SpawnBackgroundBlockAt(int gridX, int gridY)
    {
        Sansar.Vector worldPosition = GridToWorldBackground(gridX, gridY);
        
        try
        {
            ScenePrivate.CreateCluster(GenericBlock, worldPosition, Sansar.Quaternion.Identity, Sansar.Vector.Zero,
                (ScenePrivate.CreateClusterData data) =>
                {
                    if (data.Success && data.ClusterReference != null)
                    {
                        OnBackgroundBlockSpawned(data.ClusterReference, gridX, gridY);
                    }
                    else
                    {
                        Log.Write(LogLevel.Error, string.Format("TetrisGame: Failed to spawn background block at ({0}, {1}): {2}", gridX, gridY, data.Message));
                    }
                });
        }
        catch (ThrottleException)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Throttled background block at ({0}, {1}) - retrying in 0.5 seconds", gridX, gridY));
            StartCoroutine(() => RetrySpawnBackgroundBlock(gridX, gridY));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Exception spawning background block at ({0}, {1}): {2}", gridX, gridY, ex.Message));
        }
    }
    
    private void RetrySpawnBackgroundBlock(int gridX, int gridY)
    {
        Wait(TimeSpan.FromSeconds(0.5));
        SpawnBackgroundBlockAt(gridX, gridY);
    }
    
    private void OnBackgroundBlockSpawned(Cluster cluster, int gridX, int gridY)
    {
        // Store cluster reference for cleanup
        spawnedBackgroundClusters.Add(cluster);
        
        // Store object and mesh references for direct control
        foreach (var objectPrivate in cluster.GetObjectPrivates())
        {
            // Store object reference
            backgroundBlockObjects[gridX, gridY] = objectPrivate;
            
            // Get and store mesh component for direct material control
            MeshComponent mesh;
            if (objectPrivate.TryGetFirstComponent(out mesh))
            {
                backgroundBlockMeshes[gridX, gridY] = mesh;
                
                // Check if mesh is scriptable and set to black background color
                if (mesh.IsScriptable)
                {
                    SetBackgroundBlockVisible(gridX, gridY);
                }
                else
                {
                    Log.Write(LogLevel.Error, string.Format("TetrisGame: Background block ({0}, {1}): Mesh is not scriptable - cannot control materials", gridX, gridY));
                }
            }
            else
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Background block ({0}, {1}): No MeshComponent found", gridX, gridY));
            }
            
            break; // Only need the first object
        }
        
        backgroundBlocksSpawned++;
        
    }
    
    
    // Convert grid coordinates to world position for background blocks (offset to be visible)
    private Sansar.Vector GridToWorldBackground(int gridX, int gridY)
    {
        // According to the comments: gridX->X (left/right), gridY->Z (up/down), Y is forward/back
        // For background blocks directly behind each game block, use same X,Z but different Y
        return new Sansar.Vector(
            GridOrigin.X + (gridX * BLOCK_SIZE),     // Same X position as game blocks
            GridOrigin.Y + 1.0f,                     // Try positive Y offset (forward instead of back)
            GridOrigin.Z + (gridY * BLOCK_SIZE)      // Same Z position as game blocks
        );
    }
    
    public void SetBackgroundBlockVisible(int gridX, int gridY)
    {
        if (gridX < 0 || gridX >= GRID_WIDTH || gridY < 0 || gridY >= GRID_HEIGHT)
            return;
        
        MeshComponent mesh = backgroundBlockMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            try
            {
                // Make the background block visible with black color
                mesh.SetIsVisible(true);
                
                foreach (var material in mesh.GetRenderMaterials())
                {
                    var props = material.GetProperties();
                    
                    if (material.HasTint)
                    {
                        props.Tint = BackgroundColor;
                    }
                    
                    if (material.HasEmissiveIntensity)
                    {
                        props.EmissiveIntensity = 0.0f; // No glow for background
                    }
                    
                    material.SetProperties(props);
                }
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Error showing background block ({0}, {1}): {2}", gridX, gridY, ex.Message));
            }
        }
    }
    
    #endregion
    
    #region Grid Spawning and Management
    
    private void SpawnGridBlocks()
    {
        Log.Write("TetrisGame: Starting grid block spawning...");
        
        // Spawn blocks in batches to prevent throttling
        for (int batchStart = 0; batchStart < totalBlocksToSpawn; batchStart += BatchSize)
        {
            SpawnBatch(batchStart);
            
            // Wait between batches
            if (batchStart + BatchSize < totalBlocksToSpawn)
            {
                Wait(TimeSpan.FromSeconds(BatchDelay));
            }
        }
    }
    
    private void SpawnBatch(int startIndex)
    {
        int endIndex = Math.Min(startIndex + BatchSize, totalBlocksToSpawn);
        
        for (int i = startIndex; i < endIndex; i++)
        {
            int x = i % GRID_WIDTH;
            int y = i / GRID_WIDTH;
            
            SpawnBlockAt(x, y);
        }
        
        Log.Write(string.Format("TetrisGame: Spawned batch: blocks {0} to {1}", startIndex, endIndex - 1));
    }
    
    private void SpawnBlockAt(int gridX, int gridY)
    {
        Sansar.Vector worldPosition = GridToWorld(gridX, gridY);
        
        try
        {
            ScenePrivate.CreateCluster(GenericBlock, worldPosition, Sansar.Quaternion.Identity, Sansar.Vector.Zero,
                (ScenePrivate.CreateClusterData data) =>
                {
                    if (data.Success && data.ClusterReference != null)
                    {
                        OnBlockSpawned(data.ClusterReference, gridX, gridY);
                    }
                    else
                    {
                        Log.Write(LogLevel.Error, string.Format("TetrisGame: Failed to spawn block at ({0}, {1}): {2}", gridX, gridY, data.Message));
                    }
                });
        }
        catch (ThrottleException)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Throttled at ({0}, {1}) - retrying in 0.5 seconds", gridX, gridY));
            StartCoroutine(() => RetrySpawnBlock(gridX, gridY));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Exception spawning block at ({0}, {1}): {2}", gridX, gridY, ex.Message));
        }
    }
    
    private void RetrySpawnBlock(int gridX, int gridY)
    {
        Wait(TimeSpan.FromSeconds(0.5));
        SpawnBlockAt(gridX, gridY);
    }
    
    private void OnBlockSpawned(Cluster cluster, int gridX, int gridY)
    {
        // Store cluster reference for cleanup
        spawnedClusters.Add(cluster);
        
        // Store object and mesh references for direct control
        foreach (var objectPrivate in cluster.GetObjectPrivates())
        {
            // Store object reference
            blockObjects[gridX, gridY] = objectPrivate;
            
            // Get and store mesh component for direct material control
            MeshComponent mesh;
            if (objectPrivate.TryGetFirstComponent(out mesh))
            {
                blockMeshes[gridX, gridY] = mesh;
                
                // Check if mesh is scriptable and log material capabilities
                if (mesh.IsScriptable)
                {
                    var materialsEnum = mesh.GetRenderMaterials();
                    var materials = new List<RenderMaterial>();
                    foreach (var mat in materialsEnum)
                    {
                        materials.Add(mat);
                    }
                    
                    // Set initial hidden state directly
                    SetBlockHidden(gridX, gridY);
                    Log.Write(string.Format("TetrisGame: Block ({0}, {1}): Successfully created scriptable mesh with {2} materials", gridX, gridY, materials.Count));
                }
                else
                {
                    Log.Write(LogLevel.Error, string.Format("TetrisGame: Block ({0}, {1}): Mesh is not scriptable - cannot control materials", gridX, gridY));
                }
            }
            else
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Block ({0}, {1}): No MeshComponent found", gridX, gridY));
            }
            
            break; // Only need the first object
        }
        
        blocksSpawned++;
        // Log.Write(string.Format("TetrisGame: Block spawned at ({0}, {1}) - progress: {2}/{3}", gridX, gridY, blocksSpawned, totalBlocksToSpawn));
        
        if (blocksSpawned >= totalBlocksToSpawn)
        {
            OnGridInitializationComplete();
        }
    }
    
    private void OnGridInitializationComplete()
    {
        gridInitialized = true;
        Log.Write("TetrisGame: Grid initialization complete - all blocks spawned and ready");
        
        // Start debug rainbow mode if enabled
        if (DebugRainbowMode)
        {
            Log.Write("TetrisGame: Starting rainbow debug mode to test material control");
            StartCoroutine(RainbowDebugSequence);
        }
        
        // Initial visual sync to set up clean board
        SyncAllVisualsToCompleteState();
        
        // If a player is waiting, start the game
        if (currentState == GameState.WaitingForGrid && currentPlayer != null)
        {
            StartNewGame();
        }
    }
    
    // Convert grid coordinates to world position
    private Sansar.Vector GridToWorld(int gridX, int gridY)
    {
        // Map grid coordinates to world space for vertical Tetris layout:
        // gridX -> world X (left/right)
        // gridY -> world Z (up/down) 
        // world Y stays 0 (forward/back - no change)
        return new Sansar.Vector(
            GridOrigin.X + (gridX * BLOCK_SIZE),
            GridOrigin.Y,
            GridOrigin.Z + (gridY * BLOCK_SIZE)
        );
    }
    
    #endregion
    
    #region Direct Material Control Methods
    
    public void SetBlockHidden(int gridX, int gridY)
    {
        if (gridX < 0 || gridX >= GRID_WIDTH || gridY < 0 || gridY >= GRID_HEIGHT)
            return;
        
        MeshComponent mesh = blockMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            try
            {
                // Use proper Sansar visibility control - makes block completely invisible
                mesh.SetIsVisible(false);
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Error hiding block ({0}, {1}): {2}", gridX, gridY, ex.Message));
            }
        }
    }
    
    public void SetBlockVisible(int gridX, int gridY, int pieceType, bool isActivePiece = false)
    {
        if (gridX < 0 || gridX >= GRID_WIDTH || gridY < 0 || gridY >= GRID_HEIGHT)
            return;
        
        if (!PieceColors.ContainsKey(pieceType))
            return;
        
        MeshComponent mesh = blockMeshes[gridX, gridY];
        if (mesh == null)
        {
            if (isActivePiece)
            {
                Log.Write(LogLevel.Warning, string.Format("TetrisGame: Block ({0},{1}) mesh reference is null in blockMeshes array", gridX, gridY));
            }
            return;
        }
        
        if (!mesh.IsScriptable)
        {
            if (isActivePiece)
            {
                Log.Write(LogLevel.Warning, string.Format("TetrisGame: Block ({0},{1}) mesh is not scriptable", gridX, gridY));
            }
            return;
        }
        
        try
        {
            // First make the block visible
            mesh.SetIsVisible(true);
            
            // Then set its color and properties
            Sansar.Color blockColor = PieceColors[pieceType];
            
            foreach (var material in mesh.GetRenderMaterials())
            {
                var props = material.GetProperties();
                
                if (material.HasTint)
                {
                    props.Tint = blockColor;
                }
                
                if (material.HasEmissiveIntensity)
                {
                    props.EmissiveIntensity = isActivePiece ? 2.0f : 0.0f;
                }
                
                material.SetProperties(props);
            }
            
            // Debug: Log successful material changes for active pieces
            // if (isActivePiece)
            // {
            //     Log.Write(string.Format("TetrisGame: Successfully set block ({0},{1}) visible with color {2}, emissive: {3}", 
            //         gridX, gridY, blockColor, isActivePiece ? 2.0f : 0.0f));
            // }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error showing block ({0}, {1}): {2}", gridX, gridY, ex.Message));
        }
    }
    
    public void FlashBlock(int gridX, int gridY, float duration = 0.5f)
    {
        if (gridX < 0 || gridX >= GRID_WIDTH || gridY < 0 || gridY >= GRID_HEIGHT)
            return;
        
        MeshComponent mesh = blockMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            try
            {
                foreach (var material in mesh.GetRenderMaterials())
                {
                    var props = material.GetProperties();
                    
                    if (material.HasTint)
                    {
                        props.Tint = Sansar.Color.White;
                    }
                    
                    if (material.HasEmissiveIntensity)
                    {
                        props.EmissiveIntensity = 15.0f;
                    }
                    
                    material.SetProperties(props);
                }
                
                // Return to hidden state after duration
                StartCoroutine(() => DelayedHideBlock(gridX, gridY, duration));
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Error flashing block ({0}, {1}): {2}", gridX, gridY, ex.Message));
            }
        }
    }
    
    private void DelayedHideBlock(int gridX, int gridY, float delay)
    {
        Wait(TimeSpan.FromSeconds(delay));
        SetBlockHidden(gridX, gridY);
    }
    
    #endregion
    
    #region Rainbow Debug Mode
    
    private void RainbowDebugSequence()
    {
        Log.Write("TetrisGame: Rainbow debug sequence starting - cycling through all colors and effects");
        
        // Phase 1: Show all blocks visible in different colors
        for (int colorCycle = 0; colorCycle < 7; colorCycle++) // 7 Tetris piece types
        {
            Log.Write(string.Format("TetrisGame: Rainbow cycle {0} - setting all blocks to color {1}", colorCycle + 1, colorCycle));
            
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                for (int y = 0; y < GRID_HEIGHT; y++)
                {
                    SetBlockVisible(x, y, colorCycle, false);
                }
            }
            
            Wait(TimeSpan.FromSeconds(1.0)); // Hold each color for 1 second
        }
        
        // Phase 2: Reset to hidden state
        Log.Write("TetrisGame: Rainbow debug complete - resetting all blocks to hidden");
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                SetBlockHidden(x, y);
            }
        }
        
        Log.Write("TetrisGame: Rainbow debug sequence completed - material control testing finished");
    }
    
    #endregion
    
    #region Grid State and Collision Detection
    
    public bool IsBlockOccupied(int x, int y)
    {
        return (blockStates[x, y].IsActivePiece);
    }

    public bool IsBlockInBounds(int x, int y)
    {
        return (x >= 0 && x < GRID_WIDTH && y >= 0 && y < GRID_HEIGHT);
    }
    
    public void SetBlock(int x, int y, int pieceType, bool isActivePiece = false)
    {
        if (IsBlockInBounds(x, y))
        {
            // Update block state
            BlockState block = blockStates[x, y];
            block.PieceType = pieceType;
            block.IsVisible = true;
            block.IsActivePiece = isActivePiece;
            blockStates[x, y] = block;
            
            // Use direct material control
            SetBlockVisible(x, y, pieceType, isActivePiece);
        }
    }
    
    public void ClearBlock(int x, int y)
    {
        if (IsBlockInBounds(x, y))
        {
            // Update block state
            BlockState block = blockStates[x, y];
            block.PieceType = null;
            block.IsVisible = false;
            block.IsActivePiece = false;
            block.FlashMode = FlashType.None;
            blockStates[x, y] = block;
            
            // Use direct material control
            SetBlockHidden(x, y);
        }
    }
    
    public void ClearGrid()
    {
        if (!gridInitialized)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Grid not initialized yet - cannot clear");
            return;
        }
        
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                ClearBlock(x, y);
            }
        }
        
        Log.Write("TetrisGame: Grid cleared for new game");
    }
    
    public bool IsGameOver()
    {
        if (!gridInitialized) return false;
        
        // Check if any blocks in the spawn area are occupied
        // Check top 4 rows (Y=16-19) to include spawn area and safety margin
        for (int y = GRID_HEIGHT - 4; y < GRID_HEIGHT; y++)
        {
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                if (blockStates[x, y].PieceType != null)
                {
                    Log.Write(string.Format("TetrisGame: Game over detected - block found at ({0}, {1})", x, y));
                    return true;
                }
            }
        }
        return false;
    }
    
    #endregion
    
    #region Piece Logic and Collision Detection
    
    // Get the block positions for a piece at a specific position and rotation
    private Vector[] GetPieceBlocks(PieceType pieceType, int rotation, Vector centerPosition)
    {
        rotation = rotation % 4;
        if (rotation < 0) rotation += 4;
        
        Vector[] relativePattern = PieceShapes[pieceType][rotation];
        Vector[] absolutePositions = new Vector[relativePattern.Length];
        
        for (int i = 0; i < relativePattern.Length; i++)
        {
            absolutePositions[i] = new Vector(
                centerPosition.X + relativePattern[i].X,
                centerPosition.Y + relativePattern[i].Y,
                centerPosition.Z + relativePattern[i].Z
            );
        }
        
        return absolutePositions;
    }
    
    // FIXED COLLISION DETECTION: Now has access to grid state!
    public bool CanPlacePiece(PieceType pieceType, int rotation, Vector position)
    {
        Vector[] blockPositions = GetPieceBlocks(pieceType, rotation, position);
        
        foreach (var blockPos in blockPositions)
        {
            int x = (int)blockPos.X;
            int y = (int)blockPos.Y;

            // Check if position is in bounds and not occupied by another piece
            if (IsBlockOccupied(x, y) || !IsBlockInBounds(x, y))
            {
                Log.Write(string.Format("TetrisGame: CanPlacePiece - IsBlockInBounds: {0}, IsBlockOccupied: {1}", IsBlockInBounds(x, y), IsBlockOccupied(x, y)));
                return false;
            }
        }
        
        return true;
    }
    
    public bool TryRotatePiece(PieceType pieceType, Vector currentPosition, int currentRotation, out int newRotation)
    {
        newRotation = (currentRotation + 1) % 4;
        
        // Try basic rotation
        if (CanPlacePiece(pieceType, newRotation, currentPosition))
        {
            return true;
        }
        
        // Try wall kicks (simple implementation)
        Vector[] wallKicks = { new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0) };
        
        foreach (var kick in wallKicks)
        {
            Vector kickPosition = new Vector(
                currentPosition.X + kick.X,
                currentPosition.Y + kick.Y,
                currentPosition.Z + kick.Z
            );
            if (CanPlacePiece(pieceType, newRotation, kickPosition))
            {
                return true;
            }
        }
        
        newRotation = currentRotation;
        return false;
    }
    
    #endregion
    
    #region Game Logic and Flow
    
    private void StartNewGame()
    {
        if (!gridInitialized)
        {
            Log.Write("TetrisGame: Cannot start game - grid not initialized");
            return;
        }
        
        currentState = GameState.Playing;
        score = 0;
        level = 1;
        linesCleared = 0;
        dropInterval = 1.0f;
        
        // Clear game over flash state is now handled by ResetGameVariables
        
        // Reset game variables (preserving block references) and clear visuals
        ResetGameVariables();
        ClearGrid();
        
        // Subscribe to player controls
        SubscribeToPlayerControls();
        
        // Spawn first piece
        SpawnNewPiece();
        
        Log.Write("TetrisGame: Game started");
    }
    
    private void SubscribeToPlayerControls()
    {
        Log.Write("TetrisGame: SubscribeToPlayerControls called");
        
        if (currentPlayer == null || !currentPlayer.IsValid)
        {
            Log.Write("TetrisGame: No valid player to subscribe to commands");
            return;
        }
        
        Log.Write("TetrisGame: Player validation passed, proceeding with subscriptions");
        
        try
        {
            Log.Write("TetrisGame: Starting command subscriptions");
            
            // Subscribe to player commands using the proper agent.Client.SubscribeToCommand pattern
            Log.Write("TetrisGame: Subscribing to Keypad4 (A key)");
            commandSubscriptions[0] = currentPlayer.Client.SubscribeToCommand("Keypad4", CommandAction.Pressed, HandleMoveLeftPressed, null);     // A key → Move left
            
            Log.Write("TetrisGame: Subscribing to Keypad6 (D key)");
            commandSubscriptions[1] = currentPlayer.Client.SubscribeToCommand("Keypad6", CommandAction.Pressed, HandleMoveRightPressed, null);    // D key → Move right  
            
            Log.Write("TetrisGame: Subscribing to Keypad2 (S key)");
            commandSubscriptions[2] = currentPlayer.Client.SubscribeToCommand("Keypad2", CommandAction.Pressed, HandleSoftDrop, null);     // S key → Soft drop
            
            Log.Write("TetrisGame: Subscribing to Keypad8 (W key)");
            commandSubscriptions[3] = currentPlayer.Client.SubscribeToCommand("Keypad8", CommandAction.Pressed, HandleSlowDown, null);     // W key → Slow down
            
            Log.Write("TetrisGame: Subscribing to SecondaryAction (R key)");
            commandSubscriptions[4] = currentPlayer.Client.SubscribeToCommand("SecondaryAction", CommandAction.Pressed, HandleRotate, null); // R key → Rotate
            
            Log.Write("TetrisGame: Subscribing to PrimaryAction (F key)");
            commandSubscriptions[5] = currentPlayer.Client.SubscribeToCommand("PrimaryAction", CommandAction.Pressed, HandleHardDrop, null); // F key → Hard drop
            
            Log.Write("TetrisGame: Subscribing to Action1 (1 key)");
            commandSubscriptions[6] = currentPlayer.Client.SubscribeToCommand("Action1", CommandAction.Pressed, HandleRotateCounterClockwise, null); // 1 key → Rotate CCW
            
            // Subscribe to key releases for repeat system
            Log.Write("TetrisGame: Subscribing to key releases for repeat system");
            commandSubscriptions[7] = currentPlayer.Client.SubscribeToCommand("Keypad4", CommandAction.Released, HandleMoveLeftReleased, null);     // A key release
            commandSubscriptions[8] = currentPlayer.Client.SubscribeToCommand("Keypad6", CommandAction.Released, HandleMoveRightReleased, null);    // D key release
            commandSubscriptions[9] = currentPlayer.Client.SubscribeToCommand("Keypad2", CommandAction.Released, HandleSoftDropReleased, null);    // S key release
            
            Log.Write("TetrisGame: Player controls activated using agent.Client.SubscribeToCommand");
            Log.Write("TetrisGame: Controls: A=Left, D=Right, S=SoftDrop, W=SlowDown, R=Rotate, F=HardDrop, 1=RotateCCW");
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error subscribing to player controls: {0}", ex.Message));
            // Continue anyway - some controls might work
        }
    }
    
    // Sansar command handlers using proper CommandData pattern
    private void HandleMoveLeftPressed(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        // Immediate move on press
        MovePieceLeft();
        
        // Set repeat state
        leftKeyHeld = true;
        leftKeyRepeatCount = 0;
    }
    
    private void HandleMoveLeftReleased(CommandData data)
    {
        // Stop repeat
        leftKeyHeld = false;
        leftKeyRepeatCount = 0;
    }
    
    private void HandleMoveRightPressed(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        // Immediate move on press
        MovePieceRight();
        
        // Set repeat state
        rightKeyHeld = true;
        rightKeyRepeatCount = 0;
    }
    
    private void HandleMoveRightReleased(CommandData data)
    {
        // Stop repeat
        rightKeyHeld = false;
        rightKeyRepeatCount = 0;
    }
    
    private void HandleSoftDropReleased(CommandData data)
    {
        // Stop repeat
        softDropKeyHeld = false;
        softDropKeyRepeatCount = 0;
    }
    
    private void HandleSoftDrop(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        // Immediate move on press
        MovePieceDown();
        
        // Set repeat state
        softDropKeyHeld = true;
        softDropKeyRepeatCount = 0;
    }
    
    private void HandleSlowDown(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        Log.Write("TetrisGame: W key (Keypad8) pressed - Slowing down piece");
        lastDropTime = Stopwatch.GetTimestamp(); // Reset drop timer
    }
    
    private void HandleRotate(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        Log.Write("TetrisGame: R key (SecondaryAction) pressed - Rotating clockwise");
        RotatePiece(1);
    }
    
    private void HandleHardDrop(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        Log.Write("TetrisGame: F key (PrimaryAction) pressed - Hard drop");
        HardDrop();
    }
    
    private void HandleRotateCounterClockwise(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        Log.Write("TetrisGame: 1 key (Action1) pressed - Rotating counter-clockwise");
        RotatePiece(-1);
    }
    
    private void GameLoop()
    {
        int loopIteration = 0;
        while (true)
        {
            loopIteration++;
            // Log.Write(string.Format("TetrisGame: GameLoop iteration {0} starting", loopIteration));
            
            if (currentState == GameState.Playing && currentPieceBlocks != null)
            {
                // Log.Write("TetrisGame: GameLoop - Starting auto-drop logic");
                
                // Auto-drop piece at regular intervals
                double elapsedSeconds = (double)(Stopwatch.GetTimestamp() - lastDropTime) / Stopwatch.Frequency;
                if (elapsedSeconds >= dropInterval)
                {
                    // Log.Write(string.Format("TetrisGame: GameLoop - Auto-drop triggered, elapsed: {0}s", elapsedSeconds));
                    
                    if (!MovePieceDown())
                    {
                        // Piece has landed - handle locking
                        Log.Write("TetrisGame: GameLoop - Piece cannot move down, calling OnPieceLanded");
                        OnPieceLanded();
                    }
                    else
                    {
                        // Log.Write("TetrisGame: GameLoop - Piece moved down successfully");
                    }
                    lastDropTime = Stopwatch.GetTimestamp();
                }
                
                // Log.Write("TetrisGame: GameLoop - Auto-drop logic complete, starting key repeat");
                
                //Handle key repeat for smooth movement
                HandleKeyRepeat();
                
                // Log.Write("TetrisGame: GameLoop - Key repeat complete");
            }
            
            // Log.Write("TetrisGame: GameLoop - Starting visual sync");
            
            // Visual update thread - sync visuals to complete game state
            if (currentState == GameState.Playing || currentState == GameState.GameOver)
            {
                SyncAllVisualsToCompleteState();
            }
            
            // Log.Write("TetrisGame: GameLoop - Visual sync complete, iteration ending");
            
            Wait(TimeSpan.FromSeconds(0.1)); // 10 FPS game loop
        }
    }
    
    private void HandleKeyRepeat()
    {
        try
        {
            // Handle left key repeat using simple counter approach
            if (leftKeyHeld)
            {
                leftKeyRepeatCount++;
                
                // After initial delay, repeat at regular intervals
                if (leftKeyRepeatCount > KEY_INITIAL_DELAY_CYCLES)
                {
                    // Check if it's time for a repeat (every KEY_REPEAT_RATE_CYCLES after initial delay)
                    int cyclesSinceInitialDelay = leftKeyRepeatCount - KEY_INITIAL_DELAY_CYCLES;
                    if (cyclesSinceInitialDelay % KEY_REPEAT_RATE_CYCLES == 0)
                    {
                        MovePieceLeft();
                    }
                }
            }
            
            // Handle right key repeat using simple counter approach
            if (rightKeyHeld)
            {
                rightKeyRepeatCount++;
                
                // After initial delay, repeat at regular intervals
                if (rightKeyRepeatCount > KEY_INITIAL_DELAY_CYCLES)
                {
                    // Check if it's time for a repeat (every KEY_REPEAT_RATE_CYCLES after initial delay)
                    int cyclesSinceInitialDelay = rightKeyRepeatCount - KEY_INITIAL_DELAY_CYCLES;
                    if (cyclesSinceInitialDelay % KEY_REPEAT_RATE_CYCLES == 0)
                    {
                        MovePieceRight();
                    }
                }
            }
            
            // Handle soft drop key repeat using simple counter approach  
            if (softDropKeyHeld)
            {
                softDropKeyRepeatCount++;
                
                // After initial delay, repeat at regular intervals
                if (softDropKeyRepeatCount > KEY_INITIAL_DELAY_CYCLES)
                {
                    // Check if it's time for a repeat (every KEY_REPEAT_RATE_CYCLES after initial delay)
                    int cyclesSinceInitialDelay = softDropKeyRepeatCount - KEY_INITIAL_DELAY_CYCLES;
                    if (cyclesSinceInitialDelay % KEY_REPEAT_RATE_CYCLES == 0)
                    {
                        MovePieceDown();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error in HandleKeyRepeat: {0}", ex.Message));
            // Reset key states on error to prevent infinite loops
            leftKeyHeld = false;
            rightKeyHeld = false;
            softDropKeyHeld = false;
            leftKeyRepeatCount = 0;
            rightKeyRepeatCount = 0;
            softDropKeyRepeatCount = 0;
        }
    }
    
    private void SpawnNewPiece()
    {
        Log.Write("TetrisGame: SpawnNewPiece - Starting");
        
        // Get a random piece type
        PieceType[] pieceTypes = { PieceType.I, PieceType.O, PieceType.T, PieceType.S, PieceType.Z, PieceType.J, PieceType.L };
        currentPieceType = pieceTypes[randomGenerator.Next(pieceTypes.Length)];
        currentPieceRotation = 0;
        
        Log.Write(string.Format("TetrisGame: SpawnNewPiece - Selected piece type {0}", currentPieceType));
        
        // Get spawn position (center top with safety margin for piece extensions)
        currentPiecePosition = new Vector(5, 18, 0);
        
        Log.Write("TetrisGame: SpawnNewPiece - About to call CanPlacePiece");
        
        // Debug: Check if the spawn area is clear
        Log.Write("TetrisGame: Checking spawn area (Y=16-19) before spawning...");
        int occupiedCount = 0;
        for (int y = GRID_HEIGHT - 4; y < GRID_HEIGHT; y++)
        {
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                if (IsBlockOccupied(x, y))
                {
                    Log.Write(string.Format("TetrisGame: WARNING - Found occupied block in spawn area at ({0}, {1})", x, y));
                    occupiedCount++;
                }
            }
        }

        if (occupiedCount == 0)
        {
            Log.Write("TetrisGame: Spawn area is clear - safe to spawn");
        }
        else
        {
            Log.Write(string.Format("TetrisGame: WARNING - {0} occupied blocks found in spawn area", occupiedCount));
        }
        
        // Check if we can spawn the piece (game over check)
        if (!CanPlacePiece(currentPieceType, currentPieceRotation, currentPiecePosition))
        {
            Log.Write("TetrisGame: SpawnNewPiece - CanPlacePiece returned false");
            // Debug: Log why we can't spawn
            Log.Write(string.Format("TetrisGame: Cannot spawn piece {0} at {1} - checking grid state", currentPieceType, currentPiecePosition));
            Vector[] testBlocks = GetPieceBlocks(currentPieceType, currentPieceRotation, currentPiecePosition);
            foreach (var blockPos in testBlocks)
            {
                int x = (int)blockPos.X;
                int y = (int)blockPos.Y;
                Log.Write(string.Format("TetrisGame: Block at ({0}, {1}) - InBounds: {2}, Occupied: {3}", x, y, IsBlockInBounds(x, y), IsBlockOccupied(x, y)));
            }
            // Game over - cannot spawn piece
            EndGame();
            return;
        }
        
        Log.Write("TetrisGame: SpawnNewPiece - CanPlacePiece returned true");
        
        // Calculate block positions
        currentPieceBlocks = GetPieceBlocks(currentPieceType, currentPieceRotation, currentPiecePosition);
        
        Log.Write(string.Format("TetrisGame: Successfully validated spawn position for {0} at {1}", currentPieceType, currentPiecePosition));
        
        // Active piece display now handled by visual update cycle
        
        Log.Write(string.Format("TetrisGame: Spawned new piece: {0} at {1} with {2} blocks", currentPieceType, currentPiecePosition, currentPieceBlocks.Length));
    }
    
    private void ShowActivePiece(Vector[] blocks, int pieceType)
    {
        foreach (var blockPos in blocks)
        {
            int x = (int)blockPos.X;
            int y = (int)blockPos.Y;
            if (IsBlockInBounds(x, y))
            {
                SetBlockVisible(x, y, pieceType, true); // Active piece with glow
            }
        }
    }
    
    private void HideActivePiece(Vector[] blocks)
    {
        foreach (var blockPos in blocks)
        {
            int x = (int)blockPos.X;
            int y = (int)blockPos.Y;
            if (IsBlockInBounds(x, y))
            {
                // Only hide if it's not part of locked pieces
                if (blockStates[x, y].PieceType == null)
                {
                    SetBlockHidden(x, y);
                }
            }
        }
    }
    
    #endregion
    
    #region Piece Movement
    
    private bool MovePieceLeft()
    {
        Vector newPosition = new Vector(currentPiecePosition.X - 1, currentPiecePosition.Y, currentPiecePosition.Z);
        return TryMovePiece(newPosition, currentPieceRotation);
    }
    
    private bool MovePieceRight()
    {
        Vector newPosition = new Vector(currentPiecePosition.X + 1, currentPiecePosition.Y, currentPiecePosition.Z);
        return TryMovePiece(newPosition, currentPieceRotation);
    }
    
    private bool MovePieceDown()
    {
        Vector newPosition = new Vector(currentPiecePosition.X, currentPiecePosition.Y - 1, currentPiecePosition.Z);
        return TryMovePiece(newPosition, currentPieceRotation);
    }
    
    private bool TryMovePiece(Vector newPosition, int newRotation)
    {
        // FIXED: Now properly checks collision with locked pieces!
        if (!CanPlacePiece(currentPieceType, newRotation, newPosition))
        {
            Log.Write(string.Format("TetrisGame: TryMovePiece - CanPlacePiece returned false for new position {0} and rotation {1}", newPosition, newRotation));
            return false; // Cannot move to new position
        }
        
        // Pure data update - no visual calls needed
        currentPiecePosition = newPosition;
        currentPieceRotation = newRotation;
        currentPieceBlocks = GetPieceBlocks(currentPieceType, currentPieceRotation, currentPiecePosition);
        
        return true;
    }
    
    private void RotatePiece(int direction)
    {
        int newRotation;
        if (direction > 0)
        {
            // Clockwise
            if (TryRotatePiece(currentPieceType, currentPiecePosition, currentPieceRotation, out newRotation))
            {
                TryMovePiece(currentPiecePosition, newRotation);
            }
        }
        else
        {
            // Counter-clockwise
            int targetRotation = currentPieceRotation - 1;
            if (targetRotation < 0) targetRotation = 3;
            
            if (CanPlacePiece(currentPieceType, targetRotation, currentPiecePosition))
            {
                TryMovePiece(currentPiecePosition, targetRotation);
            }
        }
    }
    
    private void HardDrop()
    {
        // Drop piece as far down as possible
        while (MovePieceDown())
        {
            // Keep dropping until it can't move down
        }
        
        // Piece has landed
        OnPieceLanded();
    }
    
    private void OnPieceLanded()
    {
        if (currentPieceBlocks == null) return;
        
        Log.Write("TetrisGame: Piece landed - locking into grid state");
        
        // Lock piece into grid state
        foreach (var blockPos in currentPieceBlocks)
        {
            int x = (int)blockPos.X;
            int y = (int)blockPos.Y;
            if (IsBlockInBounds(x, y))
            {
                // Update block state
                BlockState block = blockStates[x, y];
                block.PieceType = (int)currentPieceType;
                block.IsVisible = true;
                block.IsActivePiece = false; // No longer active piece
                blockStates[x, y] = block;
                
                // Log.Write(string.Format("TetrisGame: Locked block at ({0}, {1}) as piece type {2}", x, y, currentPieceType));
            }
        }
        
        // Visual sync now handled by independent update cycle in GameLoop
        
        // Check for completed lines
        int linesCleared = CheckAndClearLines();
        if (linesCleared > 0)
        {
            UpdateScore(linesCleared);
        }
        
        // Check for game over
        if (IsGameOver())
        {
            EndGame();
            return;
        }
        
        // Brief pause before spawning next piece
        Wait(TimeSpan.FromSeconds(0.2));
        SpawnNewPiece();
    }
    
    #endregion
    
    #region Line Clearing
    
    public int CheckAndClearLines()
    {
        if (!gridInitialized)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Grid not initialized yet - cannot clear lines");
            return 0;
        }
        
        List<int> completedLines = new List<int>();
        
        // Check each row from bottom to top
        for (int y = 0; y < GRID_HEIGHT; y++)
        {
            if (IsLineComplete(y))
            {
                completedLines.Add(y);
            }
        }
        
        // Clear completed lines with visual effect
        if (completedLines.Count > 0)
        {
            StartCoroutine(() => LineClearSequence(completedLines));
        }
        
        return completedLines.Count;
    }
    
    private bool IsLineComplete(int y)
    {
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            if (blockStates[x, y].PieceType == null)
                return false;
        }
        return true;
    }
    
    private void LineClearSequence(List<int> completedLines)
    {
        // Phase 1: Flash the completed lines
        FlashCompletedLines(completedLines);
        
        // Wait for flash animation
        Wait(TimeSpan.FromSeconds(0.6));
        
        // Phase 2: Clear and drop in one operation
        ClearAndDropLines(completedLines);
        
        Log.Write(string.Format("TetrisGame: Cleared {0} lines", completedLines.Count));
    }
    
    private void FlashCompletedLines(List<int> lines)
    {
        // Clear all flash states first
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                BlockState block = blockStates[x, y];
                if (block.FlashMode == FlashType.LineClear)
                {
                    block.FlashMode = FlashType.None;
                    blockStates[x, y] = block;
                }
            }
        }
        
        // Set flash state for completed lines
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);
        foreach (int y in lines)
        {
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                BlockState block = blockStates[x, y];
                block.FlashMode = FlashType.LineClear;
                block.FlashStartTime = currentTime;
                blockStates[x, y] = block;
            }
        }
    }
    
    private void ClearLine(int y)
    {
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            BlockState block = blockStates[x, y];
            block.PieceType = null;
            block.IsVisible = false;
            block.FlashMode = FlashType.None;
            blockStates[x, y] = block;
            
            SetBlockHidden(x, y);
        }
    }
    
    private void ClearAndDropLines(List<int> completedLines)
    {
        // Create new block state grid with completed lines removed
        BlockState[,] newBlockStates = new BlockState[GRID_WIDTH, GRID_HEIGHT];
        
        // Initialize new grid with default block states
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                newBlockStates[x, y] = new BlockState
                {
                    PieceType = null,
                    IsVisible = false,
                    IsActivePiece = false,
                    FlashMode = FlashType.None,
                    FlashRate = 2.0f,
                    StateChangeTime = currentTime
                };
            }
        }
        
        int writeY = 0; // Where to write the next line in new grid
        
        // Copy non-completed lines to new grid state
        for (int readY = 0; readY < GRID_HEIGHT; readY++)
        {
            // Skip completed lines
            if (completedLines.Contains(readY))
            {
                Log.Write(string.Format("TetrisGame: Skipping completed line at Y={0}", readY));
                continue;
            }
            
            // Copy this line to the write position
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                BlockState sourceBlock = blockStates[x, readY];
                // Clear any line clear flash from the moved block
                if (sourceBlock.FlashMode == FlashType.LineClear)
                {
                    sourceBlock.FlashMode = FlashType.None;
                }
                newBlockStates[x, writeY] = sourceBlock;
            }
            
            // Log.Write(string.Format("TetrisGame: Moved line from Y={0} to Y={1}", readY, writeY));
            writeY++;
        }
        
        // Replace old grid state with new one
        blockStates = newBlockStates;
        
        // Visual sync now handled by independent update cycle in GameLoop
        
        Log.Write(string.Format("TetrisGame: Successfully cleared {0} lines and synced all visuals", completedLines.Count));
    }
    
    /// <summary>
    /// Update active piece positions in block state
    /// </summary>
    private void UpdateActivePieceInBlockState()
    {
        // First clear all active piece flags
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                if (blockStates[x, y].IsActivePiece)
                {
                    BlockState block = blockStates[x, y];
                    block.IsActivePiece = false;
                    // Clear active pieces that aren't locked
                    if (block.PieceType == null || block.PieceType == (int)currentPieceType)
                    {
                        block.PieceType = null;
                        block.IsVisible = false;
                    }
                    blockStates[x, y] = block;
                }
            }
        }
        
        // Overlay active piece if it exists
        if (currentPieceBlocks != null)
        {
            foreach (var blockPos in currentPieceBlocks)
            {
                int x = (int)blockPos.X;
                int y = (int)blockPos.Y;
                if (IsBlockInBounds(x, y))
                {
                    BlockState block = blockStates[x, y];
                    block.PieceType = (int)currentPieceType;
                    block.IsVisible = true;
                    block.IsActivePiece = true;
                    blockStates[x, y] = block;
                }
            }
        }
    }
    
    /// <summary>
    /// Visual update thread - updates ALL block visuals to match complete game state
    /// Runs independently from game logic every 0.1 seconds
    /// </summary>
    private void SyncAllVisualsToCompleteState()
    {
        // First update active piece positions in block state
        UpdateActivePieceInBlockState();
        
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);
        
        int visibleCount = 0;
        int hiddenCount = 0;
        
        // Process each block based on its state
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                BlockState block = blockStates[x, y];
                
                // Update history for edge detection
                block.UpdateHistory(currentTime);
                
                // Handle visual based on current state
                if (block.FlashMode == FlashType.GameOver)
                {
                    // Continuous pulsing for game over
                    float timeSinceFlashStart = block.GetTimeSinceFlashStart(currentTime);
                    SetGameOverFlashVisual(x, y, timeSinceFlashStart);
                    visibleCount++;
                }
                else if (block.FlashMode == FlashType.LineClear)
                {
                    // Time-limited flash for line clearing (0.6 seconds)
                    float timeSinceFlashStart = block.GetTimeSinceFlashStart(currentTime);
                    if (timeSinceFlashStart < 0.6f)
                    {
                        SetFlashVisual(x, y);
                        visibleCount++;
                    }
                    else
                    {
                        // Flash ended, clear the flash mode
                        block.FlashMode = FlashType.None;
                        
                        // Show normal block or hide if empty
                        if (block.IsVisible && block.PieceType.HasValue)
                        {
                            SetBlockVisible(x, y, block.PieceType.Value, block.IsActivePiece);
                            visibleCount++;
                        }
                        else
                        {
                            SetBlockHidden(x, y);
                            hiddenCount++;
                        }
                    }
                }
                else if (block.IsVisible && block.PieceType.HasValue)
                {
                    SetBlockVisible(x, y, block.PieceType.Value, block.IsActivePiece);
                    visibleCount++;
                    
                }
                else
                {
                    SetBlockHidden(x, y);
                    hiddenCount++;
                }
                
                // Store updated block state back to array
                blockStates[x, y] = block;
            }
        }
        
        // Log.Write(string.Format("TetrisGame: Visual sync complete - {0} visible blocks, {1} hidden blocks", visibleCount, hiddenCount));
    }
    
    private void SetFlashVisual(int gridX, int gridY)
    {
        MeshComponent mesh = blockMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            foreach (var material in mesh.GetRenderMaterials())
            {
                var props = material.GetProperties();
                if (material.HasTint) props.Tint = Sansar.Color.White;
                if (material.HasEmissiveIntensity) props.EmissiveIntensity = 15.0f;
                material.SetProperties(props);
            }
        }
    }
    
    private void SetGameOverFlashVisual(int gridX, int gridY, float timeSinceFlashStart)
    {
        MeshComponent mesh = blockMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            // Create a pulsing effect using sine wave with the flash rate from block state
            BlockState block = blockStates[gridX, gridY];
            float pulsePhase = (timeSinceFlashStart * block.FlashRate * 2.0f * 3.14159f); // Complete cycles based on flash rate
            float pulseIntensity = (float)Math.Sin(pulsePhase) * 0.5f + 0.5f; // 0.0 to 1.0
            
            // Get the piece type for this block to maintain its color
            int? pieceTypeInt = blockStates[gridX, gridY].PieceType;
            Sansar.Color baseColor = pieceTypeInt.HasValue ? PieceColors[pieceTypeInt.Value] : Sansar.Color.Red;
            
            foreach (var material in mesh.GetRenderMaterials())
            {
                var props = material.GetProperties();
                if (material.HasTint) props.Tint = baseColor;
                if (material.HasEmissiveIntensity) props.EmissiveIntensity = 2.0f + (pulseIntensity * 8.0f); // Pulse between 2.0 and 10.0
                material.SetProperties(props);
            }
        }
    }
    
    #endregion
    
    #region Scoring and Progression
    
    private void UpdateScore(int linesCleared)
    {
        // Simple scoring system
        int[] lineScores = { 0, 100, 300, 500, 800 }; // 0, 1, 2, 3, 4 lines
        if (linesCleared > 0 && linesCleared <= 4)
        {
            score += lineScores[linesCleared] * level;
        }
        
        this.linesCleared += linesCleared;
        
        // Level up every 10 lines
        if (this.linesCleared / 10 > level - 1)
        {
            level++;
            dropInterval = Math.Max(0.1f, dropInterval * 0.8f); // Speed up
            Log.Write(string.Format("TetrisGame: Level up! Now level {0}, drop interval: {1:F2}s", level, dropInterval));
        }
        
        Log.Write(string.Format("TetrisGame: Score: {0}, Level: {1}, Lines: {2}", score, level, this.linesCleared));
    }
    
    #endregion
    
    #region Game End
    
    private void EndGame()
    {
        currentState = GameState.GameOver;
        
        Log.Write(string.Format("TetrisGame: Game ended. Score: {0}, Lines: {1}, Level: {2}", score, linesCleared, level));
        
        // Mark the final piece that caused game over for continuous flashing
        if (gridInitialized && currentPieceBlocks != null)
        {
            MarkFinalPieceForGameOverFlash();
        }
        
        // Reset only the piece state, keep grid visible for flashing effect
        currentPieceBlocks = null;
        currentPieceRotation = 0;
        
        // Reset key repeat states
        leftKeyHeld = false;
        rightKeyHeld = false;
        softDropKeyHeld = false;
        leftKeyRepeatCount = 0;
        rightKeyRepeatCount = 0;
        softDropKeyRepeatCount = 0;
        
        // Player controls cleanup (simplified implementation)
        Log.Write("TetrisGame: Player controls deactivated");
        
        // Note: Not automatically transitioning to WaitingForPlayer to prevent timing conflicts
        // Player can sit down again to restart when ready
        
        Log.Write("TetrisGame: Game ended - waiting for player to restart manually");
    }
    
    private void MarkFinalPieceForGameOverFlash()
    {
        Log.Write("TetrisGame: Marking final piece blocks for continuous game over flash");
        
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);
        
        // Clear any existing game over flash state
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                if (blockStates[x, y].FlashMode == FlashType.GameOver)
                {
                    BlockState block = blockStates[x, y];
                    block.FlashMode = FlashType.None;
                    blockStates[x, y] = block;
                }
            }
        }
        
        // Mark the current piece blocks for flashing
        int blocksMarked = 0;
        foreach (var pieceBlock in currentPieceBlocks)
        {
            int x = (int)pieceBlock.X;
            int y = (int)pieceBlock.Y;
            
            if (IsBlockInBounds(x, y))
            {
                BlockState block = blockStates[x, y];
                block.FlashMode = FlashType.GameOver;
                block.FlashStartTime = currentTime;
                block.FlashRate = 2.0f; // 2 Hz = 2 pulses per second
                blockStates[x, y] = block;
                
                blocksMarked++;
                Log.Write(string.Format("TetrisGame: Marked final piece block at ({0}, {1}) for game over flash", x, y));
            }
        }
        
        Log.Write(string.Format("TetrisGame: Game over flash enabled for {0} blocks", blocksMarked));
    }
    
    #endregion
    
    #region Cleanup
    
    public void CleanupGrid()
    {
        // Clean up main game blocks
        foreach (var cluster in spawnedClusters)
        {
            if (cluster != null && cluster.IsValid)
            {
                cluster.Destroy();
            }
        }
        spawnedClusters.Clear();
        
        // Clean up background blocks
        foreach (var cluster in spawnedBackgroundClusters)
        {
            if (cluster != null && cluster.IsValid)
            {
                cluster.Destroy();
            }
        }
        spawnedBackgroundClusters.Clear();
        
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                blockObjects[x, y] = null;
                blockMeshes[x, y] = null;
                backgroundBlockObjects[x, y] = null;
                backgroundBlockMeshes[x, y] = null;
                
                // Reset block state
                blockStates[x, y] = new BlockState();
            }
        }
        
        gridInitialized = false;
        blocksSpawned = 0;
        backgroundBlocksSpawned = 0;
        
        Log.Write("TetrisGame: Grid cleanup complete (including background grid)");
    }
    
    // Helper methods for external access
    public int GetWidth() { return GRID_WIDTH; }
    public int GetHeight() { return GRID_HEIGHT; }
    public bool IsGridReady() { return gridInitialized; }
    
    public int? GetBlock(int x, int y)
    {
        if (IsBlockInBounds(x, y))
        {
            return blockStates[x, y].PieceType;
        }
        return null;
    }
    
    #endregion
}