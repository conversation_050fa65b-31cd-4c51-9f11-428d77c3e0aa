using Sansar;
using Sansar.Script;
using Sansar.Simulation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Diagnostics;


/// <summary>
/// Unified Tetris Game Script - Single script combining all game functionality
/// Place this script on the Tetris seat object in your scene
/// </summary>
public class TetrisGame : SceneObjectScript
{
    #region Editor Properties
    
    [Tooltip("Generic block to spawn for each grid position")]
    public ClusterResource GenericBlock;
    
    [Tooltip("Grid origin point in world space")]
    public Vector GridOrigin;
    
    [Tooltip("Spawn blocks in batches to prevent throttling")]
    [DefaultValue(10)]
    public int BatchSize = 10;
    
    [Tooltip("Delay between batches in seconds")]
    [DefaultValue(0.1f)]
    public float BatchDelay = 0.1f;
    
    [Tooltip("Enable rainbow debug mode for testing material control")]
    [DefaultValue(false)]
    public bool DebugRainbowMode = false;
    
    // Audio Configuration
    [DisplayName("Move Sound")]
    [Tooltip("Sound played when piece moves left/right/down")]
    public SoundResource MoveSound;
    
    [DisplayName("Move Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(40.0f)]
    [Tooltip("Volume for movement sounds (0-100%)")]
    public float MoveVolume = 80.0f;
    
    [DisplayName("Rotate Sound")]
    [Tooltip("Sound played when piece rotates")]
    public SoundResource RotateSound;
    
    [DisplayName("Rotate Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(40.0f)]
    [Tooltip("Volume for rotation sounds (0-100%)")]
    public float RotateVolume = 80.0f;
    
    [DisplayName("Hard Drop Sound")]
    [Tooltip("Sound played when piece is hard dropped")]
    public SoundResource HardDropSound;
    
    [DisplayName("Hard Drop Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(60.0f)]
    [Tooltip("Volume for hard drop sounds (0-100%)")]
    public float HardDropVolume = 80.0f;
    
    [DisplayName("Line Clear Sound")]
    [Tooltip("Sound played when 1-3 lines are cleared")]
    public SoundResource LineClearSound;
    
    [DisplayName("Line Clear Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(50.0f)]
    [Tooltip("Volume for line clear sounds (0-100%)")]
    public float LineClearVolume = 60.0f;
    
    [DisplayName("Tetris Sound")]
    [Tooltip("Special sound played when 4 lines are cleared (Tetris)")]
    public SoundResource TetrisSound;
    
    [DisplayName("Tetris Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(70.0f)]
    [Tooltip("Volume for Tetris sounds (0-100%)")]
    public float TetrisVolume = 50.0f;
    
    [DisplayName("Lock Piece Sound")]
    [Tooltip("Sound played when piece locks into place")]
    public SoundResource LockPieceSound;
    
    [DisplayName("Lock Piece Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(30.0f)]
    [Tooltip("Volume for lock piece sounds (0-100%)")]
    public float LockPieceVolume = 80.0f;
    
    [DisplayName("Game Over Sound")]
    [Tooltip("Sound played when the game ends")]
    public SoundResource GameOverSound;
    
    [DisplayName("Game Over Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(60.0f)]
    [Tooltip("Volume for game over sound (0-100%)")]
    public float GameOverVolume = 80.0f;
    
    // Leaderboard Configuration
    [DisplayName("PS ID")]
    [Tooltip("Unique identifier for the leaderboard data store. Use same ID across scenes to share leaderboards, or different IDs for separate leaderboards.")]
    [DefaultValue("TetrisLeaderboard_V1_UniqueKey2025")]
    public string LeaderboardStoreId = "TetrisLeaderboard_V1_UniqueKey2025";
    
    // Background Music Configuration
    [DisplayName("Bg Track 1")]
    [Tooltip("Background music track 1")]
    public SoundResource BackgroundMusic1;
    
    [DisplayName("Bg Track 2")]
    [Tooltip("Background music track 2")]
    public SoundResource BackgroundMusic2;
    
    [DisplayName("Bg Track 3")]
    [Tooltip("Background music track 3")]
    public SoundResource BackgroundMusic3;
    
    [DisplayName("Bg Track 4")]
    [Tooltip("Background music track 4")]
    public SoundResource BackgroundMusic4;

    [DisplayName("Bg Track 5")]
    [Tooltip("Background music track 5")]
    public SoundResource BackgroundMusic5;

    [DisplayName("Bg Track 6")]
    [Tooltip("Background music track 6")]
    public SoundResource BackgroundMusic6;

    [DisplayName("Background Music Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(30.0f)]
    [Tooltip("Default volume for background music (0-100%)")]
    public float BackgroundMusicVolume = 30.0f;
    
    [DisplayName("Background Music Enabled")]
    [DefaultValue(true)]
    [Tooltip("Start with background music playing")]
    public bool BackgroundMusicEnabled = true;
        
    #endregion

    #region Game Constants and Enums

    public const int GRID_WIDTH = 10;
    public const int GRID_HEIGHT = 20;
    public const float BLOCK_SIZE = 1.0f;
    
    // Next piece preview area
    public const int PREVIEW_WIDTH = 4;
    public const int PREVIEW_HEIGHT = 4;
    public const int PREVIEW_OFFSET_X = 12; // Right of main board (10 + 2 spacing)
    public const int PREVIEW_OFFSET_Y = 16; // Top area alignment
    
    public enum PieceType
    {
        I, O, T, S, Z, J, L
    }
    
    public enum GameState
    {
        WaitingForPlayer,
        WaitingForGrid,
        Playing,
        GameOver
    }
    
    public enum FlashType
    {
        None = 0,
        LineClear = 1,
        GameOver = 2
    }
    
    /// <summary>
    /// Represents a single entry in the leaderboard
    /// </summary>
    public class LeaderboardEntry
    {
        public string Handle { get; set; }      // Player's persona handle
        public int Score { get; set; }
        public int Level { get; set; }
        public int Lines { get; set; }
        public DateTime Date { get; set; }
    }
    
    #endregion
    
    #region Block State Structure
    
    /// <summary>
    /// Comprehensive block state with history tracking and edge detection
    /// </summary>
    public struct BlockState
    {
        // Logic-owned current state (authoritative intent)
        public int? PieceType;              // null = empty; 0..6 = locked piece type
        public bool IsVisible;              // desired visibility (locked or active)
        public bool IsActivePiece;          // true when part of the falling piece
        public FlashType FlashMode;         // LineClear, GameOver, or None
        public float FlashRate;             // used for game over pulsing
        public float FlashStartTime;        // timestamp when flashing started
    
        // Visual last-applied state (edge-driven application)
        public bool LastVisible;            // last applied visibility to Mesh
        public bool LastCollidable;         // last applied collision state
        public int LastColorIndex;          // last applied color index (-1 if none)
        public float LastEmissiveIntensity; // last applied emissive intensity
        public FlashType LastFlashMode;     // last applied flash mode
    
        // Historical fields (kept for debugging/metrics)
        public int? PreviousPieceType;
        public bool PreviousIsVisible;
        public FlashType PreviousFlashMode;
        public float StateChangeTime;
        public float VisibilityChangeTime;
    
        // Edge helpers for old path (still usable for diagnostics)
        public bool HasPieceTypeChanged() => PieceType != PreviousPieceType;
        public bool JustBecameVisible() => IsVisible && !PreviousIsVisible;
        public bool JustBecameHidden() => !IsVisible && PreviousIsVisible;
        public bool HasVisibilityChanged() => JustBecameVisible() || JustBecameHidden();
        public bool JustStartedFlashing() => FlashMode != FlashType.None && PreviousFlashMode == FlashType.None;
        public bool JustStoppedFlashing() => FlashMode == FlashType.None && PreviousFlashMode != FlashType.None;
    
        // Update history (diagnostic timestamps)
        public void UpdateHistory(float currentTime)
        {
            if (PieceType != PreviousPieceType)
                StateChangeTime = currentTime;
            if (IsVisible != PreviousIsVisible)
                VisibilityChangeTime = currentTime;
            if (FlashMode != PreviousFlashMode && FlashMode != FlashType.None)
                FlashStartTime = currentTime;
    
            PreviousPieceType = PieceType;
            PreviousIsVisible = IsVisible;
            PreviousFlashMode = FlashMode;
        }
    
        public float GetTimeSinceFlashStart(float currentTime) => currentTime - FlashStartTime;
        public float GetTimeSinceStateChange(float currentTime) => currentTime - StateChangeTime;
    }
    
    #endregion
    
    #region Game State Variables
    
    private GameState currentState = GameState.WaitingForPlayer;
    private AgentPrivate currentPlayer = null;
    private List<AgentPrivate> allPlayers = new List<AgentPrivate>();
    
    // Game scoring
    private int score = 0;
    private int level = 1;
    private int linesCleared = 0;
    private float dropInterval = 1.0f;
    
    // Current active piece state
    private PieceType currentPieceType;
    private Vector currentPiecePosition;
    private int currentPieceRotation = 0;
    private Vector[] currentPieceBlocks;
    
    // Next piece preview
    private PieceType nextPieceType;
    private bool nextPieceInitialized = false;
    
    private long lastDropTime = Stopwatch.GetTimestamp();
    
    // Hard drop protection flag to prevent race conditions
    private bool isHardDropping = false;
    
    // Pre-initialized Random to avoid blocking on entropy
    private Random randomGenerator = new Random();
    
    // Leaderboard persistence
    private DataStore leaderboardStore;
    private List<LeaderboardEntry> leaderboard = new List<LeaderboardEntry>();
    private readonly string leaderboardKey = "top10";
    
    // Background music system
    private PlayHandle currentMusicHandle;
    private int currentTrackIndex = 0;
    private bool musicEnabled = true;
    private float currentMusicVolume = 30.0f;
    private SoundResource[] musicTracks = new SoundResource[3];
    private bool musicSystemInitialized = false;
    
    #endregion
    
    #region Grid Management Variables
    
    // Unified block state array - single source of truth for all block properties
    private BlockState[,] blockStates = new BlockState[GRID_WIDTH, GRID_HEIGHT];
    
    // Object references to spawned blocks for direct control
    private ObjectPrivate[,] blockObjects = new ObjectPrivate[GRID_WIDTH, GRID_HEIGHT];
    
    // Mesh component references for direct material control
    private MeshComponent[,] blockMeshes = new MeshComponent[GRID_WIDTH, GRID_HEIGHT];
    
    // RigidBodyComponent references for collision control
    private RigidBodyComponent[,] blockRigidBodies = new RigidBodyComponent[GRID_WIDTH, GRID_HEIGHT];
    
    // Background grid - black blocks that stay visible for visual reference
    private ObjectPrivate[,] backgroundBlockObjects = new ObjectPrivate[GRID_WIDTH, GRID_HEIGHT];
    private MeshComponent[,] backgroundBlockMeshes = new MeshComponent[GRID_WIDTH, GRID_HEIGHT];
    
    // Next piece preview area - separate from main game state
    private ObjectPrivate[,] previewBlockObjects = new ObjectPrivate[PREVIEW_WIDTH, PREVIEW_HEIGHT];
    private MeshComponent[,] previewBlockMeshes = new MeshComponent[PREVIEW_WIDTH, PREVIEW_HEIGHT];
    private ObjectPrivate[,] previewBackgroundObjects = new ObjectPrivate[PREVIEW_WIDTH, PREVIEW_HEIGHT];
    private MeshComponent[,] previewBackgroundMeshes = new MeshComponent[PREVIEW_WIDTH, PREVIEW_HEIGHT];
    
    // Spawned cluster references for cleanup
    private List<Cluster> spawnedClusters = new List<Cluster>();
    private List<Cluster> spawnedBackgroundClusters = new List<Cluster>();
    private List<Cluster> spawnedPreviewClusters = new List<Cluster>();
    private List<Cluster> spawnedPreviewBackgroundClusters = new List<Cluster>();
    
    // Spawn tracking
    private int blocksSpawned = 0;
    private int backgroundBlocksSpawned = 0;
    private int previewBlocksSpawned = 0;
    private int previewBackgroundBlocksSpawned = 0;
    private int totalBlocksToSpawn = GRID_WIDTH * GRID_HEIGHT;
    private int totalBackgroundBlocksToSpawn = GRID_WIDTH * GRID_HEIGHT;
    private int totalPreviewBlocksToSpawn = PREVIEW_WIDTH * PREVIEW_HEIGHT;
    private int totalPreviewBackgroundBlocksToSpawn = PREVIEW_WIDTH * PREVIEW_HEIGHT;
    private bool gridInitialized = false;
    private bool previewInitialized = false;
    
    #endregion
    
    #region Seat Detection Variables
    
    private RigidBodyComponent rigidBody;
    private IEventSubscription sitSubscription;
    private IEventSubscription standSubscription;
    
    // Chat system variables
    private IEventSubscription chatSubscription;
    private Dictionary<string, string> commandsUsage;
    
    // Command subscriptions for player input (expanded for press/release pairs)
    private IEventSubscription[] commandSubscriptions = new IEventSubscription[10];
    
    // Key repeat system for smooth movement
    private bool leftKeyHeld = false;
    private bool rightKeyHeld = false;
    private bool softDropKeyHeld = false;
    private int leftKeyRepeatCount = 0;
    private int rightKeyRepeatCount = 0;
    private int softDropKeyRepeatCount = 0;
    private const int KEY_INITIAL_DELAY_CYCLES = 3;  // 3 cycles (300ms) before repeat starts
    private const int KEY_REPEAT_RATE_CYCLES = 1;    // 1 cycle (100ms) between repeats
    
    #endregion
        
    #region Piece Shape Definitions
    
    private static readonly Dictionary<PieceType, Vector[][]> PieceShapes = new Dictionary<PieceType, Vector[][]>
    {
        {PieceType.I, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(2, 0, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(0, 2, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(2, 0, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(0, 2, 0) }
        }},
        
        {PieceType.O, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) }
        }},
        
        {PieceType.T, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(1, 0, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(0, -1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(-1, 0, 0) }
        }},
        
        {PieceType.S, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(-1, 0, 0), new Vector(-1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(-1, 0, 0), new Vector(-1, 1, 0) }
        }},
        
        {PieceType.Z, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(-1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(1, 0, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(-1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(1, 0, 0), new Vector(1, 1, 0) }
        }},
        
        {PieceType.J, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(-1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(1, -1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(-1, -1, 0) }
        }},
        
        {PieceType.L, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(1, -1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(-1, -1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(-1, 1, 0) }
        }}
    };
    
    #endregion
    
    #region Color Definitions
    
    // Tetris piece colors
    private static readonly Dictionary<int, Sansar.Color> PieceColors = CreatePieceColors();
    
    private static Dictionary<int, Sansar.Color> CreatePieceColors()
    {
        var colors = new Dictionary<int, Sansar.Color>();
        colors.Add(0, new Sansar.Color(0.0f, 1.0f, 1.0f, 1.0f)); // I - Cyan
        colors.Add(1, new Sansar.Color(1.0f, 1.0f, 0.0f, 1.0f)); // O - Yellow
        colors.Add(2, new Sansar.Color(0.5f, 0.0f, 1.0f, 1.0f)); // T - Purple
        colors.Add(3, new Sansar.Color(0.0f, 1.0f, 0.0f, 1.0f)); // S - Green
        colors.Add(4, new Sansar.Color(1.0f, 0.0f, 0.0f, 1.0f)); // Z - Red
        colors.Add(5, new Sansar.Color(0.0f, 0.0f, 1.0f, 1.0f)); // J - Blue
        colors.Add(6, new Sansar.Color(1.0f, 0.5f, 0.0f, 1.0f)); // L - Orange
        return colors;
    }
    
    // Hidden color (completely transparent)
    private static readonly Sansar.Color HiddenColor = new Sansar.Color(0.0f, 0.0f, 0.0f, 0.0f);
    
    // Background grid color (solid black)
    private static readonly Sansar.Color BackgroundColor = new Sansar.Color(0.1f, 0.1f, 0.1f, 1.0f);
    
    #endregion
    
    #region Initialization
    
    public override void Init()
    {
        Log.Write("TetrisGame V2 - Unified Single Script Architecture");
        
        // Initialize grid state
        InitializeGridState();
        
        // Initialize seat detection
        InitializeSeatDetection();
        
        // Initialize chat interface system
        InitializeChatCommands();
        
        // Initialize leaderboard persistence
        InitializeLeaderboard();
        
        // Initialize background music system
        InitializeBackgroundMusic();
        
        // Audio system now integrated directly into TetrisGame
        Log.Write(LogLevel.Info, "TetrisGame: Audio system initialized with integrated properties");
        // Validate resources
        if (GenericBlock == null)
        {
            Log.Write(LogLevel.Error, "TetrisGame: GenericBlock resource not set");
            return;
        }
        
        // Start spawning background grid first, then main grid blocks, then preview area
        StartCoroutine(SpawnBackgroundGrid);
        StartCoroutine(SpawnGridBlocks);
        StartCoroutine(SpawnPreviewBackgroundGrid);
        StartCoroutine(SpawnPreviewGridBlocks);
        
        // Start main game loop
        StartCoroutine(GameLoop);
        
        Log.Write(string.Format("TetrisGame initialized - will spawn {0} background blocks and {1} game blocks", totalBackgroundBlocksToSpawn, totalBlocksToSpawn));
        Log.Write(string.Format("TetrisGame: GridOrigin set to: {0}", GridOrigin));
    }
    
    private void InitializeSeatDetection()
    {
        if (!ObjectPrivate.TryGetFirstComponent(out rigidBody))
        {
            Log.Write(LogLevel.Error, "TetrisGame: No RigidBodyComponent found on seat object");
            return;
        }
        
        // Subscribe to sit/stand events using Sansar's native API
        sitSubscription = rigidBody.SubscribeToSitObject(SitEventType.Start, OnPlayerSitDown);
        standSubscription = rigidBody.SubscribeToSitObject(SitEventType.End, OnPlayerStandUp);
        
        // Player input handled via SubscribeToScriptEvent - no additional components needed
        
        Log.Write("TetrisGame: Seat detection and interaction initialized");
    }
    
    private void InitializeChatCommands()
    {
        commandsUsage = new Dictionary<string, string>
        {
            { "/tet help", "(Anyone) Show command list" },
            { "/tet leaderboard", "(Anyone) Show top 10 scores" },
            { "/tet leader", "(Anyone) Show top 10 scores (short)" },
            { "/tet score", "(Seated) Show current score" },
            { "/tet status", "(Seated) Show game state" },
            { "/tet reset", "(Seated) Start new game" },
            { "/tet restart", "(Seated) Start new game" },
            { "/tet start", "(Seated) Start new game" },
            { "/tet bg", "(Anyone) Toggle background music on/off" },
            { "/tet bg volume", "(Anyone) Set music volume (0-100)" }
        };
        
        Log.Write("TetrisGame: Chat interface commands initialized with /tet prefix");
    }
    
    private void InitializeLeaderboard()
    {
        Log.Write(LogLevel.Info, "TetrisGame: InitializeLeaderboard() called");
        
        if (string.IsNullOrEmpty(LeaderboardStoreId))
        {
            Log.Write(LogLevel.Warning, "TetrisGame: LeaderboardStoreId is empty, leaderboard disabled");
            return;
        }
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Attempting to create DataStore with ID: '{0}'", LeaderboardStoreId));
        
        leaderboardStore = ScenePrivate.CreateDataStore(LeaderboardStoreId);
        if (leaderboardStore != null)
        {
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Leaderboard DataStore created successfully with id: '{0}'", LeaderboardStoreId));
            Log.Write(LogLevel.Info, string.Format("TetrisGame: DataStore.Id = '{0}'", leaderboardStore.Id));
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Attempting to restore leaderboard data with key: '{0}'", leaderboardKey));
            
            leaderboardStore.Restore<List<LeaderboardEntry>>(leaderboardKey, LoadLeaderboard);
        }
        else
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: FAILED to create leaderboard DataStore with id: '{0}'", LeaderboardStoreId));
            Log.Write(LogLevel.Error, "TetrisGame: Leaderboard functionality will be disabled");
        }
    }
    
    private void InitializeBackgroundMusic()
    {
        musicTracks[0] = BackgroundMusic1;
        musicTracks[1] = BackgroundMusic2;
        musicTracks[2] = BackgroundMusic3;
        musicTracks[3] = BackgroundMusic4;
        musicTracks[4] = BackgroundMusic5;
        musicTracks[5] = BackgroundMusic6;
        
        musicEnabled = BackgroundMusicEnabled;
        currentMusicVolume = BackgroundMusicVolume;
        musicSystemInitialized = true;
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Background music initialized - Enabled: {0}, Volume: {1}%", musicEnabled, currentMusicVolume));
        
        // Start background music if enabled
        if (musicEnabled)
        {
            StartCoroutine(StartBackgroundMusic);
        }
    }
    
    private void InitializeGridState()
    {
        // Initialize empty grid with default block states
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                // Initialize block state to default values
                blockStates[x, y] = new BlockState
                {
                    // Logic-owned intent
                    PieceType = null,
                    IsVisible = false,
                    IsActivePiece = false,
                    FlashMode = FlashType.None,
                    FlashRate = 2.0f, // Default flash rate
                    FlashStartTime = 0f,
    
                    // Visual last-applied
                    LastVisible = false,
                    LastCollidable = false,
                    LastColorIndex = -1,
                    LastEmissiveIntensity = 0f,
                    LastFlashMode = FlashType.None,
    
                    // History
                    PreviousPieceType = null,
                    PreviousIsVisible = false,
                    PreviousFlashMode = FlashType.None,
                    StateChangeTime = 0f,
                    VisibilityChangeTime = 0f
                };
    
                blockObjects[x, y] = null;
                blockMeshes[x, y] = null;
                blockRigidBodies[x, y] = null;
                backgroundBlockObjects[x, y] = null;
                backgroundBlockMeshes[x, y] = null;
            }
        }
    
        Log.Write("TetrisGame: Grid state initialized for new game");
    }
    
    private void ResetGameVariables()
    {
        // Reset block states while preserving object references
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);
        
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                // Reset block state but preserve the history for edge detection
                BlockState block = blockStates[x, y];
                block.UpdateHistory(currentTime); // Save current state as previous
                
                // Clear current state
                block.PieceType = null;
                block.IsVisible = false;
                block.IsActivePiece = false;
                block.FlashMode = FlashType.None;
                
                blockStates[x, y] = block;
            }
        }
        
        // Force clear spawn area to prevent stuck pieces (top 4 rows)
        Log.Write("TetrisGame: Force clearing spawn area (Y=16-19) for clean restart");
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = GRID_HEIGHT - 4; y < GRID_HEIGHT; y++)
            {
                // Ensure spawn area is completely clear
                BlockState block = blockStates[x, y];
                block.PieceType = null;
                block.IsVisible = false;
                blockStates[x, y] = block;
                
                // Also ensure visual is hidden
                if (gridInitialized)
                {
                    SetBlockHidden(x, y);
                }
            }
        }
        
        // Reset current piece state
        currentPieceBlocks = null;
        currentPieceRotation = 0;
        
        // Reset drop timer to current time
        lastDropTime = Stopwatch.GetTimestamp();
        
        // Reset key repeat states
        leftKeyHeld = false;
        rightKeyHeld = false;
        softDropKeyHeld = false;
        leftKeyRepeatCount = 0;
        rightKeyRepeatCount = 0;
        softDropKeyRepeatCount = 0;
        
        // Reset hard drop flag
        isHardDropping = false;
        
        // Reset next piece system
        nextPieceInitialized = false;
        
        Log.Write("TetrisGame: Game variables reset for clean restart");
    }
    
    #endregion
    
    #region Player Session Management
    
    private void OnPlayerSitDown(SitObjectData data)
    {
        // Player sat down - start game session
        Log.Write(string.Format("TetrisGame: Player sat down - previous state: {0}", currentState));
        
        // Get the player who sat down
        currentPlayer = ScenePrivate.FindAgent(data.ObjectId);
        
        // Set up chat interface for this player
        if (currentPlayer != null && currentPlayer.IsValid)
        {
            // Subscribe to chat messages
            chatSubscription = ScenePrivate.Chat.Subscribe(Chat.DefaultChannel, OnChatMessage);
            Log.Write(LogLevel.Info, "TetrisGame: Chat interface activated for " + currentPlayer.AgentInfo.Name);
            
            // Send immediate help message to the player
            SendHelpMessage(currentPlayer);
            
            // Start welcome hint coroutine
            StartCoroutine(ShowWelcomeHint, currentPlayer, currentPlayer.AgentInfo.SessionId);
        }
        
        Log.Write(string.Format("TetrisGame: Grid status - blocksSpawned: {0}/{1}, gridInitialized: {2}", blocksSpawned, totalBlocksToSpawn, gridInitialized));
        
        if (gridInitialized)
        {
            Log.Write(string.Format("TetrisGame: Grid ready - current state is {0}, starting new game", currentState));
            StartNewGame();
        }
        else
        {
            currentState = GameState.WaitingForGrid;
            Log.Write("TetrisGame: Grid not ready yet - waiting for grid initialization");
        }
    }
    
    private void OnPlayerStandUp(SitObjectData data)
    {
        // Player stood up - end game session
        Log.Write(string.Format("TetrisGame: Player stood up - current state: {0}", currentState));
        
        // Reset key repeat states
        leftKeyHeld = false;
        rightKeyHeld = false;
        softDropKeyHeld = false;
        leftKeyRepeatCount = 0;
        rightKeyRepeatCount = 0;
        softDropKeyRepeatCount = 0;
        
        // Unsubscribe from all commands
        UnsubscribeFromPlayerControls();
        
        // Unsubscribe from chat
        if (chatSubscription != null)
        {
            chatSubscription.Unsubscribe();
            chatSubscription = null;
            Log.Write(LogLevel.Info, "TetrisGame: Chat interface deactivated");
        }
        
        Log.Write("TetrisGame: Calling EndGameQuietly() due to player stand up");
        EndGameQuietly();
        currentPlayer = null;
        
        Log.Write("TetrisGame: Player session cleanup complete");
    }
    
    private void UnsubscribeFromPlayerControls()
    {
        for (int i = 0; i < commandSubscriptions.Length; i++)
        {
            if (commandSubscriptions[i] != null)
            {
                commandSubscriptions[i].Unsubscribe();
                commandSubscriptions[i] = null;
            }
        }
        Log.Write("TetrisGame: Unsubscribed from all player commands");
    }
    
    #endregion
    
    #region Multi-Player Tracking System
    
    private void InitializePlayerTracking()
    {
        Log.Write("TetrisGame: Initializing player tracking system");
        
        // Subscribe to user join/leave events
        ScenePrivate.User.Subscribe(User.AddUser, OnUserJoin);
        ScenePrivate.User.Subscribe(User.RemoveUser, OnUserLeave);
        
        // Initialize with any players already in the scene
        foreach (var agent in ScenePrivate.GetAgents())
        {
            if (agent != null && agent.IsValid)
            {
                allPlayers.Add(agent);
                Log.Write(string.Format("TetrisGame: Added existing player to tracking: {0}", agent.AgentInfo.Name));
            }
        }
        
        Log.Write(string.Format("TetrisGame: Player tracking initialized with {0} existing players", allPlayers.Count));
    }
    
    private void OnUserJoin(UserData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.User);
        if (agent != null && agent.IsValid)
        {
            allPlayers.Add(agent);
            Log.Write(string.Format("TetrisGame: Player joined: {0} (Total players: {1})", agent.AgentInfo.Name, allPlayers.Count));
            
            // Set collision state for currently visible blocks for this new player
            if (gridInitialized)
            {
                InitializeCollisionForNewPlayer(agent);
            }
        }
    }
    
    private void OnUserLeave(UserData data)
    {
        // Remove player from list by SessionId since agent reference may be invalid
        int removedCount = allPlayers.RemoveAll(agent => agent == null || !agent.IsValid || agent.AgentInfo.SessionId == data.User);
        if (removedCount > 0)
        {
            Log.Write(string.Format("TetrisGame: Player left (removed {0} entries, Total players: {1})", removedCount, allPlayers.Count));
        }
    }
    
    private void InitializeCollisionForNewPlayer(AgentPrivate player)
    {
        if (player == null || !player.IsValid)
            return;
            
        Log.Write(string.Format("TetrisGame: Initializing collision state for new player: {0}", player.AgentInfo.Name));
        
        // Set collision state for all main game blocks based on their current visibility
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                RigidBodyComponent rigidBody = blockRigidBodies[x, y];
                if (rigidBody != null)
                {
                    bool isVisible = blockStates[x, y].IsVisible;
                    // true = ignore collision (pass through), false = enable collision (solid)
                    player.IgnoreCollisionWith(rigidBody, !isVisible);
                }
            }
        }
    }
    
    #endregion
    
    #region Background Grid Spawning
    
    private void SpawnBackgroundGrid()
    {
        Log.Write("TetrisGame: Starting background grid spawning...");
        
        // Spawn background blocks in batches to prevent throttling
        for (int batchStart = 0; batchStart < totalBackgroundBlocksToSpawn; batchStart += BatchSize)
        {
            SpawnBackgroundBatch(batchStart);
            
            // Wait between batches
            if (batchStart + BatchSize < totalBackgroundBlocksToSpawn)
            {
                Wait(TimeSpan.FromSeconds(BatchDelay));
            }
        }
    }
    
    private void SpawnBackgroundBatch(int startIndex)
    {
        int endIndex = Math.Min(startIndex + BatchSize, totalBackgroundBlocksToSpawn);
        
        for (int i = startIndex; i < endIndex; i++)
        {
            int x = i % GRID_WIDTH;
            int y = i / GRID_WIDTH;
            
            SpawnBackgroundBlockAt(x, y);
        }
        
        // Log.Write(string.Format("TetrisGame: Spawned background batch: blocks {0} to {1}", startIndex, endIndex - 1));
    }
    
    private void SpawnBackgroundBlockAt(int gridX, int gridY)
    {
        Sansar.Vector worldPosition = GridToWorldBackground(gridX, gridY);
        
        try
        {
            ScenePrivate.CreateCluster(GenericBlock, worldPosition, Sansar.Quaternion.Identity, Sansar.Vector.Zero,
                (ScenePrivate.CreateClusterData data) =>
                {
                    if (data.Success && data.ClusterReference != null)
                    {
                        OnBackgroundBlockSpawned(data.ClusterReference, gridX, gridY);
                    }
                    else
                    {
                        Log.Write(LogLevel.Error, string.Format("TetrisGame: Failed to spawn background block at ({0}, {1}): {2}", gridX, gridY, data.Message));
                    }
                });
        }
        catch (ThrottleException)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Throttled background block at ({0}, {1}) - retrying in 0.5 seconds", gridX, gridY));
            StartCoroutine(() => RetrySpawnBackgroundBlock(gridX, gridY));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Exception spawning background block at ({0}, {1}): {2}", gridX, gridY, ex.Message));
        }
    }
    
    private void RetrySpawnBackgroundBlock(int gridX, int gridY)
    {
        Wait(TimeSpan.FromSeconds(0.5));
        SpawnBackgroundBlockAt(gridX, gridY);
    }
    
    private void OnBackgroundBlockSpawned(Cluster cluster, int gridX, int gridY)
    {
        // Store cluster reference for cleanup
        spawnedBackgroundClusters.Add(cluster);
        
        // Store object and mesh references for direct control
        foreach (var objectPrivate in cluster.GetObjectPrivates())
        {
            // Store object reference
            backgroundBlockObjects[gridX, gridY] = objectPrivate;
            
            // Get and store mesh component for direct material control
            MeshComponent mesh;
            if (objectPrivate.TryGetFirstComponent(out mesh))
            {
                backgroundBlockMeshes[gridX, gridY] = mesh;
                
                
                // Check if mesh is scriptable and set to black background color
                if (mesh.IsScriptable)
                {
                    SetBackgroundBlockVisible(gridX, gridY);
                }
                else
                {
                    Log.Write(LogLevel.Error, string.Format("TetrisGame: Background block ({0}, {1}): Mesh is not scriptable - cannot control materials", gridX, gridY));
                }
            }
            else
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Background block ({0}, {1}): No MeshComponent found", gridX, gridY));
            }
            
            break; // Only need the first object
        }
        
        backgroundBlocksSpawned++;
        
    }
    
    
    // Convert grid coordinates to world position for background blocks (offset to be visible)
    private Sansar.Vector GridToWorldBackground(int gridX, int gridY)
    {
        // According to the comments: gridX->X (left/right), gridY->Z (up/down), Y is forward/back
        // For background blocks directly behind each game block, use same X,Z but different Y
        return new Sansar.Vector(
            GridOrigin.X + (gridX * BLOCK_SIZE),     // Same X position as game blocks
            GridOrigin.Y + 1.0f,                     // Try positive Y offset (forward instead of back)
            GridOrigin.Z + (gridY * BLOCK_SIZE)      // Same Z position as game blocks
        );
    }
    
    public void SetBackgroundBlockVisible(int gridX, int gridY)
    {
        if (gridX < 0 || gridX >= GRID_WIDTH || gridY < 0 || gridY >= GRID_HEIGHT)
            return;
        
        MeshComponent mesh = backgroundBlockMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            try
            {
                // Make the background block visible with black color
                mesh.SetIsVisible(true);
                
                foreach (var material in mesh.GetRenderMaterials())
                {
                    var props = material.GetProperties();
                    
                    if (material.HasTint)
                    {
                        props.Tint = BackgroundColor;
                    }
                    
                    if (material.HasEmissiveIntensity)
                    {
                        props.EmissiveIntensity = 0.0f; // No glow for background
                    }
                    
                    material.SetProperties(props);
                }
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Error showing background block ({0}, {1}): {2}", gridX, gridY, ex.Message));
            }
        }
    }
    
    #endregion
    
    #region Grid Spawning and Management
    
    private void SpawnGridBlocks()
    {
        Log.Write("TetrisGame: Starting grid block spawning...");
        
        // Spawn blocks in batches to prevent throttling
        for (int batchStart = 0; batchStart < totalBlocksToSpawn; batchStart += BatchSize)
        {
            SpawnBatch(batchStart);
            
            // Wait between batches
            if (batchStart + BatchSize < totalBlocksToSpawn)
            {
                Wait(TimeSpan.FromSeconds(BatchDelay));
            }
        }
    }
    
    private void SpawnBatch(int startIndex)
    {
        int endIndex = Math.Min(startIndex + BatchSize, totalBlocksToSpawn);
        
        for (int i = startIndex; i < endIndex; i++)
        {
            int x = i % GRID_WIDTH;
            int y = i / GRID_WIDTH;
            
            SpawnBlockAt(x, y);
        }
        
        // Log.Write(string.Format("TetrisGame: Spawned batch: blocks {0} to {1}", startIndex, endIndex - 1));
    }
    
    private void SpawnBlockAt(int gridX, int gridY)
    {
        Sansar.Vector worldPosition = GridToWorld(gridX, gridY);
        
        try
        {
            ScenePrivate.CreateCluster(GenericBlock, worldPosition, Sansar.Quaternion.Identity, Sansar.Vector.Zero,
                (ScenePrivate.CreateClusterData data) =>
                {
                    if (data.Success && data.ClusterReference != null)
                    {
                        OnBlockSpawned(data.ClusterReference, gridX, gridY);
                    }
                    else
                    {
                        Log.Write(LogLevel.Error, string.Format("TetrisGame: Failed to spawn block at ({0}, {1}): {2}", gridX, gridY, data.Message));
                    }
                });
        }
        catch (ThrottleException)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Throttled at ({0}, {1}) - retrying in 0.5 seconds", gridX, gridY));
            StartCoroutine(() => RetrySpawnBlock(gridX, gridY));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Exception spawning block at ({0}, {1}): {2}", gridX, gridY, ex.Message));
        }
    }
    
    private void RetrySpawnBlock(int gridX, int gridY)
    {
        Wait(TimeSpan.FromSeconds(0.5));
        SpawnBlockAt(gridX, gridY);
    }
    
    private void OnBlockSpawned(Cluster cluster, int gridX, int gridY)
    {
        // Store cluster reference for cleanup
        spawnedClusters.Add(cluster);
        
        // Store object and mesh references for direct control
        foreach (var objectPrivate in cluster.GetObjectPrivates())
        {
            // Store object reference
            blockObjects[gridX, gridY] = objectPrivate;
            
            // Get and store mesh component for direct material control
            MeshComponent mesh;
            if (objectPrivate.TryGetFirstComponent(out mesh))
            {
                blockMeshes[gridX, gridY] = mesh;
                
                // Get and store rigid body component for collision control
                RigidBodyComponent rigidBody;
                if (objectPrivate.TryGetFirstComponent(out rigidBody))
                {
                    blockRigidBodies[gridX, gridY] = rigidBody;
                }
                else
                {
                    Log.Write(LogLevel.Warning, string.Format("TetrisGame: Block ({0}, {1}): No RigidBodyComponent found - collision control disabled", gridX, gridY));
                }
                
                // Check if mesh is scriptable and log material capabilities
                if (mesh.IsScriptable)
                {
                    var materialsEnum = mesh.GetRenderMaterials();
                    var materials = new List<RenderMaterial>();
                    foreach (var mat in materialsEnum)
                    {
                        materials.Add(mat);
                    }
                    
                    // Set initial hidden state directly
                    SetBlockHidden(gridX, gridY);
                    // Log.Write(string.Format("TetrisGame: Block ({0}, {1}): Successfully created scriptable mesh with {2} materials", gridX, gridY, materials.Count));
                }
                else
                {
                    Log.Write(LogLevel.Error, string.Format("TetrisGame: Block ({0}, {1}): Mesh is not scriptable - cannot control materials", gridX, gridY));
                }
            }
            else
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Block ({0}, {1}): No MeshComponent found", gridX, gridY));
            }
            
            break; // Only need the first object
        }
        
        blocksSpawned++;
        // Log.Write(string.Format("TetrisGame: Block spawned at ({0}, {1}) - progress: {2}/{3}", gridX, gridY, blocksSpawned, totalBlocksToSpawn));
        
        if (blocksSpawned >= totalBlocksToSpawn)
        {
            OnGridInitializationComplete();
        }
    }
    
    private void OnGridInitializationComplete()
    {
        gridInitialized = true;
        Log.Write("TetrisGame: Grid initialization complete - all blocks spawned and ready");
        
        // Start debug rainbow mode if enabled
        if (DebugRainbowMode)
        {
            Log.Write("TetrisGame: Starting rainbow debug mode to test material control");
            StartCoroutine(RainbowDebugSequence);
        }
        
        // Initial visual sync to set up clean board
        SyncAllVisualsToCompleteState();
        
        // If a player is waiting, start the game
        if (currentState == GameState.WaitingForGrid && currentPlayer != null)
        {
            StartNewGame();
        }
    }
    
    // Convert grid coordinates to world position
    private Sansar.Vector GridToWorld(int gridX, int gridY)
    {
        // Map grid coordinates to world space for vertical Tetris layout:
        // gridX -> world X (left/right)
        // gridY -> world Z (up/down) 
        // world Y stays 0 (forward/back - no change)
        return new Sansar.Vector(
            GridOrigin.X + (gridX * BLOCK_SIZE),
            GridOrigin.Y,
            GridOrigin.Z + (gridY * BLOCK_SIZE)
        );
    }
    
    #endregion
    
    #region Preview Area Spawning and Management
    
    private void SpawnPreviewBackgroundGrid()
    {
        Log.Write("TetrisGame: Starting preview background grid spawning...");
        
        // Spawn preview background blocks in batches to prevent throttling
        for (int batchStart = 0; batchStart < totalPreviewBackgroundBlocksToSpawn; batchStart += BatchSize)
        {
            SpawnPreviewBackgroundBatch(batchStart);
            
            // Wait between batches
            if (batchStart + BatchSize < totalPreviewBackgroundBlocksToSpawn)
            {
                Wait(TimeSpan.FromSeconds(BatchDelay));
            }
        }
    }
    
    private void SpawnPreviewBackgroundBatch(int startIndex)
    {
        int endIndex = Math.Min(startIndex + BatchSize, totalPreviewBackgroundBlocksToSpawn);
        
        for (int i = startIndex; i < endIndex; i++)
        {
            int x = i % PREVIEW_WIDTH;
            int y = i / PREVIEW_WIDTH;
            
            SpawnPreviewBackgroundBlockAt(x, y);
        }
        
        Log.Write(string.Format("TetrisGame: Spawned preview background batch: blocks {0} to {1}", startIndex, endIndex - 1));
    }
    
    private void SpawnPreviewBackgroundBlockAt(int gridX, int gridY)
    {
        Sansar.Vector worldPosition = GridToWorldPreviewBackground(gridX, gridY);
        
        try
        {
            ScenePrivate.CreateCluster(GenericBlock, worldPosition, Sansar.Quaternion.Identity, Sansar.Vector.Zero,
                (ScenePrivate.CreateClusterData data) =>
                {
                    if (data.Success && data.ClusterReference != null)
                    {
                        OnPreviewBackgroundBlockSpawned(data.ClusterReference, gridX, gridY);
                    }
                    else
                    {
                        Log.Write(LogLevel.Error, string.Format("TetrisGame: Failed to spawn preview background block at ({0}, {1}): {2}", gridX, gridY, data.Message));
                    }
                });
        }
        catch (ThrottleException)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Throttled preview background block at ({0}, {1}) - retrying in 0.5 seconds", gridX, gridY));
            StartCoroutine(() => RetrySpawnPreviewBackgroundBlock(gridX, gridY));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Exception spawning preview background block at ({0}, {1}): {2}", gridX, gridY, ex.Message));
        }
    }
    
    private void RetrySpawnPreviewBackgroundBlock(int gridX, int gridY)
    {
        Wait(TimeSpan.FromSeconds(0.5));
        SpawnPreviewBackgroundBlockAt(gridX, gridY);
    }
    
    private void OnPreviewBackgroundBlockSpawned(Cluster cluster, int gridX, int gridY)
    {
        // Store cluster reference for cleanup
        spawnedPreviewBackgroundClusters.Add(cluster);
        
        // Store object and mesh references for direct control
        foreach (var objectPrivate in cluster.GetObjectPrivates())
        {
            // Store object reference
            previewBackgroundObjects[gridX, gridY] = objectPrivate;
            
            // Get and store mesh component for direct material control
            MeshComponent mesh;
            if (objectPrivate.TryGetFirstComponent(out mesh))
            {
                previewBackgroundMeshes[gridX, gridY] = mesh;
                
                
                // Check if mesh is scriptable and set to black background color
                if (mesh.IsScriptable)
                {
                    SetPreviewBackgroundBlockVisible(gridX, gridY);
                }
                else
                {
                    Log.Write(LogLevel.Error, string.Format("TetrisGame: Preview background block ({0}, {1}): Mesh is not scriptable", gridX, gridY));
                }
            }
            else
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Preview background block ({0}, {1}): No MeshComponent found", gridX, gridY));
            }
            
            break; // Only need the first object
        }
        
        previewBackgroundBlocksSpawned++;
    }
    
    private void SpawnPreviewGridBlocks()
    {
        Log.Write("TetrisGame: Starting preview grid block spawning...");
        
        // Spawn blocks in batches to prevent throttling
        for (int batchStart = 0; batchStart < totalPreviewBlocksToSpawn; batchStart += BatchSize)
        {
            SpawnPreviewBatch(batchStart);
            
            // Wait between batches
            if (batchStart + BatchSize < totalPreviewBlocksToSpawn)
            {
                Wait(TimeSpan.FromSeconds(BatchDelay));
            }
        }
    }
    
    private void SpawnPreviewBatch(int startIndex)
    {
        int endIndex = Math.Min(startIndex + BatchSize, totalPreviewBlocksToSpawn);
        
        for (int i = startIndex; i < endIndex; i++)
        {
            int x = i % PREVIEW_WIDTH;
            int y = i / PREVIEW_WIDTH;
            
            SpawnPreviewBlockAt(x, y);
        }
        
        Log.Write(string.Format("TetrisGame: Spawned preview batch: blocks {0} to {1}", startIndex, endIndex - 1));
    }
    
    private void SpawnPreviewBlockAt(int gridX, int gridY)
    {
        Sansar.Vector worldPosition = GridToWorldPreview(gridX, gridY);
        
        try
        {
            ScenePrivate.CreateCluster(GenericBlock, worldPosition, Sansar.Quaternion.Identity, Sansar.Vector.Zero,
                (ScenePrivate.CreateClusterData data) =>
                {
                    if (data.Success && data.ClusterReference != null)
                    {
                        OnPreviewBlockSpawned(data.ClusterReference, gridX, gridY);
                    }
                    else
                    {
                        Log.Write(LogLevel.Error, string.Format("TetrisGame: Failed to spawn preview block at ({0}, {1}): {2}", gridX, gridY, data.Message));
                    }
                });
        }
        catch (ThrottleException)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Throttled preview block at ({0}, {1}) - retrying in 0.5 seconds", gridX, gridY));
            StartCoroutine(() => RetrySpawnPreviewBlock(gridX, gridY));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Exception spawning preview block at ({0}, {1}): {2}", gridX, gridY, ex.Message));
        }
    }
    
    private void RetrySpawnPreviewBlock(int gridX, int gridY)
    {
        Wait(TimeSpan.FromSeconds(0.5));
        SpawnPreviewBlockAt(gridX, gridY);
    }
    
    private void OnPreviewBlockSpawned(Cluster cluster, int gridX, int gridY)
    {
        // Store cluster reference for cleanup
        spawnedPreviewClusters.Add(cluster);
        
        // Store object and mesh references for direct control
        foreach (var objectPrivate in cluster.GetObjectPrivates())
        {
            // Store object reference
            previewBlockObjects[gridX, gridY] = objectPrivate;
            
            // Get and store mesh component for direct material control
            MeshComponent mesh;
            if (objectPrivate.TryGetFirstComponent(out mesh))
            {
                previewBlockMeshes[gridX, gridY] = mesh;
                
                
                // Check if mesh is scriptable and set initial hidden state
                if (mesh.IsScriptable)
                {
                    SetPreviewBlockHidden(gridX, gridY);
                }
                else
                {
                    Log.Write(LogLevel.Error, string.Format("TetrisGame: Preview block ({0}, {1}): Mesh is not scriptable", gridX, gridY));
                }
            }
            else
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Preview block ({0}, {1}): No MeshComponent found", gridX, gridY));
            }
            
            break; // Only need the first object
        }
        
        previewBlocksSpawned++;
        
        if (previewBlocksSpawned >= totalPreviewBlocksToSpawn)
        {
            OnPreviewInitializationComplete();
        }
    }
    
    private void OnPreviewInitializationComplete()
    {
        previewInitialized = true;
        Log.Write("TetrisGame: Preview area initialization complete - all preview blocks spawned and ready");
    }
    
    // Convert grid coordinates to world position for preview area
    private Sansar.Vector GridToWorldPreview(int gridX, int gridY)
    {
        return new Sansar.Vector(
            GridOrigin.X + ((PREVIEW_OFFSET_X + gridX) * BLOCK_SIZE),
            GridOrigin.Y,
            GridOrigin.Z + ((PREVIEW_OFFSET_Y + gridY) * BLOCK_SIZE)
        );
    }
    
    // Convert grid coordinates to world position for preview background blocks
    private Sansar.Vector GridToWorldPreviewBackground(int gridX, int gridY)
    {
        return new Sansar.Vector(
            GridOrigin.X + ((PREVIEW_OFFSET_X + gridX) * BLOCK_SIZE),
            GridOrigin.Y + 1.0f, // Behind preview blocks
            GridOrigin.Z + ((PREVIEW_OFFSET_Y + gridY) * BLOCK_SIZE)
        );
    }
    
    #endregion
    
    #region Direct Material Control Methods
    
    public void SetBlockHidden(int gridX, int gridY)
    {
        if (gridX < 0 || gridX >= GRID_WIDTH || gridY < 0 || gridY >= GRID_HEIGHT)
            return;
        
        MeshComponent mesh = blockMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            try
            {
                // Use proper Sansar visibility control - makes block completely invisible
                mesh.SetIsVisible(false);
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Error hiding block ({0}, {1}): {2}", gridX, gridY, ex.Message));
            }
        }
    }
    
    public void SetBlockVisible(int gridX, int gridY, int pieceType, bool isActivePiece = false)
    {
        if (gridX < 0 || gridX >= GRID_WIDTH || gridY < 0 || gridY >= GRID_HEIGHT)
            return;
        
        if (!PieceColors.ContainsKey(pieceType))
            return;
        
        MeshComponent mesh = blockMeshes[gridX, gridY];
        if (mesh == null)
        {
            if (isActivePiece)
            {
                Log.Write(LogLevel.Warning, string.Format("TetrisGame: Block ({0},{1}) mesh reference is null in blockMeshes array", gridX, gridY));
            }
            return;
        }
        
        if (!mesh.IsScriptable)
        {
            if (isActivePiece)
            {
                Log.Write(LogLevel.Warning, string.Format("TetrisGame: Block ({0},{1}) mesh is not scriptable", gridX, gridY));
            }
            return;
        }
        
        try
        {
            // First make the block visible
            mesh.SetIsVisible(true);
            
            // Then set its color and properties
            Sansar.Color blockColor = PieceColors[pieceType];
            
            foreach (var material in mesh.GetRenderMaterials())
            {
                var props = material.GetProperties();
                
                if (material.HasTint)
                {
                    props.Tint = blockColor;
                }
                
                if (material.HasEmissiveIntensity)
                {
                    props.EmissiveIntensity = isActivePiece ? 2.0f : 0.0f;
                }
                
                material.SetProperties(props);
            }
            
            // Debug: Log successful material changes for active pieces
            // if (isActivePiece)
            // {
            //     Log.Write(string.Format("TetrisGame: Successfully set block ({0},{1}) visible with color {2}, emissive: {3}", 
            //         gridX, gridY, blockColor, isActivePiece ? 2.0f : 0.0f));
            // }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error showing block ({0}, {1}): {2}", gridX, gridY, ex.Message));
        }
    }

    // set block color
    public void SetBlockColor(int gridX, int gridY, int pieceType)
    {
        if (gridX < 0 || gridX >= GRID_WIDTH || gridY < 0 || gridY >= GRID_HEIGHT)
            return;
        
        if (!PieceColors.ContainsKey(pieceType))
            return;
        
        MeshComponent mesh = blockMeshes[gridX, gridY];
        if (mesh == null)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Block ({0},{1}) mesh reference is null in blockMeshes array", gridX, gridY));
            return;
        }
        
        if (!mesh.IsScriptable)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Block ({0},{1}) mesh is not scriptable", gridX, gridY));
            return;
        }
        
        try
        {
            Sansar.Color blockColor = PieceColors[pieceType];
            
            foreach (var material in mesh.GetRenderMaterials())
            {
                var props = material.GetProperties();
                
                if (material.HasTint)
                {
                    props.Tint = blockColor;
                }
                
                material.SetProperties(props);
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error setting block color ({0}, {1}): {2}", gridX, gridY, ex.Message));
        }
    }
    
    public void FlashBlock(int gridX, int gridY, float duration = 0.5f)
    {
        if (gridX < 0 || gridX >= GRID_WIDTH || gridY < 0 || gridY >= GRID_HEIGHT)
            return;

        MeshComponent mesh = blockMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            try
            {
                foreach (var material in mesh.GetRenderMaterials())
                {
                    var props = material.GetProperties();

                    if (material.HasTint)
                    {
                        props.Tint = Sansar.Color.White;
                    }

                    if (material.HasEmissiveIntensity)
                    {
                        props.EmissiveIntensity = 15.0f;
                    }

                    material.SetProperties(props);
                }

                // Return to hidden state after duration
                StartCoroutine(() => DelayedHideBlock(gridX, gridY, duration));
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Error flashing block ({0}, {1}): {2}", gridX, gridY, ex.Message));
            }
        }
    }
    
    private void DelayedHideBlock(int gridX, int gridY, float delay)
    {
        Wait(TimeSpan.FromSeconds(delay));
        SetBlockHidden(gridX, gridY);
    }
    
    #endregion
    
    #region Collision Management Methods
    
    /// <summary>
    /// Set collision behavior for a specific game block
    /// </summary>
    /// <param name="gridX">Grid X position</param>
    /// <param name="gridY">Grid Y position</param>
    /// <param name="enableCollision">True = solid collision, False = pass through</param>
    private void SetBlockCollision(int gridX, int gridY, bool enableCollision)
    {
        if (gridX < 0 || gridX >= GRID_WIDTH || gridY < 0 || gridY >= GRID_HEIGHT)
            return;
        
        RigidBodyComponent rigidBody = blockRigidBodies[gridX, gridY];
        if (rigidBody != null)
        {
            // Debug logging to trace collision changes
            // Log.Write(string.Format("TetrisGame: Setting collision for block ({0},{1}) - enableCollision: {2}, players: {3}", 
            //     gridX, gridY, enableCollision, allPlayers.Count));
            
            // Update collision for all players in the scene
            foreach (var player in allPlayers)
            {
                if (player != null && player.IsValid)
                {
                    // true = ignore collision (pass through), false = enable collision (solid)
                    player.IgnoreCollisionWith(rigidBody, !enableCollision);
                }
            }
        }
        else
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: No RigidBodyComponent found for block ({0},{1})", gridX, gridY));
        }
    }
    
    
    #endregion
    
    #region Preview Visual Control Methods
    
    public void SetPreviewBlockHidden(int gridX, int gridY)
    {
        if (gridX < 0 || gridX >= PREVIEW_WIDTH || gridY < 0 || gridY >= PREVIEW_HEIGHT)
            return;
        
        MeshComponent mesh = previewBlockMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            try
            {
                mesh.SetIsVisible(false);
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Error hiding preview block ({0}, {1}): {2}", gridX, gridY, ex.Message));
            }
        }
    }
    
    public void SetPreviewBlockVisible(int gridX, int gridY, int pieceType)
    {
        if (gridX < 0 || gridX >= PREVIEW_WIDTH || gridY < 0 || gridY >= PREVIEW_HEIGHT)
            return;
        
        if (!PieceColors.ContainsKey(pieceType))
            return;
        
        MeshComponent mesh = previewBlockMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            try
            {
                // First make the block visible
                mesh.SetIsVisible(true);
                
                // Then set its color
                Sansar.Color blockColor = PieceColors[pieceType];
                
                foreach (var material in mesh.GetRenderMaterials())
                {
                    var props = material.GetProperties();
                    
                    if (material.HasTint)
                    {
                        props.Tint = blockColor;
                    }
                    
                    if (material.HasEmissiveIntensity)
                    {
                        props.EmissiveIntensity = 0.0f; // No glow for preview
                    }
                    
                    material.SetProperties(props);
                }
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Error showing preview block ({0}, {1}): {2}", gridX, gridY, ex.Message));
            }
        }
    }
    
    public void SetPreviewBackgroundBlockVisible(int gridX, int gridY)
    {
        if (gridX < 0 || gridX >= PREVIEW_WIDTH || gridY < 0 || gridY >= PREVIEW_HEIGHT)
            return;
        
        MeshComponent mesh = previewBackgroundMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            try
            {
                // Make the background block visible with black color
                mesh.SetIsVisible(true);
                
                foreach (var material in mesh.GetRenderMaterials())
                {
                    var props = material.GetProperties();
                    
                    if (material.HasTint)
                    {
                        props.Tint = BackgroundColor;
                    }
                    
                    if (material.HasEmissiveIntensity)
                    {
                        props.EmissiveIntensity = 0.0f; // No glow for background
                    }
                    
                    material.SetProperties(props);
                }
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Error showing preview background block ({0}, {1}): {2}", gridX, gridY, ex.Message));
            }
        }
    }
    
    public void ClearPreview()
    {
        // Hide all preview blocks
        for (int x = 0; x < PREVIEW_WIDTH; x++)
        {
            for (int y = 0; y < PREVIEW_HEIGHT; y++)
            {
                SetPreviewBlockHidden(x, y);
            }
        }
    }
    
    public void ShowNextPiece(PieceType pieceType)
    {
        // Clear current preview
        ClearPreview();
        
        // Get piece shape for rotation 0 (default orientation)
        Vector[] relativePattern = PieceShapes[pieceType][0];
        
        // Calculate center position for 4x4 preview area
        Vector centerOffset = new Vector(1.5f, 1.5f, 0); // Center of 4x4 grid
        
        // Show each block of the piece
        foreach (var blockOffset in relativePattern)
        {
            int x = (int)(centerOffset.X + blockOffset.X);
            int y = (int)(centerOffset.Y + blockOffset.Y);
            
            // Make sure block is within preview bounds
            if (x >= 0 && x < PREVIEW_WIDTH && y >= 0 && y < PREVIEW_HEIGHT)
            {
                SetPreviewBlockVisible(x, y, (int)pieceType);
            }
        }
        
        Log.Write(string.Format("TetrisGame: Updated preview to show {0} piece", pieceType));
    }
    
    #endregion
    
    #region Rainbow Debug Mode
    
    private void RainbowDebugSequence()
    {
        Log.Write("TetrisGame: Rainbow debug sequence starting - cycling through all colors and effects");
        
        // Phase 1: Show all blocks visible in different colors
        for (int colorCycle = 0; colorCycle < 7; colorCycle++) // 7 Tetris piece types
        {
            Log.Write(string.Format("TetrisGame: Rainbow cycle {0} - setting all blocks to color {1}", colorCycle + 1, colorCycle));
            
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                for (int y = 0; y < GRID_HEIGHT; y++)
                {
                    SetBlockVisible(x, y, colorCycle, false);
                }
            }
            
            Wait(TimeSpan.FromSeconds(1.0)); // Hold each color for 1 second
        }
        
        // Phase 2: Reset to hidden state
        Log.Write("TetrisGame: Rainbow debug complete - resetting all blocks to hidden");
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                SetBlockHidden(x, y);
            }
        }
        
        Log.Write("TetrisGame: Rainbow debug sequence completed - material control testing finished");
    }
    
    #endregion
    
    #region Grid State and Collision Detection
    
    public bool IsBlockOccupied(int x, int y)
    {
        // A block is occupied if it has a piece type AND is not part of the active piece
        return blockStates[x, y].PieceType.HasValue && !blockStates[x, y].IsActivePiece;
    }

    public bool IsBlockInBounds(int x, int y)
    {
        return (x >= 0 && x < GRID_WIDTH && y >= 0 && y < GRID_HEIGHT);
    }
    
    public void SetBlock(int x, int y, int pieceType, bool isActivePiece = false)
    {
        if (IsBlockInBounds(x, y))
        {
            // Update block state
            BlockState block = blockStates[x, y];
            block.PieceType = pieceType;
            block.IsVisible = true;
            block.IsActivePiece = isActivePiece;
            blockStates[x, y] = block;
            
            // Use direct material control
            SetBlockVisible(x, y, pieceType, isActivePiece);
        }
    }
    
    public void ClearBlock(int x, int y)
    {
        if (IsBlockInBounds(x, y))
        {
            // Update block state
            BlockState block = blockStates[x, y];
            block.PieceType = null;
            block.IsVisible = false;
            block.IsActivePiece = false;
            block.FlashMode = FlashType.None;
            blockStates[x, y] = block;
            
            // Use direct material control
            SetBlockHidden(x, y);
        }
    }
    
    public void ClearGrid()
    {
        if (!gridInitialized)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Grid not initialized yet - cannot clear");
            return;
        }
        
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                ClearBlock(x, y);
            }
        }
        
        Log.Write("TetrisGame: Grid cleared for new game");
    }
    
    public bool IsGameOver()
    {
        if (!gridInitialized) return false;
        
        // Check if any LOCKED blocks are in spawn area (not active pieces)
        // Check top 4 rows (Y=16-19) to include spawn area and safety margin
        for (int y = GRID_HEIGHT - 4; y < GRID_HEIGHT; y++)
        {
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                // Only count locked pieces, not active piece remnants
                if (blockStates[x, y].PieceType.HasValue && !blockStates[x, y].IsActivePiece)
                {
                    Log.Write(string.Format("TetrisGame: Game over detected - LOCKED block found at ({0}, {1})", x, y));
                    return true;
                }
            }
        }
        return false;
    }
    
    #endregion
    
    #region Piece Logic and Collision Detection
    
    // Get the block positions for a piece at a specific position and rotation
    private Vector[] GetPieceBlocks(PieceType pieceType, int rotation, Vector centerPosition)
    {
        rotation = rotation % 4;
        if (rotation < 0) rotation += 4;
        
        Vector[] relativePattern = PieceShapes[pieceType][rotation];
        Vector[] absolutePositions = new Vector[relativePattern.Length];
        
        for (int i = 0; i < relativePattern.Length; i++)
        {
            absolutePositions[i] = new Vector(
                centerPosition.X + relativePattern[i].X,
                centerPosition.Y + relativePattern[i].Y,
                centerPosition.Z + relativePattern[i].Z
            );
        }
        
        return absolutePositions;
    }
    
    // FIXED COLLISION DETECTION: Now has access to grid state!
    public bool CanPlacePiece(PieceType pieceType, int rotation, Vector position)
    {
        Vector[] blockPositions = GetPieceBlocks(pieceType, rotation, position);
        
        foreach (var blockPos in blockPositions)
        {
            int x = (int)blockPos.X;
            int y = (int)blockPos.Y;

            // Check if position is in bounds first, then check occupation
            if (!IsBlockInBounds(x, y) || IsBlockOccupied(x, y))
            {
                return false;
            }
        }
        
        return true;
    }
    
    public bool TryRotatePiece(PieceType pieceType, Vector currentPosition, int currentRotation, out int newRotation)
    {
        newRotation = (currentRotation + 1) % 4;
        
        // Try basic rotation
        if (CanPlacePiece(pieceType, newRotation, currentPosition))
        {
            return true;
        }
        
        // Try wall kicks (simple implementation)
        Vector[] wallKicks = { new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0) };
        
        foreach (var kick in wallKicks)
        {
            Vector kickPosition = new Vector(
                currentPosition.X + kick.X,
                currentPosition.Y + kick.Y,
                currentPosition.Z + kick.Z
            );
            if (CanPlacePiece(pieceType, newRotation, kickPosition))
            {
                return true;
            }
        }
        
        newRotation = currentRotation;
        return false;
    }
    
    #endregion
    
    #region Game Logic and Flow
    
    private void StartNewGame()
    {
        if (!gridInitialized)
        {
            Log.Write("TetrisGame: Cannot start game - grid not initialized");
            return;
        }
        
        // Clean up any existing control subscriptions before starting (prevents double subscription bug)
        UnsubscribeFromPlayerControls();
        
        currentState = GameState.Playing;
        score = 0;
        level = 1;
        linesCleared = 0;
        dropInterval = 1.0f;
        
        // Clear game over flash state is now handled by ResetGameVariables
        
        // Reset game variables (preserving block references) and clear visuals
        ResetGameVariables();
        ClearGrid();
        
        // Subscribe to player controls (now guaranteed to be clean)
        SubscribeToPlayerControls();
        
        // Initialize next piece system
        if (previewInitialized)
        {
            InitializeNextPieceSystem();
        }
        
        // Spawn first piece
        SpawnNewPiece();
        
        Log.Write("TetrisGame: Game started");
    }
    
    private void SubscribeToPlayerControls()
    {
        Log.Write("TetrisGame: SubscribeToPlayerControls called");
        
        if (currentPlayer == null || !currentPlayer.IsValid)
        {
            Log.Write("TetrisGame: No valid player to subscribe to commands");
            return;
        }
        
        Log.Write("TetrisGame: Player validation passed, proceeding with subscriptions");
        
        try
        {
            Log.Write("TetrisGame: Starting command subscriptions");
            
            // Subscribe to player commands using the proper agent.Client.SubscribeToCommand pattern
            Log.Write("TetrisGame: Subscribing to Keypad4 (A key)");
            commandSubscriptions[0] = currentPlayer.Client.SubscribeToCommand("Keypad4", CommandAction.Pressed, HandleMoveLeftPressed, null);     // A key → Move left
            
            Log.Write("TetrisGame: Subscribing to Keypad6 (D key)");
            commandSubscriptions[1] = currentPlayer.Client.SubscribeToCommand("Keypad6", CommandAction.Pressed, HandleMoveRightPressed, null);    // D key → Move right  
            
            Log.Write("TetrisGame: Subscribing to Keypad2 (S key)");
            commandSubscriptions[2] = currentPlayer.Client.SubscribeToCommand("Keypad2", CommandAction.Pressed, HandleSoftDrop, null);     // S key → Soft drop
            
            Log.Write("TetrisGame: Subscribing to Keypad8 (W key)");
            commandSubscriptions[3] = currentPlayer.Client.SubscribeToCommand("Keypad8", CommandAction.Pressed, HandleSlowDown, null);     // W key → Slow down
            
            Log.Write("TetrisGame: Subscribing to SecondaryAction (R key)");
            commandSubscriptions[4] = currentPlayer.Client.SubscribeToCommand("SecondaryAction", CommandAction.Pressed, HandleRotate, null); // R key → Rotate
            
            Log.Write("TetrisGame: Subscribing to PrimaryAction (F key)");
            commandSubscriptions[5] = currentPlayer.Client.SubscribeToCommand("PrimaryAction", CommandAction.Pressed, HandleHardDrop, null); // F key → Hard drop
            
            Log.Write("TetrisGame: Subscribing to Action1 (1 key)");
            commandSubscriptions[6] = currentPlayer.Client.SubscribeToCommand("Action1", CommandAction.Pressed, HandleRotateCounterClockwise, null); // 1 key → Rotate CCW
            
            // Subscribe to key releases for repeat system
            Log.Write("TetrisGame: Subscribing to key releases for repeat system");
            commandSubscriptions[7] = currentPlayer.Client.SubscribeToCommand("Keypad4", CommandAction.Released, HandleMoveLeftReleased, null);     // A key release
            commandSubscriptions[8] = currentPlayer.Client.SubscribeToCommand("Keypad6", CommandAction.Released, HandleMoveRightReleased, null);    // D key release
            commandSubscriptions[9] = currentPlayer.Client.SubscribeToCommand("Keypad2", CommandAction.Released, HandleSoftDropReleased, null);    // S key release
            
            Log.Write("TetrisGame: Player controls activated using agent.Client.SubscribeToCommand");
            Log.Write("TetrisGame: Controls: A=Left, D=Right, S=SoftDrop, W=SlowDown, R=Rotate, F=HardDrop, 1=RotateCCW");
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error subscribing to player controls: {0}", ex.Message));
            // Continue anyway - some controls might work
        }
    }
    
    // Sansar command handlers using proper CommandData pattern
    private void HandleMoveLeftPressed(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        // Immediate move on press
        MovePieceLeft();
        
        // Set repeat state
        leftKeyHeld = true;
        leftKeyRepeatCount = 0;
    }
    
    private void HandleMoveLeftReleased(CommandData data)
    {
        // Stop repeat
        leftKeyHeld = false;
        leftKeyRepeatCount = 0;
    }
    
    private void HandleMoveRightPressed(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        // Immediate move on press
        MovePieceRight();
        
        // Set repeat state
        rightKeyHeld = true;
        rightKeyRepeatCount = 0;
    }
    
    private void HandleMoveRightReleased(CommandData data)
    {
        // Stop repeat
        rightKeyHeld = false;
        rightKeyRepeatCount = 0;
    }
    
    private void HandleSoftDropReleased(CommandData data)
    {
        // Stop repeat
        softDropKeyHeld = false;
        softDropKeyRepeatCount = 0;
    }
    
    private void HandleSoftDrop(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        // Immediate move on press
        MovePieceDown(true);  // triggeredByKey = true for soft drop
        
        // Set repeat state
        softDropKeyHeld = true;
        softDropKeyRepeatCount = 0;
    }
    
    private void HandleSlowDown(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        Log.Write("TetrisGame: W key (Keypad8) pressed - Slowing down piece");
        lastDropTime = Stopwatch.GetTimestamp(); // Reset drop timer
    }
    
    private void HandleRotate(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        Log.Write("TetrisGame: R key (SecondaryAction) pressed - Rotating clockwise");
        RotatePiece(1);
    }
    
    private void HandleHardDrop(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        Log.Write("TetrisGame: F key (PrimaryAction) pressed - Hard drop");
        HardDrop();
    }
    
    private void HandleRotateCounterClockwise(CommandData data)
    {
        if (currentState != GameState.Playing || currentPieceBlocks == null) return;
        
        Log.Write("TetrisGame: 1 key (Action1) pressed - Rotating counter-clockwise");
        RotatePiece(-1);
    }
    
    private void GameLoop()
    {
        int loopIteration = 0;
        while (true)
        {
            loopIteration++;
            if (currentState == GameState.Playing && currentPieceBlocks != null && !isHardDropping)
            {
                // Auto-drop
                double elapsedSeconds = (double)(Stopwatch.GetTimestamp() - lastDropTime) / Stopwatch.Frequency;
                if (elapsedSeconds >= dropInterval)
                {
                    if (!MovePieceDown())
                    {
                        Log.Write("TetrisGame: GameLoop - Piece cannot move down, calling OnPieceLanded");
                        OnPieceLanded();
                    }
                    lastDropTime = Stopwatch.GetTimestamp();
                }
                // Key repeat
                HandleKeyRepeat();
            }

            // Logic-derived active flags must be set before visuals
            if (currentState == GameState.Playing)
            {
                UpdateActivePieceFlags();
            }

            // Visual sync
            if (currentState == GameState.Playing || currentState == GameState.GameOver)
            {
                SyncAllVisualsToCompleteState();
            }

            Wait(TimeSpan.FromSeconds(0.1)); // 10 FPS
        }
    }
    
    private void HandleKeyRepeat()
    {
        try
        {
            // Handle left key repeat using simple counter approach
            if (leftKeyHeld)
            {
                leftKeyRepeatCount++;
                
                // After initial delay, repeat at regular intervals
                if (leftKeyRepeatCount > KEY_INITIAL_DELAY_CYCLES)
                {
                    // Check if it's time for a repeat (every KEY_REPEAT_RATE_CYCLES after initial delay)
                    int cyclesSinceInitialDelay = leftKeyRepeatCount - KEY_INITIAL_DELAY_CYCLES;
                    if (cyclesSinceInitialDelay % KEY_REPEAT_RATE_CYCLES == 0)
                    {
                        MovePieceLeft();
                    }
                }
            }
            
            // Handle right key repeat using simple counter approach
            if (rightKeyHeld)
            {
                rightKeyRepeatCount++;
                
                // After initial delay, repeat at regular intervals
                if (rightKeyRepeatCount > KEY_INITIAL_DELAY_CYCLES)
                {
                    // Check if it's time for a repeat (every KEY_REPEAT_RATE_CYCLES after initial delay)
                    int cyclesSinceInitialDelay = rightKeyRepeatCount - KEY_INITIAL_DELAY_CYCLES;
                    if (cyclesSinceInitialDelay % KEY_REPEAT_RATE_CYCLES == 0)
                    {
                        MovePieceRight();
                    }
                }
            }
            
            // Handle soft drop key repeat using simple counter approach  
            if (softDropKeyHeld)
            {
                softDropKeyRepeatCount++;
                
                // After initial delay, repeat at regular intervals
                if (softDropKeyRepeatCount > KEY_INITIAL_DELAY_CYCLES)
                {
                    // Check if it's time for a repeat (every KEY_REPEAT_RATE_CYCLES after initial delay)
                    int cyclesSinceInitialDelay = softDropKeyRepeatCount - KEY_INITIAL_DELAY_CYCLES;
                    if (cyclesSinceInitialDelay % KEY_REPEAT_RATE_CYCLES == 0)
                    {
                        MovePieceDown(true);  // triggeredByKey = true for soft drop repeat
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error in HandleKeyRepeat: {0}", ex.Message));
            // Reset key states on error to prevent infinite loops
            leftKeyHeld = false;
            rightKeyHeld = false;
            softDropKeyHeld = false;
            leftKeyRepeatCount = 0;
            rightKeyRepeatCount = 0;
            softDropKeyRepeatCount = 0;
        }
    }
    
    private void SpawnNewPiece()
    {
        Log.Write("TetrisGame: SpawnNewPiece - Starting");
        
        // Use next piece as current piece, or generate random if first spawn
        if (nextPieceInitialized)
        {
            currentPieceType = nextPieceType;
            Log.Write(string.Format("TetrisGame: SpawnNewPiece - Using next piece type {0}", currentPieceType));
        }
        else
        {
            // First piece - generate random
            PieceType[] pieceTypes = { PieceType.I, PieceType.O, PieceType.T, PieceType.S, PieceType.Z, PieceType.J, PieceType.L };
            currentPieceType = pieceTypes[randomGenerator.Next(pieceTypes.Length)];
            nextPieceInitialized = true; // Mark system as initialized
            Log.Write(string.Format("TetrisGame: SpawnNewPiece - First piece, selected random type {0}", currentPieceType));
        }
        
        // Generate next piece and update preview
        GenerateNextPiece();
        
        currentPieceRotation = 0;
        
        // Get spawn position (center top with safety margin for piece extensions)
        currentPiecePosition = new Vector(5, 18, 0);
        
        Log.Write("TetrisGame: SpawnNewPiece - About to call CanPlacePiece");
        
        // Debug: Check if the spawn area is clear
        Log.Write("TetrisGame: Checking spawn area (Y=16-19) before spawning...");
        int occupiedCount = 0;
        for (int y = GRID_HEIGHT - 4; y < GRID_HEIGHT; y++)
        {
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                if (IsBlockOccupied(x, y))
                {
                    Log.Write(string.Format("TetrisGame: WARNING - Found occupied block in spawn area at ({0}, {1})", x, y));
                    occupiedCount++;
                }
            }
        }

        if (occupiedCount == 0)
        {
            Log.Write("TetrisGame: Spawn area is clear - safe to spawn");
        }
        else
        {
            Log.Write(string.Format("TetrisGame: WARNING - {0} occupied blocks found in spawn area", occupiedCount));
        }
        
        // PROPER GAME OVER CHECK: Can the new piece spawn without collision?
        if (!CanPlacePiece(currentPieceType, currentPieceRotation, currentPiecePosition))
        {
            Log.Write("TetrisGame: GAME OVER - New piece collides with existing locked pieces");
            // Debug: Log why we can't spawn
            Log.Write(string.Format("TetrisGame: Cannot spawn piece {0} at {1} - collision detected", currentPieceType, currentPiecePosition));
            Vector[] testBlocks = GetPieceBlocks(currentPieceType, currentPieceRotation, currentPiecePosition);
            foreach (var blockPos in testBlocks)
            {
                int x = (int)blockPos.X;
                int y = (int)blockPos.Y;
                Log.Write(string.Format("TetrisGame: Block at ({0}, {1}) - InBounds: {2}, Occupied: {3}", x, y, IsBlockInBounds(x, y), IsBlockOccupied(x, y)));
            }
            
            // FORCE spawn the piece anyway for visual feedback
            Log.Write("TetrisGame: Force spawning final piece for game over visualization");
            currentPieceBlocks = GetPieceBlocks(currentPieceType, currentPieceRotation, currentPiecePosition);
            
            // Game over - with final piece visible
            EndGame();
            return;
        }
        
        Log.Write("TetrisGame: SpawnNewPiece - CanPlacePiece returned true");
        
        // Calculate block positions
        currentPieceBlocks = GetPieceBlocks(currentPieceType, currentPieceRotation, currentPiecePosition);
        
        Log.Write(string.Format("TetrisGame: Successfully validated spawn position for {0} at {1}", currentPieceType, currentPiecePosition));
        
        // Active piece display now handled by visual update cycle
        
        Log.Write(string.Format("TetrisGame: Spawned new piece: {0} at {1} with {2} blocks", currentPieceType, currentPiecePosition, currentPieceBlocks.Length));
    }
    
    private void InitializeNextPieceSystem()
    {
        Log.Write("TetrisGame: Initializing next piece preview system");
        
        // Clear preview area
        ClearPreview();
        
        // Generate first next piece
        GenerateNextPiece();
        
        Log.Write("TetrisGame: Next piece preview system initialized");
    }
    
    private void GenerateNextPiece()
    {
        // Generate next piece
        PieceType[] pieceTypes = { PieceType.I, PieceType.O, PieceType.T, PieceType.S, PieceType.Z, PieceType.J, PieceType.L };
        nextPieceType = pieceTypes[randomGenerator.Next(pieceTypes.Length)];
        
        // Update preview display if preview area is ready
        if (previewInitialized)
        {
            ShowNextPiece(nextPieceType);
        }
        
        Log.Write(string.Format("TetrisGame: Generated next piece: {0}", nextPieceType));
    }
    
    private void ShowActivePiece(Vector[] blocks, int pieceType)
    {
        foreach (var blockPos in blocks)
        {
            int x = (int)blockPos.X;
            int y = (int)blockPos.Y;
            if (IsBlockInBounds(x, y))
            {
                SetBlockVisible(x, y, pieceType, true); // Active piece with glow
            }
        }
    }
    
    private void HideActivePiece(Vector[] blocks)
    {
        foreach (var blockPos in blocks)
        {
            int x = (int)blockPos.X;
            int y = (int)blockPos.Y;
            if (IsBlockInBounds(x, y))
            {
                // Only hide if it's not part of locked pieces
                if (blockStates[x, y].PieceType == null)
                {
                    SetBlockHidden(x, y);
                }
            }
        }
    }
    
    #endregion
    
    #region Piece Movement
    
    private bool MovePieceLeft()
    {
        Vector newPosition = new Vector(currentPiecePosition.X - 1, currentPiecePosition.Y, currentPiecePosition.Z);
        bool moved = TryMovePiece(newPosition, currentPieceRotation);
        
        // Play move sound if piece successfully moved
        if (moved)
        {
            PlayMoveSound(ObjectPrivate.Position);
        }
        
        return moved;
    }
    
    private bool MovePieceRight()
    {
        Vector newPosition = new Vector(currentPiecePosition.X + 1, currentPiecePosition.Y, currentPiecePosition.Z);
        bool moved = TryMovePiece(newPosition, currentPieceRotation);
        
        // Play move sound if piece successfully moved
        if (moved)
        {
            PlayMoveSound(ObjectPrivate.Position);
        }
        
        return moved;
    }
    
    private bool MovePieceDown(bool triggeredByKey = false)
    {
        Vector newPosition = new Vector(currentPiecePosition.X, currentPiecePosition.Y - 1, currentPiecePosition.Z);
        bool moved = TryMovePiece(newPosition, currentPieceRotation);
        
        // Play move sound only if piece successfully moved AND it was triggered by a key press
        if (moved && triggeredByKey)
        {
            PlayMoveSound(ObjectPrivate.Position);
        }
        
        return moved;
    }
    
    private bool TryMovePiece(Vector newPosition, int newRotation)
    {
        Vector oldPosition = currentPiecePosition;
        
        // FIXED: Now properly checks collision with locked pieces!
        if (!CanPlacePiece(currentPieceType, newRotation, newPosition))
        {
            Log.Write(string.Format("TetrisGame: TryMovePiece - CanPlacePiece returned false for new position {0} and rotation {1}", newPosition, newRotation));
            return false; // Cannot move to new position
        }
        
        // Pure data update - no visual calls needed
        currentPiecePosition = newPosition;
        currentPieceRotation = newRotation;
        currentPieceBlocks = GetPieceBlocks(currentPieceType, currentPieceRotation, currentPiecePosition);
        
        // Log.Write(string.Format("TetrisGame: TryMovePiece SUCCESS - moved from {0} to {1}, rotation {2}", oldPosition, newPosition, newRotation));
        return true;
    }
    
    private void RotatePiece(int direction)
    {
        int newRotation;
        bool rotated = false;
        
        if (direction > 0)
        {
            // Clockwise
            if (TryRotatePiece(currentPieceType, currentPiecePosition, currentPieceRotation, out newRotation))
            {
                rotated = TryMovePiece(currentPiecePosition, newRotation);
            }
        }
        else
        {
            // Counter-clockwise
            int targetRotation = currentPieceRotation - 1;
            if (targetRotation < 0) targetRotation = 3;
            
            if (CanPlacePiece(currentPieceType, targetRotation, currentPiecePosition))
            {
                rotated = TryMovePiece(currentPiecePosition, targetRotation);
            }
        }
        
        // Play rotate sound if piece successfully rotated
        if (rotated)
        {
            PlayRotateSound(ObjectPrivate.Position);
        }
    }
    
    private void HardDrop()
    {
        Log.Write(string.Format("TetrisGame: HardDrop starting - piece at {0}", currentPiecePosition));
        isHardDropping = true;
        
        // Drop piece as far down as possible
        int dropCount = 0;
        while (MovePieceDown())
        {
            dropCount++;
            Log.Write(string.Format("TetrisGame: HardDrop step {0} - piece now at {1}", dropCount, currentPiecePosition));
            // Keep dropping until it can't move down
        }
        
        Log.Write(string.Format("TetrisGame: HardDrop finished after {0} steps - final position {1}", dropCount, currentPiecePosition));
        isHardDropping = false;
        
        // Play hard drop sound if piece dropped at least one position
        if (dropCount > 0)
        {
            PlayHardDropSound(ObjectPrivate.Position);
        }
        
        // Piece has landed
        OnPieceLanded();
    }
    
    private void OnPieceLanded()
    {
        if (currentPieceBlocks == null) return;
    
        Log.Write("TetrisGame: Piece landed - locking into grid state");
    
        // Commit locked blocks
        foreach (var blockPos in currentPieceBlocks)
        {
            int x = (int)blockPos.X;
            int y = (int)blockPos.Y;
            if (IsBlockInBounds(x, y))
            {
                BlockState block = blockStates[x, y];
                block.PieceType = (int)currentPieceType;
                block.IsVisible = true;
                block.IsActivePiece = false; // no longer active
                blockStates[x, y] = block;
    
                Log.Write(string.Format("TetrisGame: Locked block at ({0}, {1}) as piece type {2}", x, y, currentPieceType));
            }
        }
    
        // Play lock piece sound
        PlayLockPieceSound(ObjectPrivate.Position);
    
        // Clear active piece
        currentPieceBlocks = null;
    
        // Check for completed lines (no waits; flash timed by FlashStartTime)
        Log.Write("TetrisGame: Starting line clearing check");
        int cleared = CheckAndClearLines();
        Log.Write(string.Format("TetrisGame: Line clearing complete - {0} lines cleared", cleared));
    
        if (cleared > 0)
        {
            UpdateScore(cleared);
        }
    
        // Spawn next immediately (no Wait)
        SpawnNewPiece();
        Log.Write("TetrisGame: OnPieceLanded complete");
    }
    
    #endregion
    
    #region Line Clearing
    
    public int CheckAndClearLines()
    {
        if (!gridInitialized)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Grid not initialized yet - cannot clear lines");
            return 0;
        }
        
        List<int> completedLines = new List<int>();
        
        // Check each row from bottom to top
        for (int y = 0; y < GRID_HEIGHT; y++)
        {
            if (IsLineComplete(y))
            {
                completedLines.Add(y);
            }
        }
        
        // Clear completed lines with visual effect
        if (completedLines.Count > 0)
        {
            StartCoroutine(() => LineClearSequence(completedLines));
        }
        
        return completedLines.Count;
    }
    
    private bool IsLineComplete(int y)
    {
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            if (blockStates[x, y].PieceType == null)
                return false;
        }
        return true;
    }
    
    private void LineClearSequence(List<int> completedLines)
    {
        // Phase 1: Flash intent only (timed by FlashStartTime)
        FlashCompletedLines(completedLines);
    
        // Play line clear sound based on number of lines
        if (completedLines.Count == 4)
        {
            PlayTetrisSound(ObjectPrivate.Position);
        }
        else if (completedLines.Count > 0)
        {
            PlayLineClearSound(ObjectPrivate.Position);
        }
    
        // No wait here; visual loop renders flash by time.
        // Phase 2 will be triggered by CheckAndClearLines immediate call path.
        ClearAndDropLines(completedLines);
    
        Log.Write(string.Format("TetrisGame: Cleared {0} lines", completedLines.Count));
    }
    
    private void FlashCompletedLines(List<int> lines)
    {
        // Clear all flash states first
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                BlockState block = blockStates[x, y];
                if (block.FlashMode == FlashType.LineClear)
                {
                    block.FlashMode = FlashType.None;
                    blockStates[x, y] = block;
                }
            }
        }
        
        // Set flash state for completed lines
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);
        foreach (int y in lines)
        {
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                BlockState block = blockStates[x, y];
                block.FlashMode = FlashType.LineClear;
                block.FlashStartTime = currentTime;
                blockStates[x, y] = block;
            }
        }
    }
    
    private void ClearLine(int y)
    {
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            BlockState block = blockStates[x, y];
            block.PieceType = null;
            block.IsVisible = false;
            block.FlashMode = FlashType.None;
            blockStates[x, y] = block;
            
            SetBlockHidden(x, y);
            
            // Disable collision for all players when clearing this block
            SetBlockCollision(x, y, false);
        }
    }
    
    private void ClearAndDropLines(List<int> completedLines)
    {
        // Create new block state grid with completed lines removed
        BlockState[,] newBlockStates = new BlockState[GRID_WIDTH, GRID_HEIGHT];
        
        // Initialize new grid with default block states
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                newBlockStates[x, y] = new BlockState
                {
                    PieceType = null,
                    IsVisible = false,
                    IsActivePiece = false,
                    FlashMode = FlashType.None,
                    FlashRate = 2.0f,
                    StateChangeTime = currentTime
                };
            }
        }
        
        int writeY = 0; // Where to write the next line in new grid
        
        // Copy non-completed lines to new grid state
        for (int readY = 0; readY < GRID_HEIGHT; readY++)
        {
            // Skip completed lines
            if (completedLines.Contains(readY))
            {
                Log.Write(string.Format("TetrisGame: Skipping completed line at Y={0}", readY));
                continue;
            }
            
            // Copy this line to the write position
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                BlockState sourceBlock = blockStates[x, readY];
                // Clear any line clear flash from the moved block
                if (sourceBlock.FlashMode == FlashType.LineClear)
                {
                    sourceBlock.FlashMode = FlashType.None;
                }
                newBlockStates[x, writeY] = sourceBlock;
            }
            
            // Log.Write(string.Format("TetrisGame: Moved line from Y={0} to Y={1}", readY, writeY));
            writeY++;
        }
        
        // Replace old grid state with new one
        blockStates = newBlockStates;
        
        // Visual sync now handled by independent update cycle in GameLoop
        
        Log.Write(string.Format("TetrisGame: Successfully cleared {0} lines and synced all visuals", completedLines.Count));
    }
    
    /// <summary>
    /// Logic-only: mark current active piece flags without mutating locked PieceType cells.
    /// </summary>
    private void UpdateActivePieceFlags()
    {
        // Clear previous active flags and visibility intent they owned
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                if (blockStates[x, y].IsActivePiece)
                {
                    BlockState b = blockStates[x, y];
                    b.IsActivePiece = false;
                    // Only clear visibility if cell isn't a locked piece
                    if (!b.PieceType.HasValue)
                    {
                        b.IsVisible = false;
                    }
                    blockStates[x, y] = b;
                }
            }
        }

        // Set current active piece flags and visibility intent
        if (currentPieceBlocks != null)
        {
            foreach (var p in currentPieceBlocks)
            {
                int x = (int)p.X;
                int y = (int)p.Y;
                if (IsBlockInBounds(x, y))
                {
                    BlockState b = blockStates[x, y];
                    b.IsActivePiece = true;
                    b.IsVisible = true; // visible while falling
                    // Do NOT set b.PieceType here; keep for locked blocks only
                    blockStates[x, y] = b;
                }
            }
        }
    }
    
    /// <summary>
    /// Visual update thread - updates ALL block visuals to match complete game state
    /// Runs independently from game logic every 0.1 seconds
    /// </summary>
    private void SyncAllVisualsToCompleteState()
    {
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);

        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                BlockState b = blockStates[x, y];

                // Determine desired intent
                bool desiredCollidable = b.IsVisible; // all visible are solid
                int desiredColorIndex = -1;
                float desiredEmissive = 0f;

                bool isFlashingLine = b.FlashMode == FlashType.LineClear;
                bool isFlashingGameOver = b.FlashMode == FlashType.GameOver;

                // Decide color index: locked piece type if present; else if active use currentPieceType
                if (b.IsVisible)
                {
                    if (b.PieceType.HasValue)
                    {
                        desiredColorIndex = b.PieceType.Value;
                    }
                    else if (b.IsActivePiece)
                    {
                        desiredColorIndex = (int)currentPieceType;
                    }
                }

                // Emissive policy
                if (b.IsActivePiece && b.IsVisible)
                {
                    desiredEmissive = 2.0f;
                }
                else
                {
                    desiredEmissive = 0.0f;
                }

                // Apply flashing visuals (overrides emissive and sometimes color)
                if (isFlashingLine)
                {
                    float t = b.GetTimeSinceFlashStart(currentTime);
                    if (t < 0.6f)
                    {
                        // Only update if flash mode changed or we weren't visible
                        if (b.LastFlashMode != FlashType.LineClear || !b.LastVisible)
                        {
                            SetFlashVisual(x, y);
                        }
                        // For collision, keep according to desiredVisible
                    }
                    else
                    {
                        // End flash
                        b.FlashMode = FlashType.None;
                    }
                }
                else if (isFlashingGameOver)
                {
                    float t = b.GetTimeSinceFlashStart(currentTime);
                    SetGameOverFlashVisual(x, y, t);
                }
                else
                {
                    // Normal rendering path
                    if (b.IsVisible)
                    {
                        // Only push visual change if any of the last-applied differ
                        if (!b.LastVisible || b.LastColorIndex != desiredColorIndex || Math.Abs(b.LastEmissiveIntensity - desiredEmissive) > 0.001f)
                        {
                            // If no valid color index, keep hidden safeguard
                            if (desiredColorIndex >= 0)
                            {
                                SetBlockVisible(x, y, desiredColorIndex, b.IsActivePiece);
                            }
                        }
                    }
                    else
                    {
                        if (b.LastVisible)
                        {
                            SetBlockHidden(x, y);
                        }
                    }
                }

                // Collision edge-apply
                if (b.LastCollidable != desiredCollidable)
                {
                    SetBlockCollision(x, y, desiredCollidable);
                }

                // Update Last* trackers
                b.LastVisible = b.IsVisible && desiredColorIndex >= 0;
                b.LastCollidable = desiredCollidable;
                b.LastColorIndex = desiredColorIndex;
                b.LastEmissiveIntensity = isFlashingLine || isFlashingGameOver ? b.LastEmissiveIntensity : desiredEmissive;
                b.LastFlashMode = b.FlashMode;

                // Maintain history timestamps for diagnostics
                b.UpdateHistory(currentTime);

                blockStates[x, y] = b;
            }
        }
    }
    
    private void SetFlashVisual(int gridX, int gridY)
    {
        MeshComponent mesh = blockMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            foreach (var material in mesh.GetRenderMaterials())
            {
                var props = material.GetProperties();
                if (material.HasTint) props.Tint = Sansar.Color.White;
                if (material.HasEmissiveIntensity) props.EmissiveIntensity = 15.0f;
                material.SetProperties(props);
            }
        }
    }
    
    private void SetGameOverFlashVisual(int gridX, int gridY, float timeSinceFlashStart)
    {
        MeshComponent mesh = blockMeshes[gridX, gridY];
        if (mesh != null && mesh.IsScriptable)
        {
            // Create a pulsing effect using sine wave with the flash rate from block state
            BlockState block = blockStates[gridX, gridY];
            float pulsePhase = (timeSinceFlashStart * block.FlashRate * 2.0f * 3.14159f); // Complete cycles based on flash rate
            float pulseIntensity = (float)Math.Sin(pulsePhase) * 0.5f + 0.5f; // 0.0 to 1.0
            
            // Get the piece type for this block to maintain its color
            int? pieceTypeInt = blockStates[gridX, gridY].PieceType;
            Sansar.Color baseColor = pieceTypeInt.HasValue ? PieceColors[pieceTypeInt.Value] : Sansar.Color.Red;
            
            foreach (var material in mesh.GetRenderMaterials())
            {
                var props = material.GetProperties();
                if (material.HasTint) props.Tint = baseColor;
                if (material.HasEmissiveIntensity) props.EmissiveIntensity = 2.0f + (pulseIntensity * 8.0f); // Pulse between 2.0 and 10.0
                material.SetProperties(props);
            }
        }
    }
    
    #endregion
    
    #region Scoring and Progression
    
    private void UpdateScore(int linesCleared)
    {
        // Simple scoring system
        int[] lineScores = { 0, 100, 300, 500, 800 }; // 0, 1, 2, 3, 4 lines
        if (linesCleared > 0 && linesCleared <= 4)
        {
            score += lineScores[linesCleared] * level;
        }
        
        this.linesCleared += linesCleared;
        
        // Level up every 10 lines
        if (this.linesCleared / 10 > level - 1)
        {
            level++;
            dropInterval = Math.Max(0.1f, dropInterval * 0.8f); // Speed up
            Log.Write(string.Format("TetrisGame: Level up! Now level {0}, drop interval: {1:F2}s", level, dropInterval));
        }
        
        Log.Write(string.Format("TetrisGame: Score: {0}, Level: {1}, Lines: {2}", score, level, this.linesCleared));
    }
    
    #endregion
    
    #region Game End
    
    private void EndGame()
    {
        currentState = GameState.GameOver;
        
        Log.Write(string.Format("TetrisGame: Game ended. Score: {0}, Lines: {1}, Level: {2}", score, linesCleared, level));
        
        // Play game over sound
        PlayGameOverSound(ObjectPrivate.Position);
        
        // Check if score qualifies for leaderboard
        if (currentPlayer != null && currentPlayer.IsValid && IsHighScore(score))
        {
            UpdateLeaderboard(currentPlayer, score, level, linesCleared);
            
            // Notify player of their achievement
            try
            {
                currentPlayer.SendChat(string.Format("Congratulations {0}! You made the top 10 with {1} points!", 
                    currentPlayer.AgentInfo.Handle, score));
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Warning, "TetrisGame: Error sending leaderboard notification: " + ex.Message);
            }
        }
        
        // Mark the final piece that caused game over for continuous flashing
        if (gridInitialized && currentPieceBlocks != null)
        {
            MarkFinalPieceForGameOverFlash();
        }
        
        // Reset only the piece state, keep grid visible for flashing effect
        currentPieceBlocks = null;
        currentPieceRotation = 0;
        
        // Reset key repeat states
        leftKeyHeld = false;
        rightKeyHeld = false;
        softDropKeyHeld = false;
        leftKeyRepeatCount = 0;
        rightKeyRepeatCount = 0;
        softDropKeyRepeatCount = 0;
        
        // Player controls cleanup (simplified implementation)
        Log.Write("TetrisGame: Player controls deactivated");
        
        // Note: Not automatically transitioning to WaitingForPlayer to prevent timing conflicts
        // Player can sit down again to restart when ready
        
        Log.Write("TetrisGame: Game ended - waiting for player to restart manually");
    }
    
    // End game without playing sound (for player stand up scenarios)
    private void EndGameQuietly()
    {
        currentState = GameState.GameOver;
        
        Log.Write(string.Format("TetrisGame: Game ended quietly (player left). Score: {0}, Lines: {1}, Level: {2}", score, linesCleared, level));
        
        // NO game over sound for player leaving
        
        // Check if score qualifies for leaderboard
        if (currentPlayer != null && currentPlayer.IsValid && IsHighScore(score))
        {
            UpdateLeaderboard(currentPlayer, score, level, linesCleared);
            
            // Notify player of their achievement  
            try
            {
                currentPlayer.SendChat(string.Format("Great job {0}! You made the top 10 with {1} points!", 
                    currentPlayer.AgentInfo.Handle, score));
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Warning, "TetrisGame: Error sending leaderboard notification: " + ex.Message);
            }
        }
        
        // Mark the final piece that caused game over for continuous flashing
        if (gridInitialized && currentPieceBlocks != null)
        {
            MarkFinalPieceForGameOverFlash();
        }
        
        // Reset only the piece state, keep grid visible for flashing effect
        currentPieceBlocks = null;
        currentPieceRotation = 0;
        
        // Reset key repeat states
        leftKeyHeld = false;
        rightKeyHeld = false;
        softDropKeyHeld = false;
        leftKeyRepeatCount = 0;
        rightKeyRepeatCount = 0;
        softDropKeyRepeatCount = 0;
        
        // Player controls cleanup (simplified implementation)
        Log.Write("TetrisGame: Player controls deactivated");
        
        // Note: Not automatically transitioning to WaitingForPlayer to prevent timing conflicts
        // Player can sit down again to restart when ready
        
        Log.Write("TetrisGame: Game ended quietly - waiting for player to restart manually");
    }
    
    private void MarkFinalPieceForGameOverFlash()
    {
        Log.Write("TetrisGame: Marking final piece blocks for continuous game over flash");
        
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);
        
        // Clear any existing game over flash state
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                if (blockStates[x, y].FlashMode == FlashType.GameOver)
                {
                    BlockState block = blockStates[x, y];
                    block.FlashMode = FlashType.None;
                    blockStates[x, y] = block;
                }
            }
        }
        
        // Mark the current piece blocks for flashing
        int blocksMarked = 0;
        foreach (var pieceBlock in currentPieceBlocks)
        {
            int x = (int)pieceBlock.X;
            int y = (int)pieceBlock.Y;
            
            if (IsBlockInBounds(x, y))
            {
                BlockState block = blockStates[x, y];
                block.PieceType = (int)currentPieceType; // Ensure piece type is set
                block.IsVisible = true; // Ensure it's visible
                block.IsActivePiece = false; // Not active, but should flash
                block.FlashMode = FlashType.GameOver;
                block.FlashStartTime = currentTime;
                block.FlashRate = 2.0f; // 2 Hz = 2 pulses per second
                blockStates[x, y] = block;
                
                blocksMarked++;
                Log.Write(string.Format("TetrisGame: Marked final piece block at ({0}, {1}) for game over flash", x, y));
            }
        }
        
        Log.Write(string.Format("TetrisGame: Game over flash enabled for {0} blocks", blocksMarked));
    }
    
    #endregion
    
    #region Cleanup
    
    public void CleanupGrid()
    {
        // Clean up main game blocks
        foreach (var cluster in spawnedClusters)
        {
            if (cluster != null && cluster.IsValid)
            {
                cluster.Destroy();
            }
        }
        spawnedClusters.Clear();
        
        // Clean up background blocks
        foreach (var cluster in spawnedBackgroundClusters)
        {
            if (cluster != null && cluster.IsValid)
            {
                cluster.Destroy();
            }
        }
        spawnedBackgroundClusters.Clear();
        
        // Clean up preview blocks
        foreach (var cluster in spawnedPreviewClusters)
        {
            if (cluster != null && cluster.IsValid)
            {
                cluster.Destroy();
            }
        }
        spawnedPreviewClusters.Clear();
        
        // Clean up preview background blocks
        foreach (var cluster in spawnedPreviewBackgroundClusters)
        {
            if (cluster != null && cluster.IsValid)
            {
                cluster.Destroy();
            }
        }
        spawnedPreviewBackgroundClusters.Clear();
        
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                blockObjects[x, y] = null;
                blockMeshes[x, y] = null;
                blockRigidBodies[x, y] = null;
                backgroundBlockObjects[x, y] = null;
                backgroundBlockMeshes[x, y] = null;
                
                // Reset block state
                blockStates[x, y] = new BlockState();
            }
        }
        
        // Clean up preview arrays
        for (int x = 0; x < PREVIEW_WIDTH; x++)
        {
            for (int y = 0; y < PREVIEW_HEIGHT; y++)
            {
                previewBlockObjects[x, y] = null;
                previewBlockMeshes[x, y] = null;
                previewBackgroundObjects[x, y] = null;
                previewBackgroundMeshes[x, y] = null;
            }
        }
        
        gridInitialized = false;
        previewInitialized = false;
        blocksSpawned = 0;
        backgroundBlocksSpawned = 0;
        previewBlocksSpawned = 0;
        previewBackgroundBlocksSpawned = 0;
        
        Log.Write("TetrisGame: Grid cleanup complete (including background grid and preview area)");
    }
    
    // Helper methods for external access
    public int GetWidth() { return GRID_WIDTH; }
    public int GetHeight() { return GRID_HEIGHT; }
    public bool IsGridReady() { return gridInitialized; }
    
    public int? GetBlock(int x, int y)
    {
        if (IsBlockInBounds(x, y))
        {
            return blockStates[x, y].PieceType;
        }
        return null;
    }
    
    #endregion
    
    #region Audio System
    
    // Convert loudness percentage to decibels
    private float LoudnessPercentToDb(float loudnessPercent)
    {
        loudnessPercent = Math.Min(Math.Max(loudnessPercent, 0.0f), 100.0f);
        return 60.0f * (loudnessPercent / 100.0f) - 48.0f;
    }
    
    // Audio playback methods
    private void PlayMoveSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayMoveSound called");
        if (MoveSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(MoveVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing move sound at volume {0}% (loudness: {1}dB)", MoveVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(MoveSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: MoveSound resource not set");
        }
    }
    
    private void PlayRotateSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayRotateSound called");
        if (RotateSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(RotateVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing rotate sound at volume {0}% (loudness: {1}dB)", RotateVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(RotateSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: RotateSound resource not set");
        }
    }
    
    private void PlayHardDropSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayHardDropSound called");
        if (HardDropSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(HardDropVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing hard drop sound at volume {0}% (loudness: {1}dB)", HardDropVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(HardDropSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: HardDropSound resource not set");
        }
    }
    
    private void PlayLineClearSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayLineClearSound called");
        if (LineClearSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(LineClearVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing line clear sound at volume {0}% (loudness: {1}dB)", LineClearVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(LineClearSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: LineClearSound resource not set");
        }
    }
    
    private void PlayTetrisSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayTetrisSound called");
        if (TetrisSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(TetrisVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing Tetris sound at volume {0}% (loudness: {1}dB)", TetrisVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(TetrisSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: TetrisSound resource not set");
        }
    }
    
    private void PlayLockPieceSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayLockPieceSound called");
        if (LockPieceSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(LockPieceVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing lock piece sound at volume {0}% (loudness: {1}dB)", LockPieceVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(LockPieceSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: LockPieceSound resource not set");
        }
    }
    
    private void PlayGameOverSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayGameOverSound called");
        if (GameOverSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(GameOverVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing game over sound at volume {0}% (loudness: {1}dB)", GameOverVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(GameOverSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: GameOverSound resource not set");
        }
    }
    
    #endregion
    
    #region Chat Interface System
    
    private void ShowWelcomeHint(AgentPrivate agent, SessionId sessionId)
    {
        // Check if agent is still valid
        if (agent != null && agent.IsValid)
        {
            try
            {
                // Set the hint text (truncated to 80 characters as per HintText.cs example)
                string hintText = "Welcome! Type /tet help in nearby chat for commands";
                if (hintText.Length > 80)
                {
                    hintText = hintText.Substring(0, 80);
                }
                
                agent.Client.UI.HintText = hintText;
                Log.Write(LogLevel.Info, "TetrisGame: Displayed welcome hint to " + agent.AgentInfo.Name);
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Warning, "TetrisGame: Error showing hint text: " + ex.Message);
            }
            
            // Wait 8 seconds
            Wait(TimeSpan.FromSeconds(8.0f));
            
            // Remove the hint text
            try
            {
                AgentPrivate currentAgent = ScenePrivate.FindAgent(sessionId);
                if (currentAgent != null && currentAgent.IsValid)
                {
                    currentAgent.Client.UI.HintText = null;
                    Log.Write(LogLevel.Info, "TetrisGame: Removed welcome hint from " + currentAgent.AgentInfo.Name);
                }
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Warning, "TetrisGame: Error removing hint text: " + ex.Message);
            }
        }
    }
    
    private void OnChatMessage(ChatData data)
    {
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Chat message received: '{0}' from sourceId: {1}", data.Message, data.SourceId));
        
        // Find the sender
        AgentPrivate sender = ScenePrivate.FindAgent(data.SourceId);
        if (sender == null)
        {
            Log.Write(LogLevel.Info, "TetrisGame: Could not find sender agent, ignoring chat");
            return;
        }
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Processing chat from: {0}", sender.AgentInfo.Name));
        
        // Parse the message for commands (must start with /tet)
        string message = data.Message.Trim().ToLower();
        string[] chatWords = message.Split(' ');
        
        if (chatWords.Length < 1)
            return;
            
        // Check if it's a /tet command
        if (chatWords[0] != "/tet")
        {
            Log.Write(LogLevel.Info, "TetrisGame: Message doesn't start with /tet, ignoring");
            return;
        }
        
        // Need at least 2 words: "/tet" and the subcommand
        if (chatWords.Length < 2)
        {
            Log.Write(LogLevel.Info, "TetrisGame: /tet command missing subcommand");
            return;
        }
        
        // Build the command for lookup - handle special cases for /tet bg volume
        string command;
        if (chatWords.Length >= 3 && chatWords[1] == "bg" && chatWords[2] == "volume")
        {
            command = chatWords[0] + " " + chatWords[1] + " " + chatWords[2];  // "/tet bg volume"
        }
        else
        {
            command = chatWords[0] + " " + chatWords[1];  // "/tet help", "/tet bg", etc.
        }
        
        if (!commandsUsage.ContainsKey(command))
        {
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Unknown /tet command: {0}", command));
            return;
        }
            
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Processing command '{0}' from {1}", command, sender.AgentInfo.Name));
        
        // Route to appropriate command handler
        try
        {
            // Check for public commands first (no seated player check)
            if (command == "/tet help" || command == "/tet leaderboard" || command == "/tet leader" ||
                command == "/tet bg" || command == "/tet bg volume")
            {
                switch (command)
                {
                    case "/tet help":
                        SendHelpMessage(sender);
                        break;
                    case "/tet leaderboard":
                    case "/tet leader":
                        SendLeaderboard(sender);
                        break;
                    case "/tet bg":
                        ToggleBackgroundMusic(sender);
                        break;
                    case "/tet bg volume":
                        if (chatWords.Length >= 4)
                        {
                            SetBackgroundMusicVolume(sender, chatWords[3]);
                        }
                        else
                        {
                            sender.SendChat("Usage: /tet bg volume [0-100]");
                        }
                        break;
                }
                return;
            }
            
            // All other commands require seated player
            if (currentPlayer == null || !currentPlayer.IsValid)
            {
                sender.SendChat("You must be seated in the chair to use this command.");
                Log.Write(LogLevel.Info, "TetrisGame: Command from non-seated player");
                return;
            }
            
            // Verify sender is the seated player
            if (sender.AgentInfo.SessionId != currentPlayer.AgentInfo.SessionId)
            {
                sender.SendChat("Only the seated player can use game commands.");
                Log.Write(LogLevel.Info, "TetrisGame: Command from different player than seated");
                return;
            }
            
            // Handle seated-only commands
            switch (command)
            {
                case "/tet score":
                    SendScoreInfo(sender);
                    break;
                case "/tet status":
                    SendGameStatus(sender);
                    break;
                case "/tet reset":
                case "/tet restart":
                case "/tet start":
                    HandleResetCommand(sender);
                    break;
                default:
                    sender.SendChat("Unknown command. Type '/tet help' for available commands.");
                    break;
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error processing chat command: " + ex.Message);
        }
    }
    
    private void SendHelpMessage(AgentPrivate agent)
    {
        try
        {
            string helpMessage = "Tetris Chat Commands:";
            helpMessage += "\n/tet help - (Anyone) Show this command list";
            helpMessage += "\n/tet leaderboard - (Anyone) Show top 10 scores";
            helpMessage += "\n/tet leader - (Anyone) Show top 10 scores";
            helpMessage += "\n/tet bg - (Anyone) Toggle bg music on/off";
            helpMessage += "\n/tet bg volume [0-100] - (Anyone) Set music volume";
            helpMessage += "\n/tet score - (Seated) Show current score";
            helpMessage += "\n/tet status - (Seated) Show game state";
            helpMessage += "\n/tet reset - (Seated) Start a new game";
            helpMessage += "\n/tet restart - (Seated) Start a new game";
            helpMessage += "\n/tet start - (Seated) Start a new game";
            
            agent.SendChat(helpMessage);
            Log.Write(LogLevel.Info, "TetrisGame: Sent help message to " + agent.AgentInfo.Name);
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error sending help message: " + ex.Message);
        }
    }
    
    private void SendScoreInfo(AgentPrivate agent)
    {
        try
        {
            string scoreMessage = string.Format("Score: {0} | Level: {1} | Lines: {2}", score, level, linesCleared);
            
            agent.SendChat(scoreMessage);
            Log.Write(LogLevel.Info, "TetrisGame: Sent score info to " + agent.AgentInfo.Name);
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error sending score info: " + ex.Message);
        }
    }
    
    private void SendGameStatus(AgentPrivate agent)
    {
        try
        {
            string statusMessage = "Game State: " + currentState.ToString();
            
            if (currentState == GameState.Playing)
            {
                statusMessage += " | Current Piece: " + currentPieceType.ToString();
            }
            else if (currentState == GameState.GameOver)
            {
                statusMessage += " | Final Score: " + score.ToString();
            }
            
            agent.SendChat(statusMessage);
            Log.Write(LogLevel.Info, "TetrisGame: Sent game status to " + agent.AgentInfo.Name);
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error sending game status: " + ex.Message);
        }
    }
    
    private void HandleResetCommand(AgentPrivate agent)
    {
        try
        {
            // Call the existing StartNewGame method
            StartNewGame();
            
            agent.SendChat("New game started! Good luck!");
            Log.Write(LogLevel.Info, "TetrisGame: Reset game for " + agent.AgentInfo.Name);
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error resetting game: " + ex.Message);
        }
    }
    
    #endregion
    
    #region Leaderboard System
    
    private void LoadLeaderboard(DataStore.Result<List<LeaderboardEntry>> result)
    {
        Log.Write(LogLevel.Info, "TetrisGame: LoadLeaderboard() callback called");
        Log.Write(LogLevel.Info, string.Format("TetrisGame: DataStore result - Success: {0}", result.Success));
        
        if (result.Success)
        {
            leaderboard = result.Object;
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Successfully loaded {0} leaderboard entries", leaderboard.Count));
            
            // Log each entry for debugging
            for (int i = 0; i < leaderboard.Count; i++)
            {
                var entry = leaderboard[i];
                Log.Write(LogLevel.Info, string.Format("TetrisGame: Leaderboard[{0}]: {1} - Score: {2}, Level: {3}, Lines: {4}, Date: {5}", 
                    i, entry.Handle, entry.Score, entry.Level, entry.Lines, entry.Date));
            }
        }
        else
        {
            // If no leaderboard exists yet, create an empty one
            leaderboard = new List<LeaderboardEntry>();
            Log.Write(LogLevel.Info, "TetrisGame: No existing leaderboard found, starting fresh");
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Load failure reason: {0}", result.Message));
            Log.Write(LogLevel.Info, string.Format("TetrisGame: JsonString: {0}", result.JsonString ?? "null"));
        }
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Final leaderboard state - Count: {0}", leaderboard.Count));
    }
    
    private void SaveLeaderboard()
    {
        Log.Write(LogLevel.Info, "TetrisGame: SaveLeaderboard() called");
        
        if (leaderboardStore != null)
        {
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Attempting to save {0} leaderboard entries with key '{1}'", leaderboard.Count, leaderboardKey));
            
            // Log what we're trying to save
            for (int i = 0; i < leaderboard.Count; i++)
            {
                var entry = leaderboard[i];
                Log.Write(LogLevel.Info, string.Format("TetrisGame: Saving entry[{0}]: {1} - Score: {2}, Level: {3}, Lines: {4}", 
                    i, entry.Handle, entry.Score, entry.Level, entry.Lines));
            }
            
            leaderboardStore.Store(leaderboardKey, leaderboard, (result) =>
            {
                Log.Write(LogLevel.Info, string.Format("TetrisGame: SaveLeaderboard callback - Success: {0}", result.Success));
                
                if (result.Success)
                {
                    Log.Write(LogLevel.Info, "TetrisGame: Leaderboard saved successfully to DataStore!");
                    Log.Write(LogLevel.Info, string.Format("TetrisGame: Save result version: {0}", result.Version));
                }
                else
                {
                    Log.Write(LogLevel.Warning, string.Format("TetrisGame: FAILED to save leaderboard: {0}", result.Message));
                    Log.Write(LogLevel.Warning, string.Format("TetrisGame: Save failure JsonString: {0}", result.JsonString ?? "null"));
                }
            });
        }
        else
        {
            Log.Write(LogLevel.Error, "TetrisGame: Cannot save leaderboard - leaderboardStore is null!");
        }
    }
    
    private bool IsHighScore(int score)
    {
        if (score <= 0) return false;
        if (leaderboard.Count < 10) return true;
        return score > leaderboard[leaderboard.Count - 1].Score;
    }
    
    private void UpdateLeaderboard(AgentPrivate player, int score, int level, int lines)
    {
        Log.Write(LogLevel.Info, "TetrisGame: UpdateLeaderboard() called");
        
        if (player == null || !player.IsValid || score <= 0) 
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: UpdateLeaderboard() early return - player valid: {0}, score: {1}", 
                (player != null && player.IsValid), score));
            return;
        }
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Current leaderboard count before update: {0}", leaderboard.Count));
        
        LeaderboardEntry newEntry = new LeaderboardEntry
        {
            Handle = player.AgentInfo.Handle,
            Score = score,
            Level = level,
            Lines = lines,
            Date = DateTime.UtcNow
        };
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Created new entry - Handle: {0}, Score: {1}, Level: {2}, Lines: {3}", 
            newEntry.Handle, newEntry.Score, newEntry.Level, newEntry.Lines));
        
        // Add the new entry
        leaderboard.Add(newEntry);
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Added entry to leaderboard. New count: {0}", leaderboard.Count));
        
        // Sort by score (descending)
        leaderboard.Sort((a, b) => b.Score.CompareTo(a.Score));
        Log.Write(LogLevel.Info, "TetrisGame: Sorted leaderboard by score (descending)");
        
        // Keep only top 10
        if (leaderboard.Count > 10)
        {
            int removedCount = leaderboard.Count - 10;
            leaderboard.RemoveRange(10, removedCount);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Trimmed leaderboard - removed {0} entries, new count: {1}", removedCount, leaderboard.Count));
        }
        
        // Log final leaderboard state
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Final leaderboard state after update ({0} entries):", leaderboard.Count));
        for (int i = 0; i < leaderboard.Count; i++)
        {
            var entry = leaderboard[i];
            Log.Write(LogLevel.Info, string.Format("TetrisGame: #{0}: {1} - {2} points", i+1, entry.Handle, entry.Score));
        }
        
        // Save to persistent storage
        SaveLeaderboard();
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Successfully added {0} to leaderboard with score {1}", player.AgentInfo.Handle, score));
    }
    
    private string FormatLeaderboard()
    {
        if (leaderboard.Count == 0)
        {
            return "=== TOP 10 LEADERBOARD ===\nNo scores yet! Be the first!";
        }
        
        string leaderboardText = "=== TOP 10 LEADERBOARD ===\n";
        for (int i = 0; i < leaderboard.Count; i++)
        {
            LeaderboardEntry entry = leaderboard[i];
            string dateStr = entry.Date.ToString("MM/dd HH:mm");
            leaderboardText += string.Format("{0}. {1} - {2} pts (L{3}, {4} lines) - {5}\n",
                i + 1, entry.Handle, entry.Score, entry.Level, entry.Lines, dateStr);
        }
        return leaderboardText;
    }
    
    private void SendLeaderboard(AgentPrivate agent)
    {
        Log.Write(LogLevel.Info, string.Format("TetrisGame: SendLeaderboard() called by {0}", agent.AgentInfo.Name));
        
        try
        {
            if (leaderboardStore == null)
            {
                Log.Write(LogLevel.Warning, "TetrisGame: Leaderboard requested but leaderboardStore is null");
                agent.SendChat("Leaderboard is not available (disabled or failed to initialize).");
                return;
            }
            
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Formatting leaderboard with {0} entries", leaderboard.Count));
            
            // Debug: Log current leaderboard state when requested
            Log.Write(LogLevel.Info, "TetrisGame: Current leaderboard state when requested:");
            for (int i = 0; i < leaderboard.Count; i++)
            {
                var entry = leaderboard[i];
                Log.Write(LogLevel.Info, string.Format("TetrisGame: Entry[{0}]: {1} - Score: {2}, Level: {3}, Lines: {4}, Date: {5}", 
                    i, entry.Handle, entry.Score, entry.Level, entry.Lines, entry.Date));
            }
            
            string leaderboardMessage = FormatLeaderboard();
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Formatted leaderboard message: {0}", leaderboardMessage));
            
            agent.SendChat(leaderboardMessage);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Successfully sent leaderboard to {0}", agent.AgentInfo.Name));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Error sending leaderboard to {0}: {1}", agent.AgentInfo.Name, ex.Message));
        }
    }
    
    #endregion
    
    #region Background Music System
    
    private void StartBackgroundMusic()
    {
        if (!musicSystemInitialized || !musicEnabled)
        {
            Log.Write(LogLevel.Info, "TetrisGame: Background music not started - system not initialized or disabled");
            return;
        }
        
        Log.Write(LogLevel.Info, "TetrisGame: Starting background music loop");
        StartCoroutine(BackgroundMusicLoop);
    }
    
    private void StopBackgroundMusic()
    {
        if (currentMusicHandle != null && currentMusicHandle.IsPlaying())
        {
            currentMusicHandle.Stop();
            Log.Write(LogLevel.Info, "TetrisGame: Stopped background music");
        }
        currentMusicHandle = null;
    }
    
    private void BackgroundMusicLoop()
    {
        while (musicEnabled && musicSystemInitialized)
        {
            // Get current track
            SoundResource currentTrack = musicTracks[currentTrackIndex];
            
            if (currentTrack != null)
            {
                PlayCurrentTrack(currentTrack);
                
                // Wait for track to finish playing
                while (currentMusicHandle != null && currentMusicHandle.IsPlaying() && musicEnabled)
                {
                    Wait(TimeSpan.FromSeconds(1.0f)); // Check every second
                }
                
                // 5-second delay between tracks
                if (musicEnabled)
                {
                    Log.Write(LogLevel.Info, "TetrisGame: 5-second delay between tracks");
                    Wait(TimeSpan.FromSeconds(5.0f));
                }
            }
            else
            {
                Log.Write(LogLevel.Warning, string.Format("TetrisGame: Background music track {0} is null, skipping", currentTrackIndex + 1));
            }
            
            // Move to next track (cycle through 0, 1, 2)
            currentTrackIndex = (currentTrackIndex + 1) % 3;
            
            // If music was disabled during loop, exit
            if (!musicEnabled)
            {
                Log.Write(LogLevel.Info, "TetrisGame: Background music loop exiting - music disabled");
                break;
            }
        }
    }
    
    private void PlayCurrentTrack(SoundResource track)
    {
        try
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(currentMusicVolume);
            
            currentMusicHandle = ScenePrivate.PlaySound(track, playSettings);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing background music track {0} at volume {1}%", currentTrackIndex + 1, currentMusicVolume));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error playing background music: " + ex.Message);
        }
    }
    
    private void ToggleBackgroundMusic(AgentPrivate agent)
    {
        try
        {
            musicEnabled = !musicEnabled;
            
            if (musicEnabled)
            {
                agent.SendChat("Background music enabled.");
                Log.Write(LogLevel.Info, "TetrisGame: Background music enabled via chat command");
                StartCoroutine(StartBackgroundMusic);
            }
            else
            {
                agent.SendChat("Background music disabled.");
                Log.Write(LogLevel.Info, "TetrisGame: Background music disabled via chat command");
                StopBackgroundMusic();
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error toggling background music: " + ex.Message);
        }
    }
    
    private void SetBackgroundMusicVolume(AgentPrivate agent, string volumeStr)
    {
        try
        {
            float volume;
            if (float.TryParse(volumeStr, out volume))
            {
                if (volume >= 0.0f && volume <= 100.0f)
                {
                    currentMusicVolume = volume;
                    agent.SendChat(string.Format("Background music volume set to {0}%", (int)volume));
                    Log.Write(LogLevel.Info, string.Format("TetrisGame: Background music volume set to {0}% via chat command", volume));
                    
                    // Update current playing track volume if music is playing
                    if (currentMusicHandle != null && currentMusicHandle.IsPlaying())
                    {
                        // Note: Sansar doesn't support changing volume of already playing sounds
                        // The new volume will apply to the next track
                        agent.SendChat("Volume will apply to next track.");
                    }
                }
                else
                {
                    agent.SendChat("Volume must be between 0 and 100.");
                }
            }
            else
            {
                agent.SendChat("Invalid volume value. Use a number between 0 and 100.");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error setting background music volume: " + ex.Message);
        }
    }
    
    #endregion
}