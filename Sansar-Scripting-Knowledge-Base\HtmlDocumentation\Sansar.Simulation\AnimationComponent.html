<html>
  <head>
    <title>Sansar.Simulation.AnimationComponent</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.AnimationComponent">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.AnimationComponent:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.AnimationComponent:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.AnimationComponent:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.AnimationComponent">AnimationComponent  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.AnimationComponent:Summary">The AnimationComponent handles interactions with animations.</p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.AnimationComponent:Signature">[Sansar.Script.Interface]<br />public class  <b>AnimationComponent</b> : <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.AnimationComponent:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.AnimationComponent:Docs:Remarks">Animated objects should be manipulated through this interface.</div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.AnimationComponent:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.
							</p>
        <h2 class="Section">Public Fields</h2>
        <div class="SectionBox" id="Public Fields">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.AnimationComponent.ComponentType">ComponentType</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/ComponentType.html">ComponentType</a>
                  </i>. The <a href="../Sansar.Simulation/AnimationComponent.html#F:Sansar.Simulation.AnimationComponent.ComponentType">AnimationComponent.ComponentType</a> of this component</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.AnimationComponent.ComponentId">ComponentId</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a>
                  </i>. Retrieves the component id for this AnimationComponent.</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.AnimationComponent.DefaultAnimation">DefaultAnimation</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/Animation.html">Animation</a>
                  </i>. 
            Gets the default scriptable animation.
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/InstanceInterface.html#P:Sansar.Script.InstanceInterface.IsValid">IsValid</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            Whether or not this interface is valid.
             (<i>Inherited from <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.AnimationComponent.Name">Name</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. This AnimationComponent name, as specified in the editor.</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32)">EnqueueEvent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<blockquote>
            Enqueues an event for this component.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,Sansar.Script.ScriptBase.OperationComplete)">EnqueueEvent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>
             Enqueues an event for this component.
             </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent})">EnqueueEvent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>
            Enqueues an event for this component.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.GetAnimation(System.String)">GetAnimation</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="../Sansar.Simulation/Animation.html">Animation</a></nobr><blockquote>
            Finds a scriptable animations by name.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.GetAnimations()">GetAnimations</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerable`1">IEnumerable&lt;Animation&gt;</a></nobr><blockquote>
            Gets the scriptable animations for this object.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.GetEventId(System.String)">GetEventId</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a></nobr><blockquote>
            Lookup the event id for the named event.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.GetVectorAnimationVariable(System.String)">GetVectorAnimationVariable</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="../Sansar/Vector.html">Sansar.Vector</a></nobr><blockquote>Get a Vector type variable from the animation by name</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.HasEvent(System.String)">HasEvent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a></nobr><blockquote>
            Check this component for the existence of the named event.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector)">SetPosition</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>)<blockquote>
            Asynchronously set the position for this object.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">SetPosition</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>
             Asynchronously set the position for this object.
             </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetPosition</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>
            Asynchronously set the position for this object.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,Sansar.Simulation.AnimationComponent.SubscriptionHandler,System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Simulation/AnimationComponent+SubscriptionHandler.html">AnimationComponent.SubscriptionHandler</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<blockquote>Subscribes to Animation Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,System.Action{Sansar.Simulation.AnimationData},System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;AnimationData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Animation Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.AnimationComponent.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote> A string representation of this object. </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.AnimationComponent:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="P:Sansar.Simulation.AnimationComponent.ComponentId">ComponentId Property</h3>
        <blockquote id="P:Sansar.Simulation.AnimationComponent.ComponentId:member">
          <div class="msummary">Retrieves the component id for this AnimationComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a> <b>ComponentId</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.AnimationComponent.ComponentId:Value">The id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.AnimationComponent.ComponentId:Remarks">This id will be unique among all components.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.AnimationComponent.ComponentId:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.AnimationComponent.ComponentType">ComponentType Field</h3>
        <blockquote id="F:Sansar.Simulation.AnimationComponent.ComponentType:member">
          <div class="msummary">The <a href="../Sansar.Simulation/AnimationComponent.html#F:Sansar.Simulation.AnimationComponent.ComponentType">AnimationComponent.ComponentType</a> of this component</div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="../Sansar.Simulation/ComponentType.html">ComponentType</a> <b>ComponentType</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.AnimationComponent.ComponentType:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.AnimationComponent.ComponentType:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.AnimationComponent.DefaultAnimation">DefaultAnimation Property</h3>
        <blockquote id="P:Sansar.Simulation.AnimationComponent.DefaultAnimation:member">
          <div class="msummary">
            Gets the default scriptable animation.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/Animation.html">Animation</a> <b>DefaultAnimation</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.AnimationComponent.DefaultAnimation:Value">An Animation object. </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.AnimationComponent.DefaultAnimation:Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.AnimationComponent.DefaultAnimation:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32)">EnqueueEvent Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32):member">
          <div class="msummary">
            Enqueues an event for this component.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>EnqueueEvent</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> internalId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32):Parameters">
            <dl>
              <dt>
                <i>internalId</i>
              </dt>
              <dd>The id of the animation event, may be looked up by name from <a href="../Sansar.Simulation/AnimationComponent.html#M:Sansar.Simulation.AnimationComponent.GetEventId(System.String)">AnimationComponent.GetEventId(string)</a></dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32):Returns">The internal event id for the completion event.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32):Remarks">Ids can be retrieved with <a href="../Sansar.Simulation/AnimationComponent.html#M:Sansar.Simulation.AnimationComponent.GetEventId(System.String)">AnimationComponent.GetEventId(string)</a>.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,Sansar.Script.ScriptBase.OperationComplete)">EnqueueEvent Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">
             Enqueues an event for this component.
             </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>EnqueueEvent</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> internalId, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>internalId</i>
              </dt>
              <dd>The id of the animation event, may be looked up by name from <a href="../Sansar.Simulation/AnimationComponent.html#M:Sansar.Simulation.AnimationComponent.GetEventId(System.String)">AnimationComponent.GetEventId(string)</a></dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,Sansar.Script.ScriptBase.OperationComplete):Returns">The internal event id for the completion event.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,Sansar.Script.ScriptBase.OperationComplete):Remarks">Ids can be retrieved with <a href="../Sansar.Simulation/AnimationComponent.html#M:Sansar.Simulation.AnimationComponent.GetEventId(System.String)">AnimationComponent.GetEventId(string)</a>.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent})">EnqueueEvent Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Enqueues an event for this component.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>EnqueueEvent</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> internalId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>internalId</i>
              </dt>
              <dd>The id of the animation event, may be looked up by name from <a href="../Sansar.Simulation/AnimationComponent.html#M:Sansar.Simulation.AnimationComponent.GetEventId(System.String)">AnimationComponent.GetEventId(string)</a></dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The internal event id for the completion event.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">Ids can be retrieved with <a href="../Sansar.Simulation/AnimationComponent.html#M:Sansar.Simulation.AnimationComponent.GetEventId(System.String)">AnimationComponent.GetEventId(string)</a>.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.GetAnimation(System.String)">GetAnimation Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.GetAnimation(System.String):member">
          <div class="msummary">
            Finds a scriptable animations by name.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/Animation.html">Animation</a> <b>GetAnimation</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> animationName)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.GetAnimation(System.String):Parameters">
            <dl>
              <dt>
                <i>animationName</i>
              </dt>
              <dd>The name of the animation.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.GetAnimation(System.String):Returns">An Animation, or null if no animation was found with the correct name. </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.GetAnimation(System.String):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.GetAnimation(System.String):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.GetAnimations()">GetAnimations Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.GetAnimations():member">
          <div class="msummary">
            Gets the scriptable animations for this object.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerable`1">IEnumerable&lt;Animation&gt;</a> <b>GetAnimations</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.GetAnimations():Returns">An IEnumarable collection of animation objects. </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.GetAnimations():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.GetAnimations():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.GetEventId(System.String)">GetEventId Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.GetEventId(System.String):member">
          <div class="msummary">
            Lookup the event id for the named event.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>GetEventId</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> eventName)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.GetEventId(System.String):Parameters">
            <dl>
              <dt>
                <i>eventName</i>
              </dt>
              <dd>The name of the event to query.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.GetEventId(System.String):Returns">The id of the named event or -1 if the name is not valid.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.GetEventId(System.String):Remarks">This can be used to trigger animations with <a href="../Sansar.Simulation/AnimationComponent.html#M:Sansar.Simulation.AnimationComponent.EnqueueEvent(System.Int32)">AnimationComponent.EnqueueEvent(int)</a>.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.GetEventId(System.String):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.GetVectorAnimationVariable(System.String)">GetVectorAnimationVariable Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.GetVectorAnimationVariable(System.String):member">
          <div class="msummary">Get a Vector type variable from the animation by name</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar/Vector.html">Sansar.Vector</a> <b>GetVectorAnimationVariable</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> variableName)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.GetVectorAnimationVariable(System.String):Parameters">
            <dl>
              <dt>
                <i>variableName</i>
              </dt>
              <dd>The name of the animation variable</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.GetVectorAnimationVariable(System.String):Returns">A Mono.Simd.Vector4f of the value of the named variable</blockquote>
          <h4 class="Subsection">Exceptions</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.GetVectorAnimationVariable(System.String):Exceptions">
            <table class="TypeDocumentation">
              <tr>
                <th>Type</th>
                <th>Reason</th>
              </tr>
              <tr valign="top">
                <td>
                  <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.ArgumentException">ArgumentException</a>
                </td>
                <td>Thrown when attempting to get a variable that doesn't exist or is of the wrong type</td>
              </tr>
              <tr valign="top">
                <td>
                  <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.ArgumentNullException">ArgumentNullException</a>
                </td>
                <td>If variableName is null</td>
              </tr>
            </table>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.GetVectorAnimationVariable(System.String):Remarks">Characters have an 'LLCameraForward' variable which can be queried with GetVectorAnimationVariable to determine the forward vector of the camera.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.GetVectorAnimationVariable(System.String):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.HasEvent(System.String)">HasEvent Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.HasEvent(System.String):member">
          <div class="msummary">
            Check this component for the existence of the named event.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>HasEvent</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> eventName)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.HasEvent(System.String):Parameters">
            <dl>
              <dt>
                <i>eventName</i>
              </dt>
              <dd>The name of the event to query.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.HasEvent(System.String):Returns">true if the event exits.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.HasEvent(System.String):Remarks">Can be used to check if an event exists.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.HasEvent(System.String):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.AnimationComponent.Name">Name Property</h3>
        <blockquote id="P:Sansar.Simulation.AnimationComponent.Name:member">
          <div class="msummary">This AnimationComponent name, as specified in the editor.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>Name</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.AnimationComponent.Name:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.AnimationComponent.Name:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.AnimationComponent.Name:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector)">SetPosition Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector):member">
          <div class="msummary">
            Asynchronously set the position for this object.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetPosition</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> position)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>position</i>
              </dt>
              <dd>The Mono.Simd.Vector4f position desired.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)">SetPosition Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">
             Asynchronously set the position for this object.
             </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetPosition</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> position, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>position</i>
              </dt>
              <dd>The Mono.Simd.Vector4f position desired.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent})">SetPosition Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Asynchronously set the position for this object.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetPosition</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> position, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>position</i>
              </dt>
              <dd>The Mono.Simd.Vector4f position desired.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.SetPosition(Sansar.Vector,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,Sansar.Simulation.AnimationComponent.SubscriptionHandler,System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,Sansar.Simulation.AnimationComponent.SubscriptionHandler,System.Boolean):member">
          <div class="msummary">Subscribes to Animation Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Use subscription callbacks of type Action&lt;Sansar.Simulation.AnimationData&gt;", true)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> BehaviorName, <a href="../Sansar.Simulation/AnimationComponent+SubscriptionHandler.html">AnimationComponent.SubscriptionHandler</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,Sansar.Simulation.AnimationComponent.SubscriptionHandler,System.Boolean):Parameters">
            <dl>
              <dt>
                <i>BehaviorName</i>
              </dt>
              <dd> The behavior name of the Animation. This name will match the subscribed event.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,Sansar.Simulation.AnimationComponent.SubscriptionHandler,System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,Sansar.Simulation.AnimationComponent.SubscriptionHandler,System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,System.Action{Sansar.Simulation.AnimationData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,System.Action{Sansar.Simulation.AnimationData},System.Boolean):member">
          <div class="msummary">Subscribes to Animation Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> BehaviorName, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;AnimationData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,System.Action{Sansar.Simulation.AnimationData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/AnimationData.html">AnimationData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,System.Action{Sansar.Simulation.AnimationData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>BehaviorName</i>
              </dt>
              <dd> The behavior name of the Animation. This name will match the subscribed event.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,System.Action{Sansar.Simulation.AnimationData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,System.Action{Sansar.Simulation.AnimationData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.Subscribe(System.String,System.Action{Sansar.Simulation.AnimationData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.AnimationComponent.ToString()">ToString Method</h3>
        <blockquote id="M:Sansar.Simulation.AnimationComponent.ToString():member">
          <div class="msummary"> A string representation of this object. </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.AnimationComponent.ToString():Returns">A string representation of this object.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.ToString():Remarks">The format of this string may change between releases.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.AnimationComponent.ToString():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>