<html>
  <head>
    <title>Sansar.Script.ScriptBase</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Script</a> : <a href="index.html">Sansar.Script Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Script.ScriptBase">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Script.ScriptBase:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Script.ScriptBase:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Script.ScriptBase:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Script.ScriptBase">ScriptBase  Class</h1>
    <p class="Summary" id="T:Sansar.Script.ScriptBase:Summary">
            Base class for all scripts, has ScriptHandle accessors and coroutine utilities.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Script.ScriptBase:Signature">public abstract class  <b>ScriptBase</b> : <a href="../Sansar.Script/Reflective.html">Reflective</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Script.ScriptBase:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Script.ScriptBase:Docs:Remarks">This abstract class manages shared information for all script types.</div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Script.ScriptBase:Docs:Version Information">
        <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/Reflective.html">Reflective</a>.
							</p>
        <h2 class="Section">Public Constructors</h2>
        <div class="SectionBox" id="Public Constructors">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <div>
                    <b>
                      <a href="#C:Sansar.Script.ScriptBase()">ScriptBase</a>
                    </b>()</div>
                </td>
                <td>
            Constructor
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Script.ScriptBase.Log">Log</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/Log.html">Log</a>
                  </i>. 
            Gets the script console.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Script.ScriptBase.Memory">Memory</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/Memory.html">Memory</a>
                  </i>. 
            Memory information for the pool this script is in.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Script.ScriptBase.Script">Script</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/ScriptHandle.html">ScriptHandle</a>
                  </i>. 
            Script handle to this script.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Protected Properties</h2>
        <div class="SectionBox" id="Protected Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/Reflective.html#P:Sansar.Script.Reflective.AllowedContexts">AllowedContexts</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/Reflective+Context.html">Reflective.Context</a>
                  </i>. Internal Use Only. Overridden by subclasses to return only those contexts requested which are allowed for that type of script. (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Reflective</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Script.ScriptBase.CurrentCoroutine">CurrentCoroutine</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/ICoroutine.html">ICoroutine</a>
                  </i>. 
            Gets the ICoroutine interface for the current coroutine.
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Script.ScriptBase.MaxCoroutines">MaxCoroutines</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>
                  </i>. 
            The maximum number of coroutines that a single script can run.
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Script.ScriptBase.PendingEventCount">PendingEventCount</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>
                  </i>. 
            The number of events currently waiting to be processed.
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/Reflective.html#P:Sansar.Script.Reflective.ReflectiveContexts">ReflectiveContexts</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/Reflective+Context.html">Reflective.Context</a>
                  </i>. 
            Override ReflectiveContexts to limit which contexts this Reflective interface is available in when registered with.
             (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Reflective</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/Reflective.html#P:Sansar.Script.Reflective.ReflectiveName">ReflectiveName</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. 
            Override ReflectiveName to change which name this class will be registered as in the Reflective system.
             (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Reflective</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div>static </div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Script.ScriptBase.ScriptEventMessageIdCount">ScriptEventMessageIdCount</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>
                  </i>. 
            Obsolete
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/Reflective.html#M:Sansar.Script.Reflective.AsInterface``1()">AsInterface&lt;TInterface&gt;</a>
                  </b>()<nobr> : <i title="The interface describing the methods and properties desired.">TInterface</i></nobr><blockquote>
            Returns a TInterface object if one can be created, null otherwise
             (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Reflective</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/Reflective.html#M:Sansar.Script.Reflective.FullInterface(System.String)">FullInterface</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>
            Generates a string which shows all the members which can be reflected.
             (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Reflective</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>abstract </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.Init()">Init</a>
                  </b>()<blockquote> 
            Init() is called after all interfaces have been initialized.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/Reflective.html#M:Sansar.Script.Reflective.Register()">Register</a>
                  </b>()<blockquote>
            Register this object to be found with <a href="../Sansar.Simulation/ScenePrivate.html#M:Sansar.Simulation.ScenePrivate.FindReflective(System.String)">Sansar.Simulation.ScenePrivate.FindReflective(string)</a> (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Reflective</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/Reflective.html#M:Sansar.Script.Reflective.Unregister()">Unregister</a>
                  </b>()<blockquote>
            Unregister this object so it will not be found with <a href="../Sansar.Simulation/ScenePrivate.html#M:Sansar.Simulation.ScenePrivate.FindReflective(System.String)">Sansar.Simulation.ScenePrivate.FindReflective(string)</a> (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Reflective</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.Yield()">Yield</a>
                  </b>()<blockquote>
            Yield to let other coroutines or events run.
            </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Protected Methods</h2>
        <div class="SectionBox" id="Protected Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.GetAllCoroutines()">GetAllCoroutines</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IReadOnlyList`1">IReadOnlyList&lt;ICoroutine&gt;</a></nobr><blockquote>
            A list of all coroutines for this script.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.GetCoroutineCount()">GetCoroutineCount</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a></nobr><blockquote>
            The total number of coroutines running on this script.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.Lock(System.Object)">Lock</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.IDisposable">IDisposable</a></nobr><blockquote>
            Use to create a critical section with a using statement, ensuring that no other coroutines or scripts (via Reflective) may enter this code section while one coroutine or script is in it.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.PostScriptEvent(System.String)">PostScriptEvent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<blockquote>
            Post the event for all scripts.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.PostScriptEvent(System.String,Sansar.Script.Reflective)">PostScriptEvent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/Reflective.html">Reflective</a>)<blockquote>
            Post the event for all scripts.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">PostScriptEvent</a>
                  </b>(<a href="../Sansar.Script/ScriptId.html">ScriptId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/Reflective.html">Reflective</a>)<blockquote>
            Post the event for the target script.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object)">PostSimpleScriptEvent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>)<blockquote>
            Deprecated. Use <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">ScriptBase.PostScriptEvent(ScriptId, string, Reflective)</a></blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(Sansar.Script.ScriptId,System.String,System.Object)">PostSimpleScriptEvent</a>
                  </b>(<a href="../Sansar.Script/ScriptId.html">ScriptId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>)<blockquote>
            Deprecated. Use <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">ScriptBase.PostScriptEvent(ScriptId, string, Reflective)</a></blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.ReleaseLock(System.Object)">ReleaseLock</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>)<blockquote>
            Release a lock if held.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.SetDefaultErrorMode()">SetDefaultErrorMode</a>
                  </b>()<blockquote>
            Write errors to the Script Debug Console instead of throwing exceptions for some common API errors while still throwing exceptions for the more serious errors.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.SetRelaxedErrorMode()">SetRelaxedErrorMode</a>
                  </b>()<blockquote>
            Write errors to the Script Debug Console instead of throw exceptions for almost all Sansar API errors.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.SetStrictErrorMode()">SetStrictErrorMode</a>
                  </b>()<blockquote>
            Throw exceptions for almost all Sansar API errors.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;OperationCompleteEvent&gt;</a>)<nobr> : <a href="../Sansar.Script/ICoroutine.html">ICoroutine</a></nobr><blockquote>
            Starts a coroutine on the current script.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.StartCoroutine``1(System.Action{``0},``0,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine&lt;T&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;T&gt;</a>, <i title="Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;OperationCompleteEvent&gt;</a>)<nobr> : <a href="../Sansar.Script/ICoroutine.html">ICoroutine</a></nobr><blockquote>
            Starts a coroutine on the current script.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.StartCoroutine``2(System.Action{``0,``1},``0,``1,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine&lt;T,T1&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`2">Action&lt;T, T1&gt;</a>, <i title="Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T</i>, <i title="Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T1</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;OperationCompleteEvent&gt;</a>)<nobr> : <a href="../Sansar.Script/ICoroutine.html">ICoroutine</a></nobr><blockquote>
            Starts a coroutine on the current script.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.StartCoroutine``3(System.Action{``0,``1,``2},``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine&lt;T,T1,T2&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`3">Action&lt;T, T1, T2&gt;</a>, <i title="Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T</i>, <i title="Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T1</i>, <i title="Type of the third parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T2</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;OperationCompleteEvent&gt;</a>)<nobr> : <a href="../Sansar.Script/ICoroutine.html">ICoroutine</a></nobr><blockquote>
            Starts a coroutine on the current script.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.StartCoroutine``4(System.Action{``0,``1,``2,``3},``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine&lt;T,T1,T2,T3&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`4">Action&lt;T, T1, T2, T3&gt;</a>, <i title="Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T</i>, <i title="Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T1</i>, <i title="Type of the third parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T2</i>, <i title="Type of the fourth parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T3</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;OperationCompleteEvent&gt;</a>)<nobr> : <a href="../Sansar.Script/ICoroutine.html">ICoroutine</a></nobr><blockquote>
            Starts a coroutine on the current script.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean)">SubscribeToScriptEvent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ScriptEventData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">IEventSubscription</a></nobr><blockquote>
            Subscribes to events sent by other scripts
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(Sansar.Script.ScriptId,System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean)">SubscribeToScriptEvent</a>
                  </b>(<a href="../Sansar.Script/ScriptId.html">ScriptId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ScriptEventData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">IEventSubscription</a></nobr><blockquote>
            Subscribes to events sent only by a specific script.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.Terminate(System.String)">Terminate</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<blockquote>
            Terminates this script immediately.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.Wait(System.Double)">Wait</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Double">double</a>)<blockquote>
            Delays execution of the current coroutine for the specified time.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.Wait(System.TimeSpan)">Wait</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.TimeSpan">TimeSpan</a>)<blockquote>
            Delays execution of the current coroutine for the specified time.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor(Sansar.Script.ICoroutine)">WaitFor</a>
                  </b>(<a href="../Sansar.Script/ICoroutine.html">ICoroutine</a>)<blockquote>
            Block the current coroutine until otherCoroutine finishes.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor(System.Action{System.Action{Sansar.Script.OperationCompleteEvent}})">WaitFor</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Action&lt;OperationCompleteEvent&gt;&gt;</a>)<nobr> : <a href="../Sansar.Script/OperationCompleteEvent.html">OperationCompleteEvent</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription})">WaitFor</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`4">Func&lt;Action&lt;EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">WaitFor</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`3">Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},System.Action)">WaitFor</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`3">Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0)">WaitFor&lt;ARG1&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`5">Func&lt;ARG1, Action&lt;EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0)">WaitFor&lt;ARG1&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`4">Func&lt;ARG1, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,System.Action)">WaitFor&lt;ARG1&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`4">Func&lt;ARG1, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``1(System.Action{``0,System.Action{Sansar.Script.OperationCompleteEvent}},``0)">WaitFor&lt;T1&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`2">Action&lt;T1, Action&lt;OperationCompleteEvent&gt;&gt;</a>, <i title="The type of the argument to func.">T1</i>)<nobr> : <a href="../Sansar.Script/OperationCompleteEvent.html">OperationCompleteEvent</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1)">WaitFor&lt;ARG1,ARG2&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`6">Func&lt;ARG1, ARG2, Action&lt;EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1)">WaitFor&lt;ARG1,ARG2&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`5">Func&lt;ARG1, ARG2, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,System.Action)">WaitFor&lt;ARG1,ARG2&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`5">Func&lt;ARG1, ARG2, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``2(System.Action{``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1)">WaitFor&lt;T1,T2&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`3">Action&lt;T1, T2, Action&lt;OperationCompleteEvent&gt;&gt;</a>, <i title="The type of the argument to func.">T1</i>, <i title="The type of the argument to func.">T2</i>)<nobr> : <a href="../Sansar.Script/OperationCompleteEvent.html">OperationCompleteEvent</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2)">WaitFor&lt;ARG1,ARG2,ARG3&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`7">Func&lt;ARG1, ARG2, ARG3, Action&lt;EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2)">WaitFor&lt;ARG1,ARG2,ARG3&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`6">Func&lt;ARG1, ARG2, ARG3, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,System.Action)">WaitFor&lt;ARG1,ARG2,ARG3&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`6">Func&lt;ARG1, ARG2, ARG3, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``3(System.Action{``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2)">WaitFor&lt;T1,T2,T3&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`4">Action&lt;T1, T2, T3, Action&lt;OperationCompleteEvent&gt;&gt;</a>, <i title="The type of the argument to func.">T1</i>, <i title="The type of the argument to func.">T2</i>, <i title="The type of the argument to func.">T3</i>)<nobr> : <a href="../Sansar.Script/OperationCompleteEvent.html">OperationCompleteEvent</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`8">Func&lt;ARG1, ARG2, ARG3, ARG4, Action&lt;EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <i title="The type of the argument to func.">ARG4</i>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`7">Func&lt;ARG1, ARG2, ARG3, ARG4, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <i title="The type of the argument to func.">ARG4</i>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,System.Action)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`7">Func&lt;ARG1, ARG2, ARG3, ARG4, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <i title="The type of the argument to func.">ARG4</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``4(System.Action{``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2,``3)">WaitFor&lt;T1,T2,T3,T4&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`5">Action&lt;T1, T2, T3, T4, Action&lt;OperationCompleteEvent&gt;&gt;</a>, <i title="The type of the argument to func.">T1</i>, <i title="The type of the argument to func.">T2</i>, <i title="The type of the argument to func.">T3</i>, <i title="The type of the argument to func.">T4</i>)<nobr> : <a href="../Sansar.Script/OperationCompleteEvent.html">OperationCompleteEvent</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4,ARG5&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`9">Func&lt;ARG1, ARG2, ARG3, ARG4, ARG5, Action&lt;EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <i title="The type of the argument to func.">ARG4</i>, <i title="The type of the argument to func.">ARG5</i>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4,ARG5&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`8">Func&lt;ARG1, ARG2, ARG3, ARG4, ARG5, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <i title="The type of the argument to func.">ARG4</i>, <i title="The type of the argument to func.">ARG5</i>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4,System.Action)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4,ARG5&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`8">Func&lt;ARG1, ARG2, ARG3, ARG4, ARG5, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <i title="The type of the argument to func.">ARG4</i>, <i title="The type of the argument to func.">ARG5</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>)<nobr> : <a href="../Sansar.Script/EventData.html">EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitForLock(System.Object)">WaitForLock</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>)<blockquote>
            WaitFor and take a lock on Token
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ScriptBase.WaitForSignal()">WaitForSignal</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a></nobr><blockquote>
            Wait the current coroutine until at least 1 signal is sent to it through the ICoroutine interface.
            </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Script.ScriptBase:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="C:Sansar.Script.ScriptBase()">ScriptBase Constructor</h3>
        <blockquote id="C:Sansar.Script.ScriptBase():member">
          <div class="msummary">
            Constructor
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public  <b>ScriptBase</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="C:Sansar.Script.ScriptBase():Remarks">Initializes a new instance of the ScriptBase class.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="C:Sansar.Script.ScriptBase():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Script.ScriptBase.CurrentCoroutine">CurrentCoroutine Property</h3>
        <blockquote id="P:Sansar.Script.ScriptBase.CurrentCoroutine:member">
          <div class="msummary">
            Gets the ICoroutine interface for the current coroutine.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/ICoroutine.html">ICoroutine</a> <b>CurrentCoroutine</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Script.ScriptBase.CurrentCoroutine:Value">The ICoroutine interface to the currently running coroutine.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.CurrentCoroutine:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.CurrentCoroutine:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.GetAllCoroutines()">GetAllCoroutines Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.GetAllCoroutines():member">
          <div class="msummary">
            A list of all coroutines for this script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IReadOnlyList`1">IReadOnlyList&lt;ICoroutine&gt;</a> <b>GetAllCoroutines</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.GetAllCoroutines():Returns">A list containing all running or waiting coroutines.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.GetAllCoroutines():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.GetAllCoroutines():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.GetCoroutineCount()">GetCoroutineCount Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.GetCoroutineCount():member">
          <div class="msummary">
            The total number of coroutines running on this script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>GetCoroutineCount</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.GetCoroutineCount():Returns">The total number of coroutines running on this script.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.GetCoroutineCount():Remarks">Includes currently active and waiting coroutines, but not killed coroutines. A new list is created every time it is called with the coroutines at the time of the call. It is possible for coroutines to finish while iterating on the list which will not remove them from the list. Any finished coroutines still in the list will have IsAlive == false.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.GetCoroutineCount():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.Init()">Init Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.Init():member">
          <div class="msummary"> 
            Init() is called after all interfaces have been initialized.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />public abstract <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Init</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Init():Remarks">
            Override Init() to set up script subscriptions and callbacks. 
            Any members set before Init is called (such as in a script constructor) may be overwritten before Init is called.
            </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Init():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.Lock(System.Object)">Lock Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.Lock(System.Object):member">
          <div class="msummary">
            Use to create a critical section with a using statement, ensuring that no other coroutines or scripts (via Reflective) may enter this code section while one coroutine or script is in it.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.IDisposable">IDisposable</a> <b>Lock</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a> Token)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.Lock(System.Object):See Also">
            <div>
              <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.ReleaseLock(System.Object)">ScriptBase.ReleaseLock(object)</a>
            </div>
            <div>
              <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitForLock(System.Object)">ScriptBase.WaitForLock(object)</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.Lock(System.Object):Parameters">
            <dl>
              <dt>
                <i>Token</i>
              </dt>
              <dd>The object controlling the lock. Recommended to be a private object member.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.Lock(System.Object):Returns">A ScopedLock that holds the lock and will release it in IDisposable.Dispose.</blockquote>
          <h4 class="Subsection">Exceptions</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.Lock(System.Object):Exceptions">
            <table class="TypeDocumentation">
              <tr>
                <th>Type</th>
                <th>Reason</th>
              </tr>
              <tr valign="top">
                <td>
                  <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.ArgumentNullException">ArgumentNullException</a>
                </td>
                <td>Thrown if Token is null.</td>
              </tr>
            </table>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Lock(System.Object):Remarks">Use with <tt>using</tt> to create a critical section. If a lock is already held on Token by a different coroutine then this coroutine will wait for the lock to be released before entering the critical section.
            Lock is conceptually similar to the <tt>lock</tt> keyword.</div>
          <h2 class="Section">Example</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Lock(System.Object):Example:1">
            <table class="CodeExampleTable">
              <tr>
                <td>
                  <b>
                    <font size="-1">C# Example</font>
                  </b>
                </td>
              </tr>
              <tr>
                <td>
                  <pre class="code-csharp">
            private object BalanceLock = new Object();
            int Balance = 100;
            bool Spend(int value)
            {
                using (Lock(BalanceLock))
                {
                    if (Balance &gt; value)
                    {
                        Balance -= value;
                        return true;
                    }
                    return false;
                }
            }</pre>
                </td>
              </tr>
            </table>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Lock(System.Object):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Script.ScriptBase.Log">Log Property</h3>
        <blockquote id="P:Sansar.Script.ScriptBase.Log:member">
          <div class="msummary">
            Gets the script console.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />public <a href="../Sansar.Script/Log.html">Log</a> <b>Log</b>  { protected get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Script.ScriptBase.Log:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.Log:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.Log:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Script.ScriptBase.MaxCoroutines">MaxCoroutines Property</h3>
        <blockquote id="P:Sansar.Script.ScriptBase.MaxCoroutines:member">
          <div class="msummary">
            The maximum number of coroutines that a single script can run.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>MaxCoroutines</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Script.ScriptBase.MaxCoroutines:Value">The maximum number of coroutines that a single script can run.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.MaxCoroutines:Remarks">If GetCoroutineCount() == MaxCoroutines, no more coroutines will be started.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.MaxCoroutines:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Script.ScriptBase.Memory">Memory Property</h3>
        <blockquote id="P:Sansar.Script.ScriptBase.Memory:member">
          <div class="msummary">
            Memory information for the pool this script is in.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />public <a href="../Sansar.Script/Memory.html">Memory</a> <b>Memory</b>  { protected get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Script.ScriptBase.Memory:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.Memory:Remarks">Scripts are pooled by their owner and share memory. Use this object to get events on nearing memory limits as well as get information on memory activity and use.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.Memory:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Script.ScriptBase.PendingEventCount">PendingEventCount Property</h3>
        <blockquote id="P:Sansar.Script.ScriptBase.PendingEventCount:member">
          <div class="msummary">
            The number of events currently waiting to be processed.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>PendingEventCount</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Script.ScriptBase.PendingEventCount:Value">The number of events currently waiting to be processed.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.PendingEventCount:Remarks">To remedy a situation of growing pending event count try reducing the number or rate of events the script is receiving or the amount of work done in each event.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.PendingEventCount:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String)">PostScriptEvent Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String):member">
          <div class="msummary">
            Post the event for all scripts.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>PostScriptEvent</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> message)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String):Parameters">
            <dl>
              <dt>
                <i>message</i>
              </dt>
              <dd>The message id used to direct the event.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String,Sansar.Script.Reflective)">PostScriptEvent Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String,Sansar.Script.Reflective):member">
          <div class="msummary">
            Post the event for all scripts.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>PostScriptEvent</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> message, <a href="../Sansar.Script/Reflective.html">Reflective</a> data)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String,Sansar.Script.Reflective):Parameters">
            <dl>
              <dt>
                <i>message</i>
              </dt>
              <dd>The message id used to direct the event.</dd>
              <dt>
                <i>data</i>
              </dt>
              <dd>The object to be post.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String,Sansar.Script.Reflective):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String,Sansar.Script.Reflective):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">PostScriptEvent Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective):member">
          <div class="msummary">
            Post the event for the target script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>PostScriptEvent</b> (<a href="../Sansar.Script/ScriptId.html">ScriptId</a> targetScriptId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> message, <a href="../Sansar.Script/Reflective.html">Reflective</a> data)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective):Parameters">
            <dl>
              <dt>
                <i>targetScriptId</i>
              </dt>
              <dd>The id of the script to sent the event to. To broadcast the message to all subscripted scripts use <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(System.String,Sansar.Script.Reflective)">ScriptBase.PostScriptEvent(string, Reflective)</a>. </dd>
              <dt>
                <i>message</i>
              </dt>
              <dd>The message id used to direct the event.</dd>
              <dt>
                <i>data</i>
              </dt>
              <dd>The object to be post.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object)">PostSimpleScriptEvent Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object):member">
          <div class="msummary">
            Deprecated. Use <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">ScriptBase.PostScriptEvent(ScriptId, string, Reflective)</a></div>
          <h2>Syntax</h2>
          <div class="Signature">[System.Obsolete("Deprecated. Use PostScriptEvent directly instead.", true)]<br />protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>PostSimpleScriptEvent</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> message, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a> data)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object):See Also">
            <div>
              <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(System.String,Sansar.Script.Reflective)">ScriptBase.PostScriptEvent(string, Reflective)</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object):Parameters">
            <dl>
              <dt>
                <i>message</i>
              </dt>
              <dd>The message id used to direct the event.</dd>
              <dt>
                <i>data</i>
              </dt>
              <dd>An object to send to scripts subscribed to message.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object):Remarks">Deprecated. Use <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">ScriptBase.PostScriptEvent(ScriptId, string, Reflective)</a></div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(Sansar.Script.ScriptId,System.String,System.Object)">PostSimpleScriptEvent Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(Sansar.Script.ScriptId,System.String,System.Object):member">
          <div class="msummary">
            Deprecated. Use <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">ScriptBase.PostScriptEvent(ScriptId, string, Reflective)</a></div>
          <h2>Syntax</h2>
          <div class="Signature">[System.Obsolete("Deprecated. Use PostScriptEvent directly instead.", true)]<br />protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>PostSimpleScriptEvent</b> (<a href="../Sansar.Script/ScriptId.html">ScriptId</a> targetScriptId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> message, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a> data)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(Sansar.Script.ScriptId,System.String,System.Object):See Also">
            <div>
              <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">ScriptBase.PostScriptEvent(ScriptId, string, Reflective)</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(Sansar.Script.ScriptId,System.String,System.Object):Parameters">
            <dl>
              <dt>
                <i>targetScriptId</i>
              </dt>
              <dd>The id of the script to sent the event to. To broadcast the message to all subscripted scripts use <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object)">ScriptBase.PostSimpleScriptEvent(string, object)</a>. </dd>
              <dt>
                <i>message</i>
              </dt>
              <dd>The message id used to direct the event.</dd>
              <dt>
                <i>data</i>
              </dt>
              <dd>An object to send to script targetScriptId if subscribed to message.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(Sansar.Script.ScriptId,System.String,System.Object):Remarks">Deprecated. Use <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">ScriptBase.PostScriptEvent(ScriptId, string, Reflective)</a></div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(Sansar.Script.ScriptId,System.String,System.Object):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.ReleaseLock(System.Object)">ReleaseLock Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.ReleaseLock(System.Object):member">
          <div class="msummary">
            Release a lock if held.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>ReleaseLock</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a> Token)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.ReleaseLock(System.Object):See Also">
            <div>
              <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Lock(System.Object)">ScriptBase.Lock(object)</a>
            </div>
            <div>
              <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitForLock(System.Object)">ScriptBase.WaitForLock(object)</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.ReleaseLock(System.Object):Parameters">
            <dl>
              <dt>
                <i>Token</i>
              </dt>
              <dd>The object controlling the lock. Recommended to be a private object member.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Exceptions</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.ReleaseLock(System.Object):Exceptions">
            <table class="TypeDocumentation">
              <tr>
                <th>Type</th>
                <th>Reason</th>
              </tr>
              <tr valign="top">
                <td>
                  <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.ArgumentNullException">ArgumentNullException</a>
                </td>
                <td>Thrown if Token is null.</td>
              </tr>
            </table>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.ReleaseLock(System.Object):Remarks">It is highly recommended to use <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Lock(System.Object)">ScriptBase.Lock(object)</a> instead. This is for advanced use only and can easily create deadlocks that stop scripts from working. Can not be used to release a lock not currently held by this coroutine.</div>
          <h2 class="Section">Example</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.ReleaseLock(System.Object):Example:1">
            <table class="CodeExampleTable">
              <tr>
                <td>
                  <b>
                    <font size="-1">C# Example</font>
                  </b>
                </td>
              </tr>
              <tr>
                <td>
                  <pre class="code-csharp">
            private object BalanceLock = new Object();
            int Balance = 100;
            bool Spend(int value)
            {
                WaitForLock(BalanceLock);
                bool success = false;
                if (Balance &gt; value)
                {
                    Balance -= value;
                    success = true;
                }
                ReleaseLock(BalanceLock);
                // Note that any early exit between WaitForLock and ReleaseLock fail to release the lock, creating a deadlock the next time this method is called.
                // For this reason it is highly recommended to instead do: using (Lock(BalanceLock))
                return success;
            }</pre>
                </td>
              </tr>
            </table>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.ReleaseLock(System.Object):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Script.ScriptBase.Script">Script Property</h3>
        <blockquote id="P:Sansar.Script.ScriptBase.Script:member">
          <div class="msummary">
            Script handle to this script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />public <a href="../Sansar.Script/ScriptHandle.html">ScriptHandle</a> <b>Script</b>  { protected get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Script.ScriptBase.Script:Value">An opaque type used by certain API methods.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.Script:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.Script:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Script.ScriptBase.ScriptEventMessageIdCount">ScriptEventMessageIdCount Property</h3>
        <blockquote id="P:Sansar.Script.ScriptBase.ScriptEventMessageIdCount:member">
          <div class="msummary">
            Obsolete
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[System.Obsolete("No longer used as there is no longer a limited number of message ids. Obsoleted 2018-04.", true)]<br />protected static <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>ScriptEventMessageIdCount</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Script.ScriptBase.ScriptEventMessageIdCount:Value">Always returns 0</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.ScriptEventMessageIdCount:Remarks">No longer used as there is no longer a limited number of message ids. Obsoleted 2018-04.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Script.ScriptBase.ScriptEventMessageIdCount:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.SetDefaultErrorMode()">SetDefaultErrorMode Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.SetDefaultErrorMode():member">
          <div class="msummary">
            Write errors to the Script Debug Console instead of throwing exceptions for some common API errors while still throwing exceptions for the more serious errors.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetDefaultErrorMode</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.SetDefaultErrorMode():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.SetDefaultErrorMode():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.SetRelaxedErrorMode()">SetRelaxedErrorMode Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.SetRelaxedErrorMode():member">
          <div class="msummary">
            Write errors to the Script Debug Console instead of throw exceptions for almost all Sansar API errors.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetRelaxedErrorMode</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.SetRelaxedErrorMode():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.SetRelaxedErrorMode():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.SetStrictErrorMode()">SetStrictErrorMode Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.SetStrictErrorMode():member">
          <div class="msummary">
            Throw exceptions for almost all Sansar API errors.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetStrictErrorMode</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.SetStrictErrorMode():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.SetStrictErrorMode():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Starts a coroutine on the current script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/ICoroutine.html">ICoroutine</a> <b>StartCoroutine</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a> coroutine, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>coroutine</i>
              </dt>
              <dd>The coroutine to run.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>A callback to know when the coroutine has finished.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">An ICoroutine interface to stop the coroutine or see if it has finished.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">A coroutine can call <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.UInt64})">ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.UInt64&gt;)</a> to block while waiting
            for events to return.
            <p>The following three scripts acccomplish the same goal using event subscriptions and coroutines.
            Each script will listen for new agents to join the scene and keep track of the agents name and the time it
            joined. When the agent leaves, its name and the duration of its visit will be reported.</p><table class="CodeExampleTable"><tr><td><b><font size="-1">C# Example</font></b></td></tr><tr><td><pre class="code-csharp">/* This content is licensed under the terms of the Creative Commons Attribution 4.0 International License.
 * When using this content, you must:
 * �    Acknowledge that the content is from the Sansar Knowledge Base.
 * �    Include our copyright notice: "2022 Sansar, Inc."
 * �    Indicate that the content is licensed under the Creative Commons Attribution-Share Alike 4.0 International License.
 * �    Include the URL for, or link to, the license summary at https://creativecommons.org/licenses/by-sa/4.0/deed.hi (and, if possible, to the complete license terms at https://creativecommons.org/licenses/by-sa/4.0/legalcode.
 * For example:
 * "This work uses content from the Sansar Knowledge Base. � 2022 Sansar, Inc." Licensed under the Creative Commons Attribution 4.0 International License (license summary available at https://creativecommons.org/licenses/by/4.0/ and complete license terms available at https://creativecommons.org/licenses/by/4.0/legalcode)."
 */

using System;
using Sansar.Script;
using Sansar.Simulation;
using System.Collections.Generic;

public class StarWarsVoiceover : SceneObjectScript
{
    // PUBLIC MEMBERS ------

    public SoundResource Sound;

    [Range(0.0, 60.0)]
    [DefaultValue(48.0)]
    public double Loudness_dB;


    // PRIVATE MEMBERS ------

    private float ZeroDBOffset = 48.0f;
    private RigidBodyComponent RigidBody;
    private float Relative_Loudness;
    static private Dictionary&lt;SessionId, PlayHandle&gt; playHandles = new Dictionary&lt;SessionId, PlayHandle&gt;();


    private void PlayOnAgent(AgentPrivate agent)
    {
        try
        {
            SessionId agentId = agent.AgentInfo.SessionId;
            Stop(agentId);

            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = Relative_Loudness;
            if (agent.IsValid)
            {
                PlayHandle playHandle = agent.PlaySound(Sound, playSettings);
                if (playHandle != null)
                {
                    playHandles.Add(agentId, playHandle);
                    playHandle.OnFinished(() =&gt; Stop(agentId));
                }
            }
        }
        catch (Exception e)
        {
            Log.Write(LogLevel.Warning, "Voiceover", $"Exception {e.GetType().Name} in PlayOnAgent");
        }
    }

    private void Stop(SessionId id)
    {
        try
        {
            PlayHandle playing;
            if (playHandles.TryGetValue(id, out playing))
            {
                playing.Stop();
                playHandles.Remove(id);
            }
        }
        catch (Exception e)
        {
            Log.Write(LogLevel.Warning, "Voiceover", $"Exception {e.GetType().Name} while stopping a sound");
        }
    }

    private void OnCollide(CollisionData data)
    {
        if (data.Phase == Sansar.Simulation.CollisionEventPhase.TriggerEnter)
        {
            try
            {
                AgentPrivate agent = ScenePrivate.FindAgent(data.HitComponentId.ObjectId);
                if (agent != null &amp;&amp; agent.IsValid) PlayOnAgent(agent);
            }
            catch (Exception e)
            {
                // User left with _really_ bad timing, don't worry about it.
                Log.Write(LogLevel.Warning, "Voiceover", $"Exception {e.GetType().Name} in OnCollide");
            }
        }
    }

    private void UserLeft(UserData data)
    {
        Stop(data.User);
    }

    public override void Init()
    {
        ScenePrivate.User.Subscribe(User.RemoveUser, UserLeft);
        // Collision events are related to the RigidBody component so we must find it.
        // See if this object has a RigidBodyComponent and grab the first one.
        if (ObjectPrivate.TryGetFirstComponent(out RigidBody))
        {
            Relative_Loudness = (float)Loudness_dB - ZeroDBOffset;

            if (RigidBody.IsTriggerVolume())
            {
                // Subscribe to TriggerVolume collisions on our rigid body: our callback (OnCollide) will get called
                // whenever a character or character vr hand collides with our trigger volume
                RigidBody.Subscribe(CollisionEventType.Trigger, OnCollide);
            }
        }
        else
        {
            // This will write to the region's log files. Only helpful to Linden developers.
            Log.Write("Couldn't find rigid body!");
        }
    }
}
</pre></td></tr></table><p>Event handlers are useful when there is a well contained bit of code which can get most or all of its
            state from the <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a> it receives. In this example, a separate data structure is needed to
            keep track of the name and join time for each agent since there could be multiple agents in the scene.</p><table class="CodeExampleTable"><tr><td><b><font size="-1">C# Example</font></b></td></tr><tr><td><pre class="code-csharp">/* This content is licensed under the terms of the Creative Commons Attribution 4.0 International License.
 * When using this content, you must:
 * �    Acknowledge that the content is from the Sansar Knowledge Base.
 * �    Include our copyright notice: "2022 Sansar, Inc."
 * �    Indicate that the content is licensed under the Creative Commons Attribution-Share Alike 4.0 International License.
 * �    Include the URL for, or link to, the license summary at https://creativecommons.org/licenses/by-sa/4.0/deed.hi (and, if possible, to the complete license terms at https://creativecommons.org/licenses/by-sa/4.0/legalcode.
 * For example:
 * "This work uses content from the Sansar Knowledge Base. � 2022 Sansar, Inc." Licensed under the Creative Commons Attribution 4.0 International License (license summary available at https://creativecommons.org/licenses/by/4.0/ and complete license terms available at https://creativecommons.org/licenses/by/4.0/legalcode)."
 */

/* Use a coroutine to wait for event callbacks to track visitors to a scene.
 * For every visitor start a new coroutine to track that visitor's time.
 */
using System;
using Sansar.Script;
using Sansar.Simulation;
using System.Diagnostics;


// This example shows how to use coroutines to track agents entering and leaving the scene
public class CoroutineExample : SceneObjectScript
{
    public override void Init()
    {
        StartCoroutine(TrackNewUsers);
    }

    // Wait for new users and start a new coroutine to time them.
    private void TrackNewUsers()
    {
        // This coroutine will run for the lifetime of the script.
        while (true)
        {
            // Block until an add user event happens
            UserData data = (UserData)WaitFor(ScenePrivate.User.Subscribe, User.AddUser, SessionId.Invalid);

            // Start a new coroutine to track the new user
            // Each coroutine will run cooperatively with the others, this script, and other scripts in the system
            StartCoroutine(TrackUser, data.User);
        }
    }

    // There will be one instance of this coroutine per active user in the scene
    private void TrackUser(SessionId userId)
    {
        // Track joined time
        long joined = Stopwatch.GetTimestamp();

        // Lookup the name of the agent. This is looked up now since the agent cannot be retrieved after they
        // leave the scene.
        string name = ScenePrivate.FindAgent(userId).AgentInfo.Name;

        // Block until the agent leaves the scene
        WaitFor(ScenePrivate.User.Subscribe, User.RemoveUser, userId);

        // Calculate elapsed time and report.
        TimeSpan elapsed = TimeSpan.FromTicks(Stopwatch.GetTimestamp()-joined);
        ScenePrivate.Chat.MessageAllUsers(string.Format("{0} was present for {0} seconds", name, elapsed.TotalSeconds));
    }
}
</pre></td></tr></table><p>While the amount of code is approximately the same for each, the coroutine version does not require
            access to any member data, simplifying the tracking by moving all the required information to stack
            variables.</p><table class="CodeExampleTable"><tr><td><b><font size="-1">C# Example</font></b></td></tr><tr><td><pre class="code-csharp">/* This content is licensed under the terms of the Creative Commons Attribution 4.0 International License.
 * When using this content, you must:
 * �    Acknowledge that the content is from the Sansar Knowledge Base.
 * �    Include our copyright notice: "2022 Sansar, Inc."
 * �    Indicate that the content is licensed under the Creative Commons Attribution-Share Alike 4.0 International License.
 * �    Include the URL for, or link to, the license summary at https://creativecommons.org/licenses/by-sa/4.0/deed.hi (and, if possible, to the complete license terms at https://creativecommons.org/licenses/by-sa/4.0/legalcode.
 * For example:
 * "This work uses content from the Sansar Knowledge Base. � 2022 Sansar, Inc." Licensed under the Creative Commons Attribution 4.0 International License (license summary available at https://creativecommons.org/licenses/by/4.0/ and complete license terms available at https://creativecommons.org/licenses/by/4.0/legalcode)."
 */

/* Use a coroutine as an event callback to easily track visitors to a scene.
 * Every visitor triggers an AddUser event which will start a coroutine for that visitor.
 */
using System;
using Sansar.Script;
using Sansar.Simulation;
using System.Diagnostics;


// This example shows how to use coroutines and events to track agents entering and leaving the scene
public class CoroutineEventExample : SceneObjectScript
{
    public override void Init()
    {
        // Subscribe to Add User events
        // Events can be handled by anonymous methods
        ScenePrivate.User.Subscribe(User.AddUser, (UserData data) =&gt; StartCoroutine(TrackUser, data.User), true);
    }


    // There will be one instance of this coroutine per active user in the scene
    private void TrackUser(SessionId userId)
    {
        // Track joined time
        long joined = Stopwatch.GetTimestamp();

        // Lookup the name of the agent. This is looked up now since the agent cannot be retrieved after they
        // leave the scene.
        string name = ScenePrivate.FindAgent(userId).AgentInfo.Name;

        // Block until the agent leaves the scene
        WaitFor(ScenePrivate.User.Subscribe, User.RemoveUser, userId);

        // Calculate elapsed time and report.
        TimeSpan elapsed = TimeSpan.FromTicks(Stopwatch.GetTimestamp()-joined);
        ScenePrivate.Chat.MessageAllUsers(string.Format("{0} was present for {0} seconds", name, elapsed.TotalSeconds));
    }
}
</pre></td></tr></table><p>Coroutines can be mixed with event handlers. This example uses an event handler for the new user
            events to start a coroutine to watch for the remove user events.</p></div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.StartCoroutine``1(System.Action{``0},``0,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine&lt;T&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.StartCoroutine``1(System.Action{``0},``0,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Starts a coroutine on the current script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/ICoroutine.html">ICoroutine</a> <b>StartCoroutine&lt;T&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;T&gt;</a> coroutine, <i title="Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T</i> arg1, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``1(System.Action{``0},``0,System.Action{Sansar.Script.OperationCompleteEvent}):Type Parameters">
            <dl>
              <dt>
                <i>T</i>
              </dt>
              <dd>Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``1(System.Action{``0},``0,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>coroutine</i>
              </dt>
              <dd>The coroutine to run.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>First parameter to pass to the coroutine when it is run.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>A callback to know when the coroutine has finished. Will report Success==false only if an exception was thrown by the coroutine. Message will be the name of the coroutine.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``1(System.Action{``0},``0,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">An ICoroutine interface to stop the coroutine or see if it has finished.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``1(System.Action{``0},``0,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">A coroutine can call <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.UInt64})">ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.UInt64&gt;)</a> to block while waiting
            for events to return. See <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent})">ScriptBase.StartCoroutine(Action, Action&lt;OperationCompleteEvent&gt;)</a> for an extended example.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``1(System.Action{``0},``0,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.StartCoroutine``2(System.Action{``0,``1},``0,``1,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine&lt;T,T1&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.StartCoroutine``2(System.Action{``0,``1},``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Starts a coroutine on the current script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/ICoroutine.html">ICoroutine</a> <b>StartCoroutine&lt;T, T1&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`2">Action&lt;T, T1&gt;</a> coroutine, <i title="Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T</i> arg1, <i title="Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T1</i> arg2, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``2(System.Action{``0,``1},``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}):Type Parameters">
            <dl>
              <dt>
                <i>T</i>
              </dt>
              <dd>Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</dd>
              <dt>
                <i>T1</i>
              </dt>
              <dd>Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``2(System.Action{``0,``1},``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>coroutine</i>
              </dt>
              <dd>The coroutine to run.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>First parameter to pass to the coroutine when it is run.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>Second parameter to pass to the coroutine when it is run.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>A callback to know when the coroutine has finished. Will report Success==false only if an exception was thrown by the coroutine. Message will be the name of the coroutine.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``2(System.Action{``0,``1},``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">An ICoroutine interface to stop the coroutine or see if it has finished.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``2(System.Action{``0,``1},``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">A coroutine can call <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.UInt64})">ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.UInt64&gt;)</a> to block while waiting
            for events to return. See <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent})">ScriptBase.StartCoroutine(Action, Action&lt;OperationCompleteEvent&gt;)</a> for an extended example.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``2(System.Action{``0,``1},``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.StartCoroutine``3(System.Action{``0,``1,``2},``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine&lt;T,T1,T2&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.StartCoroutine``3(System.Action{``0,``1,``2},``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Starts a coroutine on the current script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/ICoroutine.html">ICoroutine</a> <b>StartCoroutine&lt;T, T1, T2&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`3">Action&lt;T, T1, T2&gt;</a> coroutine, <i title="Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T</i> arg1, <i title="Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T1</i> arg2, <i title="Type of the third parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T2</i> arg3, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``3(System.Action{``0,``1,``2},``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}):Type Parameters">
            <dl>
              <dt>
                <i>T</i>
              </dt>
              <dd>Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</dd>
              <dt>
                <i>T1</i>
              </dt>
              <dd>Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</dd>
              <dt>
                <i>T2</i>
              </dt>
              <dd>Type of the third parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``3(System.Action{``0,``1,``2},``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>coroutine</i>
              </dt>
              <dd>The coroutine to run.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>First parameter to pass to the coroutine when it is run.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>Second parameter to pass to the coroutine when it is run.</dd>
              <dt>
                <i>arg3</i>
              </dt>
              <dd>Third parameter to pass to the coroutine when it is run.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>A callback to know when the coroutine has finished. Will report Success==false only if an exception was thrown by the coroutine. Message will be the name of the coroutine.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``3(System.Action{``0,``1,``2},``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">An ICoroutine interface to stop the coroutine or see if it has finished.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``3(System.Action{``0,``1,``2},``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">A coroutine can call <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.UInt64})">ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.UInt64&gt;)</a> to block while waiting
            for events to return. See <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent})">ScriptBase.StartCoroutine(Action, Action&lt;OperationCompleteEvent&gt;)</a> for an extended example.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``3(System.Action{``0,``1,``2},``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.StartCoroutine``4(System.Action{``0,``1,``2,``3},``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine&lt;T,T1,T2,T3&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.StartCoroutine``4(System.Action{``0,``1,``2,``3},``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Starts a coroutine on the current script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/ICoroutine.html">ICoroutine</a> <b>StartCoroutine&lt;T, T1, T2, T3&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`4">Action&lt;T, T1, T2, T3&gt;</a> coroutine, <i title="Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T</i> arg1, <i title="Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T1</i> arg2, <i title="Type of the third parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T2</i> arg3, <i title="Type of the fourth parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T3</i> arg4, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``4(System.Action{``0,``1,``2,``3},``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}):Type Parameters">
            <dl>
              <dt>
                <i>T</i>
              </dt>
              <dd>Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</dd>
              <dt>
                <i>T1</i>
              </dt>
              <dd>Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</dd>
              <dt>
                <i>T2</i>
              </dt>
              <dd>Type of the third parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</dd>
              <dt>
                <i>T3</i>
              </dt>
              <dd>Type of the fourth parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``4(System.Action{``0,``1,``2,``3},``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>coroutine</i>
              </dt>
              <dd>The coroutine to run.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>First parameter to pass to the coroutine when it is run.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>Second parameter to pass to the coroutine when it is run.</dd>
              <dt>
                <i>arg3</i>
              </dt>
              <dd>Third parameter to pass to the coroutine when it is run.</dd>
              <dt>
                <i>arg4</i>
              </dt>
              <dd>Fourth parameter to pass to the coroutine when it is run.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>A callback to know when the coroutine has finished. Will report Success==false only if an exception was thrown by the coroutine. Message will be the name of the coroutine.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``4(System.Action{``0,``1,``2,``3},``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">An ICoroutine interface to stop the coroutine or see if it has finished.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``4(System.Action{``0,``1,``2,``3},``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">A coroutine can call <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.UInt64})">ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.UInt64&gt;)</a> to block while waiting 
            for events to return. See <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent})">ScriptBase.StartCoroutine(Action, Action&lt;OperationCompleteEvent&gt;)</a> for an extended example.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.StartCoroutine``4(System.Action{``0,``1,``2,``3},``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean)">SubscribeToScriptEvent Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean):member">
          <div class="msummary">
            Subscribes to events sent by other scripts
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/IEventSubscription.html">IEventSubscription</a> <b>SubscribeToScriptEvent</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> message, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ScriptEventData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>message</i>
              </dt>
              <dd>The event message to listen for.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Handler to call when the event is generated</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Set to false if a one time event is desired.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean):Returns">An IEventSubscription that can be used to Unsubscribe from these script events.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(Sansar.Script.ScriptId,System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean)">SubscribeToScriptEvent Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(Sansar.Script.ScriptId,System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean):member">
          <div class="msummary">
            Subscribes to events sent only by a specific script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/IEventSubscription.html">IEventSubscription</a> <b>SubscribeToScriptEvent</b> (<a href="../Sansar.Script/ScriptId.html">ScriptId</a> sourceScriptId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> message, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ScriptEventData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(Sansar.Script.ScriptId,System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>sourceScriptId</i>
              </dt>
              <dd>Ignore events posted by scripts not matching this id.</dd>
              <dt>
                <i>message</i>
              </dt>
              <dd>The event message to listen for.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Handler to call when the event is generated.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Set to false if a one time event is desired.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(Sansar.Script.ScriptId,System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean):Returns">An IEventSubscription that can be used to Unsubscribe from these script events.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(Sansar.Script.ScriptId,System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(Sansar.Script.ScriptId,System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.Terminate(System.String)">Terminate Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.Terminate(System.String):member">
          <div class="msummary">
            Terminates this script immediately.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Terminate</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> message)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.Terminate(System.String):Parameters">
            <dl>
              <dt>
                <i>message</i>
              </dt>
              <dd>The message to write to the log.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Terminate(System.String):Remarks">This method does not return.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Terminate(System.String):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.Wait(System.Double)">Wait Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.Wait(System.Double):member">
          <div class="msummary">
            Delays execution of the current coroutine for the specified time.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Wait</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Double">double</a> duration)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.Wait(System.Double):Parameters">
            <dl>
              <dt>
                <i>duration</i>
              </dt>
              <dd>The length of time to wait before continuing in seconds.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Wait(System.Double):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Wait(System.Double):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.Wait(System.TimeSpan)">Wait Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.Wait(System.TimeSpan):member">
          <div class="msummary">
            Delays execution of the current coroutine for the specified time.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Wait</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.TimeSpan">TimeSpan</a> duration)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.Wait(System.TimeSpan):Parameters">
            <dl>
              <dt>
                <i>duration</i>
              </dt>
              <dd>The length of time to wait before continuing.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Wait(System.TimeSpan):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Wait(System.TimeSpan):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor(Sansar.Script.ICoroutine)">WaitFor Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor(Sansar.Script.ICoroutine):member">
          <div class="msummary">
            Block the current coroutine until otherCoroutine finishes.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>WaitFor</b> (<a href="../Sansar.Script/ICoroutine.html">ICoroutine</a> otherCoroutine)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(Sansar.Script.ICoroutine):Parameters">
            <dl>
              <dt>
                <i>otherCoroutine</i>
              </dt>
              <dd>The coroutine to wait for.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(Sansar.Script.ICoroutine):Remarks">Waiting for the current coroutine, a null coroutine or a coroutine that is not alive will immediately return.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(Sansar.Script.ICoroutine):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor(System.Action{System.Action{Sansar.Script.OperationCompleteEvent}})">WaitFor Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor(System.Action{System.Action{Sansar.Script.OperationCompleteEvent}}):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/OperationCompleteEvent.html">OperationCompleteEvent</a> <b>WaitFor</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Action&lt;OperationCompleteEvent&gt;&gt;</a> func)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Action{System.Action{Sansar.Script.OperationCompleteEvent}}):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Action{System.Action{Sansar.Script.OperationCompleteEvent}}):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Action{System.Action{Sansar.Script.OperationCompleteEvent}}):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Action{System.Action{Sansar.Script.OperationCompleteEvent}}):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription})">WaitFor Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription}):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`4">Func&lt;Action&lt;EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription}):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription}):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription}):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription}):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">WaitFor Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription}):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`3">Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription}):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription}):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription}):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription}):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},System.Action)">WaitFor Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},System.Action):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`3">Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a> run)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},System.Action):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>run</i>
              </dt>
              <dd>An Action to run after subscribing the the event, but before waiting for the event to occurr.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},System.Action):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},System.Action):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},System.Action):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0)">WaitFor&lt;ARG1&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`5">Func&lt;ARG1, Action&lt;EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0)">WaitFor&lt;ARG1&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`4">Func&lt;ARG1, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,System.Action)">WaitFor&lt;ARG1&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,System.Action):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`4">Func&lt;ARG1, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a> run)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,System.Action):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,System.Action):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>run</i>
              </dt>
              <dd>An Action to run after subscribing the the event, but before waiting for the event to occurr.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,System.Action):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,System.Action):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,System.Action):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Action{``0,System.Action{Sansar.Script.OperationCompleteEvent}},``0)">WaitFor&lt;T1&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Action{``0,System.Action{Sansar.Script.OperationCompleteEvent}},``0):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/OperationCompleteEvent.html">OperationCompleteEvent</a> <b>WaitFor&lt;T1&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`2">Action&lt;T1, Action&lt;OperationCompleteEvent&gt;&gt;</a> func, <i title="The type of the argument to func.">T1</i> t1)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Action{``0,System.Action{Sansar.Script.OperationCompleteEvent}},``0):Type Parameters">
            <dl>
              <dt>
                <i>T1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Action{``0,System.Action{Sansar.Script.OperationCompleteEvent}},``0):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>t1</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Action{``0,System.Action{Sansar.Script.OperationCompleteEvent}},``0):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Action{``0,System.Action{Sansar.Script.OperationCompleteEvent}},``0):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``1(System.Action{``0,System.Action{Sansar.Script.OperationCompleteEvent}},``0):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1)">WaitFor&lt;ARG1,ARG2&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1, ARG2&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`6">Func&lt;ARG1, ARG2, Action&lt;EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <i title="The type of the argument to func.">ARG2</i> arg2)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1)">WaitFor&lt;ARG1,ARG2&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1, ARG2&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`5">Func&lt;ARG1, ARG2, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <i title="The type of the argument to func.">ARG2</i> arg2)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,System.Action)">WaitFor&lt;ARG1,ARG2&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,System.Action):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1, ARG2&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`5">Func&lt;ARG1, ARG2, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <i title="The type of the argument to func.">ARG2</i> arg2, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a> run)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,System.Action):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,System.Action):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>run</i>
              </dt>
              <dd>An Action to run after subscribing the the event, but before waiting for the event to occurr.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,System.Action):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,System.Action):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,System.Action):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Action{``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1)">WaitFor&lt;T1,T2&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Action{``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/OperationCompleteEvent.html">OperationCompleteEvent</a> <b>WaitFor&lt;T1, T2&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`3">Action&lt;T1, T2, Action&lt;OperationCompleteEvent&gt;&gt;</a> func, <i title="The type of the argument to func.">T1</i> t1, <i title="The type of the argument to func.">T2</i> t2)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Action{``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1):Type Parameters">
            <dl>
              <dt>
                <i>T1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>T2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Action{``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>t1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>t2</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Action{``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Action{``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``2(System.Action{``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2)">WaitFor&lt;ARG1,ARG2,ARG3&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1, ARG2, ARG3&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`7">Func&lt;ARG1, ARG2, ARG3, Action&lt;EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <i title="The type of the argument to func.">ARG2</i> arg2, <i title="The type of the argument to func.">ARG3</i> arg3)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG3</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg3</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2)">WaitFor&lt;ARG1,ARG2,ARG3&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1, ARG2, ARG3&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`6">Func&lt;ARG1, ARG2, ARG3, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <i title="The type of the argument to func.">ARG2</i> arg2, <i title="The type of the argument to func.">ARG3</i> arg3)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG3</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg3</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,System.Action)">WaitFor&lt;ARG1,ARG2,ARG3&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,System.Action):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1, ARG2, ARG3&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`6">Func&lt;ARG1, ARG2, ARG3, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <i title="The type of the argument to func.">ARG2</i> arg2, <i title="The type of the argument to func.">ARG3</i> arg3, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a> run)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,System.Action):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG3</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,System.Action):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg3</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>run</i>
              </dt>
              <dd>An Action to run after subscribing the the event, but before waiting for the event to occurr.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,System.Action):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,System.Action):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,System.Action):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Action{``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2)">WaitFor&lt;T1,T2,T3&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Action{``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/OperationCompleteEvent.html">OperationCompleteEvent</a> <b>WaitFor&lt;T1, T2, T3&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`4">Action&lt;T1, T2, T3, Action&lt;OperationCompleteEvent&gt;&gt;</a> func, <i title="The type of the argument to func.">T1</i> t1, <i title="The type of the argument to func.">T2</i> t2, <i title="The type of the argument to func.">T3</i> t3)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Action{``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2):Type Parameters">
            <dl>
              <dt>
                <i>T1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>T2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>T3</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Action{``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>t1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>t2</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>t3</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Action{``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Action{``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``3(System.Action{``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1, ARG2, ARG3, ARG4&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`8">Func&lt;ARG1, ARG2, ARG3, ARG4, Action&lt;EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <i title="The type of the argument to func.">ARG2</i> arg2, <i title="The type of the argument to func.">ARG3</i> arg3, <i title="The type of the argument to func.">ARG4</i> arg4)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG3</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG4</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg3</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg4</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1, ARG2, ARG3, ARG4&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`7">Func&lt;ARG1, ARG2, ARG3, ARG4, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <i title="The type of the argument to func.">ARG2</i> arg2, <i title="The type of the argument to func.">ARG3</i> arg3, <i title="The type of the argument to func.">ARG4</i> arg4)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG3</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG4</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg3</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg4</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,System.Action)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,System.Action):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1, ARG2, ARG3, ARG4&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`7">Func&lt;ARG1, ARG2, ARG3, ARG4, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <i title="The type of the argument to func.">ARG2</i> arg2, <i title="The type of the argument to func.">ARG3</i> arg3, <i title="The type of the argument to func.">ARG4</i> arg4, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a> run)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,System.Action):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG3</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG4</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,System.Action):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg3</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg4</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>run</i>
              </dt>
              <dd>An Action to run after subscribing the the event, but before waiting for the event to occurr.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,System.Action):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,System.Action):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,System.Action):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Action{``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2,``3)">WaitFor&lt;T1,T2,T3,T4&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Action{``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2,``3):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/OperationCompleteEvent.html">OperationCompleteEvent</a> <b>WaitFor&lt;T1, T2, T3, T4&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`5">Action&lt;T1, T2, T3, T4, Action&lt;OperationCompleteEvent&gt;&gt;</a> func, <i title="The type of the argument to func.">T1</i> t1, <i title="The type of the argument to func.">T2</i> t2, <i title="The type of the argument to func.">T3</i> t3, <i title="The type of the argument to func.">T4</i> t4)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Action{``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2,``3):Type Parameters">
            <dl>
              <dt>
                <i>T1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>T2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>T3</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>T4</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Action{``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2,``3):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>t1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>t2</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>t3</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>t4</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Action{``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2,``3):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Action{``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2,``3):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``4(System.Action{``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2,``3):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4,ARG5&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1, ARG2, ARG3, ARG4, ARG5&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`9">Func&lt;ARG1, ARG2, ARG3, ARG4, ARG5, Action&lt;EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <i title="The type of the argument to func.">ARG2</i> arg2, <i title="The type of the argument to func.">ARG3</i> arg3, <i title="The type of the argument to func.">ARG4</i> arg4, <i title="The type of the argument to func.">ARG5</i> arg5)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG3</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG4</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG5</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg3</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg4</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg5</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4,ARG5&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1, ARG2, ARG3, ARG4, ARG5&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`8">Func&lt;ARG1, ARG2, ARG3, ARG4, ARG5, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <i title="The type of the argument to func.">ARG2</i> arg2, <i title="The type of the argument to func.">ARG3</i> arg3, <i title="The type of the argument to func.">ARG4</i> arg4, <i title="The type of the argument to func.">ARG5</i> arg5)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG3</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG4</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG5</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg3</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg4</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg5</i>
              </dt>
              <dd>An argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4,System.Action)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4,ARG5&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4,System.Action):member">
          <div class="msummary">
            Use to pause the script until an event happens.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Script/EventData.html">EventData</a> <b>WaitFor&lt;ARG1, ARG2, ARG3, ARG4, ARG5&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`8">Func&lt;ARG1, ARG2, ARG3, ARG4, ARG5, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a> func, <i title="The type of the argument to func.">ARG1</i> arg1, <i title="The type of the argument to func.">ARG2</i> arg2, <i title="The type of the argument to func.">ARG3</i> arg3, <i title="The type of the argument to func.">ARG4</i> arg4, <i title="The type of the argument to func.">ARG5</i> arg5, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a> run)</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4,System.Action):Type Parameters">
            <dl>
              <dt>
                <i>ARG1</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG2</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG3</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG4</i>
              </dt>
              <dd>The type of the argument to func.</dd>
              <dt>
                <i>ARG5</i>
              </dt>
              <dd>The type of the argument to func.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4,System.Action):Parameters">
            <dl>
              <dt>
                <i>func</i>
              </dt>
              <dd>The event-generating API to wait for.</dd>
              <dt>
                <i>arg1</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg2</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg3</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg4</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>arg5</i>
              </dt>
              <dd>An argument to func.</dd>
              <dt>
                <i>run</i>
              </dt>
              <dd>An Action to run after subscribing the the event, but before waiting for the event to occurr.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4,System.Action):Returns">An EventData that can be case to type corresponding to the event type of func.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4,System.Action):Remarks">Throws <a href="../Sansar.Script/CoroutineException.html">Sansar.Script.CoroutineException</a> if called from a script's constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4,System.Action):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitForLock(System.Object)">WaitForLock Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitForLock(System.Object):member">
          <div class="msummary">
            WaitFor and take a lock on Token
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>WaitForLock</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a> Token)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitForLock(System.Object):See Also">
            <div>
              <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Lock(System.Object)">ScriptBase.Lock(object)</a>
            </div>
            <div>
              <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.ReleaseLock(System.Object)">ScriptBase.ReleaseLock(object)</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitForLock(System.Object):Parameters">
            <dl>
              <dt>
                <i>Token</i>
              </dt>
              <dd>The object controlling the lock. Recommended to be a private object member.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Exceptions</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitForLock(System.Object):Exceptions">
            <table class="TypeDocumentation">
              <tr>
                <th>Type</th>
                <th>Reason</th>
              </tr>
              <tr valign="top">
                <td>
                  <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.ArgumentNullException">ArgumentNullException</a>
                </td>
                <td>Thrown if Token is null.</td>
              </tr>
            </table>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitForLock(System.Object):Remarks">It is highly recommended to use <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Lock(System.Object)">ScriptBase.Lock(object)</a> instead. This is for advanced use only and can easily create deadlocks that stop scripts from working. If not paired with <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.ReleaseLock(System.Object)">ScriptBase.ReleaseLock(object)</a> will create a deadlock preventing this code from being entered.</div>
          <h2 class="Section">Example</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitForLock(System.Object):Example:1">
            <table class="CodeExampleTable">
              <tr>
                <td>
                  <b>
                    <font size="-1">C# Example</font>
                  </b>
                </td>
              </tr>
              <tr>
                <td>
                  <pre class="code-csharp">
            private object BalanceLock = new Object();
            int Balance = 100;
            bool Spend(int value)
            {
                WaitForLock(BalanceLock);
                bool success = false;
                if (Balance &gt; value)
                {
                    Balance -= value;
                    success = true;
                }
                ReleaseLock(BalanceLock);
                // Note that any early exit between WaitForLock and ReleaseLock fail to release the lock, creating a deadlock the next time this method is called.
                // For this reason it is highly recommended to instead do: using (Lock(BalanceLock))
                return success;
            }</pre>
                </td>
              </tr>
            </table>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitForLock(System.Object):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.WaitForSignal()">WaitForSignal Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.WaitForSignal():member">
          <div class="msummary">
            Wait the current coroutine until at least 1 signal is sent to it through the ICoroutine interface.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>WaitForSignal</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ScriptBase.WaitForSignal():Returns">The number of signals received while waiting.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitForSignal():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.WaitForSignal():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ScriptBase.Yield()">Yield Method</h3>
        <blockquote id="M:Sansar.Script.ScriptBase.Yield():member">
          <div class="msummary">
            Yield to let other coroutines or events run.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Yield</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Yield():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ScriptBase.Yield():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>