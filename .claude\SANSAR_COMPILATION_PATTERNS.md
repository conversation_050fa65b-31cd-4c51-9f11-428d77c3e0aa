# Sansar Compilation Patterns and API Reference

## Critical Compatibility Patterns

### 1. Color Type Usage
**Issue**: `Color` type not found errors  
**Solution**: Use fully qualified `Sansar.Color`

```csharp
// ✅ Correct
private static readonly Sansar.Color PieceColor = new Sansar.Color(1.0f, 0.0f, 0.0f, 1.0f);
private static readonly Dictionary<PieceType, Sansar.Color> Colors = CreateColors();

// ❌ Incorrect  
private static readonly Color PieceColor = new Color(1.0f, 0.0f, 0.0f, 1.0f);
```

Available Sansar.Color features:
- Constructors: `new Sansar.Color(r, g, b)` and `new Sansar.Color(r, g, b, a)`
- Predefined: `Sansar.Color.White`, `Sansar.Color.Red`, etc.
- Methods: `Sansar.Color.Random()`, `.ToRGB()`, `.ToRGBA()`

### 2. Event Data Parsing
**Issue**: `Reflective does not contain a definition for Split`  
**Solution**: Convert to string first using `.ToString()`

```csharp
// ✅ Correct
private void OnEvent(ScriptEventData data)
{
    if (data.Data != null)
    {
        string dataString = data.Data.ToString();
        string[] parts = dataString.Split(',');
        // Process parts...
    }
}

// ❌ Incorrect
private void OnEvent(ScriptEventData data)
{
    string[] parts = data.Data.Split(','); // ERROR
}
```

### 3. C# 5 Dictionary Initialization
**Issue**: Dictionary initializer syntax not supported  
**Solution**: Use traditional `.Add()` method

```csharp
// ✅ Correct - C# 5 compatible
private static Dictionary<PieceType, Sansar.Color> CreateColors()
{
    var colors = new Dictionary<PieceType, Sansar.Color>();
    colors.Add(PieceType.I, new Sansar.Color(0.0f, 1.0f, 1.0f, 1.0f));
    colors.Add(PieceType.O, new Sansar.Color(1.0f, 1.0f, 0.0f, 1.0f));
    return colors;
}

// ❌ Incorrect - Not supported in C# 5
private static readonly Dictionary<PieceType, Sansar.Color> Colors = new Dictionary<PieceType, Sansar.Color>
{
    [PieceType.I] = new Sansar.Color(0.0f, 1.0f, 1.0f, 1.0f),
    [PieceType.O] = new Sansar.Color(1.0f, 1.0f, 0.0f, 1.0f)
};
```

### 4. String Interpolation
**Issue**: String interpolation not supported in C# 5  
**Solution**: Use `string.Format()`

```csharp
// ✅ Correct
Log.Write(string.Format("Block at ({0}, {1})", x, y));

// ❌ Incorrect
Log.Write($"Block at ({x}, {y})");
```

### 5. Expression-Bodied Members
**Issue**: `=>` syntax not supported in C# 5  
**Solution**: Use traditional method bodies

```csharp
// ✅ Correct
public int GetWidth() { return GRID_WIDTH; }

// ❌ Incorrect
public int GetWidth() => GRID_WIDTH;
```

### 6. Wait() Calls
**Issue**: Wait() requires TimeSpan parameter  
**Solution**: Use `TimeSpan.FromSeconds()`

```csharp
// ✅ Correct
Wait(TimeSpan.FromSeconds(0.5));

// ❌ Incorrect
Wait(0.5);
```

### 7. DateTime Usage
**Issue**: DateTime.UtcNow not available  
**Solution**: Use Stopwatch for timing

```csharp
// ✅ Correct
long startTicks = System.Diagnostics.Stopwatch.GetTimestamp();
double ticksPerSecond = System.Diagnostics.Stopwatch.Frequency;
double elapsed = (System.Diagnostics.Stopwatch.GetTimestamp() - startTicks) / ticksPerSecond;

// ❌ Incorrect
DateTime start = DateTime.UtcNow;
double elapsed = (DateTime.UtcNow - start).TotalSeconds;
```

### 8. Event Data Classes
**Issue**: SimpleScriptEventData not available  
**Solution**: Create custom classes implementing ISimpleData

```csharp
// ✅ Correct
public interface ISimpleData
{
    AgentInfo AgentInfo { get; }
    ObjectId ObjectId { get; }
    ObjectId SourceObjectId { get; }
    Reflective ExtraData { get; }
}

public class CustomEventData : Reflective, ISimpleData
{
    private readonly Reflective _extraData;
    public CustomEventData(ScriptBase script) { _extraData = script; }
    public AgentInfo AgentInfo { get; set; }
    public ObjectId ObjectId { get; set; }
    public ObjectId SourceObjectId { get; set; }
    public Reflective ExtraData { get { return _extraData; } }
    public string Message { get; set; }
}
```

## Compilation Testing Strategy

1. **Local Compilation**: For syntax checking only
   - Use .NET Framework 4.x C# compiler  
   - Reference Sansar.Script.dll and Sansar.Simulation.dll
   - Expect Vector arithmetic errors (normal)

2. **Sansar Upload**: For full functionality testing
   - Vector operations work correctly
   - Material/Color operations work correctly
   - Event system works correctly

## Verified Working Scripts
- ✅ GameManager.cs - Core game logic
- ✅ BlockController.cs - Material control with Sansar.Color
- ✅ GridManager.cs - Grid management with custom event data
- ✅ PieceController.cs - Piece movement with Vector operations
- ✅ PieceLogic.cs - Utility functions
- ✅ SeatController.cs - Player seating system