<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Sansar.Script</name>
    </assembly>
    <members>
        <member name="T:Sansar.Script.ScriptBase">
            <summary>
            Base class for all scripts, has ScriptHandle accessors and coroutine utilities.
            </summary>
            <remarks>This abstract class manages shared information for all script types.</remarks>
        </member>
        <member name="T:Sansar.Script.ScriptBase.OperationComplete">
            <summary>
            Used to obtain notification that the operation has completed.
            </summary>
            <param name="success">true if the operation completed successfully.</param>
            <param name="failureMessage">A message describing the failure if success if false.</param>
            <remarks>The failure message is intended to be used for debugging and may change without notice.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.#ctor">
            <summary>
            Constructor
            </summary>
            <remarks>Initializes a new instance of the ScriptBase class.</remarks>
        </member>
        <member name="P:Sansar.Script.ScriptBase.ErrorMode">
            <summary>
            
            </summary>
        </member>
        <member name="M:Sansar.Script.ScriptBase.SetRelaxedErrorMode">
            <summary>
            Write errors to the Script Debug Console instead of throw exceptions for almost all Sansar API errors.
            </summary>
        </member>
        <member name="M:Sansar.Script.ScriptBase.SetStrictErrorMode">
            <summary>
            Throw exceptions for almost all Sansar API errors.
            </summary>
        </member>
        <member name="M:Sansar.Script.ScriptBase.SetDefaultErrorMode">
            <summary>
            Write errors to the Script Debug Console instead of throwing exceptions for some common API errors while still throwing exceptions for the more serious errors.
            </summary>
        </member>
        <member name="M:Sansar.Script.ScriptBase.Init">
            <summary> 
            Init() is called after all interfaces have been initialized.
            </summary>
            <remarks>
            Override Init() to set up script subscriptions and callbacks. 
            Any members set before Init is called (such as in a script constructor) may be overwritten before Init is called.
            </remarks>
        </member>
        <member name="P:Sansar.Script.ScriptBase.Script">
            <summary>
            Script handle to this script.
            </summary>
            <value>An opaque type used by certain API methods.</value>
            <remarks></remarks>
        </member>
        <member name="P:Sansar.Script.ScriptBase.PendingEventCount">
            <summary>
            The number of events currently waiting to be processed.
            </summary>
            <value>The number of events currently waiting to be processed.</value>
            <remarks>If this number grows it indicates the script is not processing events as fast as it is receiving them which will bloat memory and is generally a sign of poor performance.
            <para>To remedy a situation of growing pending event count try reducing the number or rate of events the script is receiving or the amount of work done in each event.</para></remarks>
        </member>
        <member name="P:Sansar.Script.ScriptBase.Memory">
            <summary>
            Memory information for the pool this script is in.
            </summary>
            <value></value>
            <remarks>Scripts are pooled by their owner and share memory. Use this object to get events on nearing memory limits as well as get information on memory activity and use.</remarks>
        </member>
        <member name="P:Sansar.Script.ScriptBase.Log">
            <summary>
            Gets the script console.
            </summary>
            <value></value>
            <remarks/>
        </member>
        <member name="P:Sansar.Script.ScriptBase.CurrentCoroutine">
            <summary>
            Gets the ICoroutine interface for the current coroutine.
            </summary><remarks/>
            <value>The ICoroutine interface to the currently running coroutine.</value>
        </member>
        <member name="M:Sansar.Script.ScriptBase.GetAllCoroutines">
            <summary>
            A list of all coroutines for this script.
            </summary>
            <returns>A list containing all running or waiting coroutines.</returns><remarks></remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.GetCoroutineCount">
            <summary>
            The total number of coroutines running on this script.
            </summary>
            <returns>The total number of coroutines running on this script.</returns>
            <remarks>Includes currently active and waiting coroutines, but not killed coroutines. A new list is created every time it is called with the coroutines at the time of the call. It is possible for coroutines to finish while iterating on the list which will not remove them from the list. Any finished coroutines still in the list will have IsAlive == false.</remarks>
        </member>
        <member name="P:Sansar.Script.ScriptBase.MaxCoroutines">
            <summary>
            The maximum number of coroutines that a single script can run.
            </summary><remarks>If GetCoroutineCount() == MaxCoroutines, no more coroutines will be started.</remarks>
            <value>The maximum number of coroutines that a single script can run.</value>
        </member>
        <member name="M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent})">
            <summary>
            Starts a coroutine on the current script.
            </summary>
            <param name="coroutine">The coroutine to run.</param>
            <param name="handler">A callback to know when the coroutine has finished.</param>
            <returns>An ICoroutine interface to stop the coroutine or see if it has finished.</returns>
            <remarks>A coroutine can call <see cref="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.UInt64})"/> to block while waiting
            for events to return.
            <para>The following three scripts acccomplish the same goal using event subscriptions and coroutines.
            Each script will listen for new agents to join the scene and keep track of the agents name and the time it
            joined. When the agent leaves, its name and the duration of its visit will be reported.</para>
            <example>
            <code lang="C#" src="examples/EventExample.cs"/>
            </example>
            <para>Event handlers are useful when there is a well contained bit of code which can get most or all of its
            state from the <see cref="T:Sansar.Script.EventData"/> it receives. In this example, a separate data structure is needed to
            keep track of the name and join time for each agent since there could be multiple agents in the scene.</para>
            <example>
            <code lang="C#" src="examples/CoroutineExample.cs"/>
            </example>
            <para>While the amount of code is approximately the same for each, the coroutine version does not require
            access to any member data, simplifying the tracking by moving all the required information to stack
            variables.</para>
            <example>
            <code lang="C#" src="examples/CoroutineEventExample.cs"/>
            </example>
            <para>Coroutines can be mixed with event handlers. This example uses an event handler for the new user
            events to start a coroutine to watch for the remove user events.</para>
            </remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.StartCoroutine``1(System.Action{``0},``0,System.Action{Sansar.Script.OperationCompleteEvent})">
            <summary>
            Starts a coroutine on the current script.
            </summary>
            <param name="coroutine">The coroutine to run.</param>
            <param name="arg1">First parameter to pass to the coroutine when it is run.</param>
            <param name="handler">A callback to know when the coroutine has finished. Will report Success==false only if an exception was thrown by the coroutine. Message will be the name of the coroutine.</param>
            <returns>An ICoroutine interface to stop the coroutine or see if it has finished.</returns>
            <typeparam name="T">Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</typeparam>
            <remarks>A coroutine can call <see cref="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.UInt64})" /> to block while waiting
            for events to return. See <see cref="M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent})"/> for an extended example.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.StartCoroutine``2(System.Action{``0,``1},``0,``1,System.Action{Sansar.Script.OperationCompleteEvent})">
            <summary>
            Starts a coroutine on the current script.
            </summary>
            <param name="coroutine">The coroutine to run.</param>
            <param name="arg1">First parameter to pass to the coroutine when it is run.</param>
            <param name="arg2">Second parameter to pass to the coroutine when it is run.</param>
            <param name="handler">A callback to know when the coroutine has finished. Will report Success==false only if an exception was thrown by the coroutine. Message will be the name of the coroutine.</param>
            <returns>An ICoroutine interface to stop the coroutine or see if it has finished.</returns>
            <typeparam name="T">Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</typeparam>
            <typeparam name="T1">Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</typeparam>
            <remarks>A coroutine can call <see cref="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.UInt64})"/> to block while waiting
            for events to return. See <see cref="M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent})"/> for an extended example.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.StartCoroutine``3(System.Action{``0,``1,``2},``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent})">
            <summary>
            Starts a coroutine on the current script.
            </summary>
            <param name="coroutine">The coroutine to run.</param>
            <param name="arg1">First parameter to pass to the coroutine when it is run.</param>
            <param name="arg2">Second parameter to pass to the coroutine when it is run.</param>
            <param name="arg3">Third parameter to pass to the coroutine when it is run.</param>
            <param name="handler">A callback to know when the coroutine has finished. Will report Success==false only if an exception was thrown by the coroutine. Message will be the name of the coroutine.</param>
            <returns>An ICoroutine interface to stop the coroutine or see if it has finished.</returns>
            <typeparam name="T">Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</typeparam>
            <typeparam name="T1">Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</typeparam>
            <typeparam name="T2">Type of the third parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</typeparam>
            <remarks>A coroutine can call <see cref="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.UInt64})"/> to block while waiting
            for events to return. See <see cref="M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent})"/> for an extended example.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.StartCoroutine``4(System.Action{``0,``1,``2,``3},``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent})">
            <summary>
            Starts a coroutine on the current script.
            </summary>
            <param name="coroutine">The coroutine to run.</param>
            <param name="arg1">First parameter to pass to the coroutine when it is run.</param>
            <param name="arg2">Second parameter to pass to the coroutine when it is run.</param>
            <param name="arg3">Third parameter to pass to the coroutine when it is run.</param>
            <param name="arg4">Fourth parameter to pass to the coroutine when it is run.</param>
            <param name="handler">A callback to know when the coroutine has finished. Will report Success==false only if an exception was thrown by the coroutine. Message will be the name of the coroutine.</param>
            <returns>An ICoroutine interface to stop the coroutine or see if it has finished.</returns>
            <typeparam name="T">Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</typeparam>
            <typeparam name="T1">Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</typeparam>
            <typeparam name="T2">Type of the third parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</typeparam>
            <typeparam name="T3">Type of the fourth parameter to pass to the coroutine when it is run. This can usually be derived from the Action.</typeparam>
            <remarks>A coroutine can call <see cref="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.UInt64})"/> to block while waiting 
            for events to return. See <see cref="M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent})"/> for an extended example.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <param name="func">The event-generating API to wait for.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},System.Action)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <param name="func">The event-generating API to wait for.</param>
            <param name="run">An Action to run after subscribing the the event, but before waiting for the event to occurr.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,System.Action)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="run">An Action to run after subscribing the the event, but before waiting for the event to occurr.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <typeparam name="ARG2">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="arg2">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,System.Action)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <typeparam name="ARG2">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="arg2">An argument to func.</param>
            <param name="run">An Action to run after subscribing the the event, but before waiting for the event to occurr.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <typeparam name="ARG2">The type of the argument to func.</typeparam>
            <typeparam name="ARG3">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="arg2">An argument to func.</param>
            <param name="arg3">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,System.Action)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <typeparam name="ARG2">The type of the argument to func.</typeparam>
            <typeparam name="ARG3">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="arg2">An argument to func.</param>
            <param name="arg3">An argument to func.</param>
            <param name="run">An Action to run after subscribing the the event, but before waiting for the event to occurr.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <typeparam name="ARG2">The type of the argument to func.</typeparam>
            <typeparam name="ARG3">The type of the argument to func.</typeparam>
            <typeparam name="ARG4">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="arg2">An argument to func.</param>
            <param name="arg3">An argument to func.</param>
            <param name="arg4">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,System.Action)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <typeparam name="ARG2">The type of the argument to func.</typeparam>
            <typeparam name="ARG3">The type of the argument to func.</typeparam>
            <typeparam name="ARG4">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="arg2">An argument to func.</param>
            <param name="arg3">An argument to func.</param>
            <param name="arg4">An argument to func.</param>
            <param name="run">An Action to run after subscribing the the event, but before waiting for the event to occurr.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <typeparam name="ARG2">The type of the argument to func.</typeparam>
            <typeparam name="ARG3">The type of the argument to func.</typeparam>
            <typeparam name="ARG4">The type of the argument to func.</typeparam>
            <typeparam name="ARG5">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="arg2">An argument to func.</param>
            <param name="arg3">An argument to func.</param>
            <param name="arg4">An argument to func.</param>
            <param name="arg5">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4,System.Action)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <typeparam name="ARG2">The type of the argument to func.</typeparam>
            <typeparam name="ARG3">The type of the argument to func.</typeparam>
            <typeparam name="ARG4">The type of the argument to func.</typeparam>
            <typeparam name="ARG5">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="arg2">An argument to func.</param>
            <param name="arg3">An argument to func.</param>
            <param name="arg4">An argument to func.</param>
            <param name="arg5">An argument to func.</param>
            <param name="run">An Action to run after subscribing the the event, but before waiting for the event to occurr.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor(System.Action{System.Action{Sansar.Script.OperationCompleteEvent}})">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <param name="func">The event-generating API to wait for.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``1(System.Action{``0,System.Action{Sansar.Script.OperationCompleteEvent}},``0)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="T1">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="t1">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``2(System.Action{``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="T1">The type of the argument to func.</typeparam>
            <typeparam name="T2">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="t1">An argument to func.</param>
            <param name="t2">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``3(System.Action{``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="T1">The type of the argument to func.</typeparam>
            <typeparam name="T2">The type of the argument to func.</typeparam>
            <typeparam name="T3">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="t1">An argument to func.</param>
            <param name="t2">An argument to func.</param>
            <param name="t3">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``4(System.Action{``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2,``3)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="T1">The type of the argument to func.</typeparam>
            <typeparam name="T2">The type of the argument to func.</typeparam>
            <typeparam name="T3">The type of the argument to func.</typeparam>
            <typeparam name="T4">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="t1">An argument to func.</param>
            <param name="t2">An argument to func.</param>
            <param name="t3">An argument to func.</param>
            <param name="t4">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription})">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <param name="func">The event-generating API to wait for.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <typeparam name="ARG2">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="arg2">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <typeparam name="ARG2">The type of the argument to func.</typeparam>
            <typeparam name="ARG3">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="arg2">An argument to func.</param>
            <param name="arg3">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <typeparam name="ARG2">The type of the argument to func.</typeparam>
            <typeparam name="ARG3">The type of the argument to func.</typeparam>
            <typeparam name="ARG4">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="arg2">An argument to func.</param>
            <param name="arg3">An argument to func.</param>
            <param name="arg4">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4)">
            <summary>
            Use to pause the script until an event happens.
            </summary>
            <typeparam name="ARG1">The type of the argument to func.</typeparam>
            <typeparam name="ARG2">The type of the argument to func.</typeparam>
            <typeparam name="ARG3">The type of the argument to func.</typeparam>
            <typeparam name="ARG4">The type of the argument to func.</typeparam>
            <typeparam name="ARG5">The type of the argument to func.</typeparam>
            <param name="func">The event-generating API to wait for.</param>
            <param name="arg1">An argument to func.</param>
            <param name="arg2">An argument to func.</param>
            <param name="arg3">An argument to func.</param>
            <param name="arg4">An argument to func.</param>
            <param name="arg5">An argument to func.</param>
            <returns>An EventData that can be case to type corresponding to the event type of func.</returns>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitForSignal">
            <summary>
            Wait the current coroutine until at least 1 signal is sent to it through the ICoroutine interface.
            </summary>
            <returns>The number of signals received while waiting.</returns><remarks/>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitFor(Sansar.Script.ICoroutine)">
            <summary>
            Block the current coroutine until otherCoroutine finishes.
            </summary>
            <param name="otherCoroutine">The coroutine to wait for.</param>
            <remarks>Waiting for the current coroutine, a null coroutine or a coroutine that is not alive will immediately return.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.Yield">
            <summary>
            Yield to let other coroutines or events run.
            </summary>
            <remarks />
        </member>
        <member name="M:Sansar.Script.ScriptBase.Wait(System.TimeSpan)">
            <summary>
            Delays execution of the current coroutine for the specified time.
            </summary>
            <param name="duration">The length of time to wait before continuing.</param>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.Wait(System.Double)">
            <summary>
            Delays execution of the current coroutine for the specified time.
            </summary>
            <param name="duration">The length of time to wait before continuing in seconds.</param>
            <remarks>Throws <see cref="T:Sansar.Script.CoroutineException"/> if called from a script's constructor.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.Terminate(System.String)">
            <summary>
            Terminates this script immediately.
            </summary>
            <param name="message">The message to write to the log.</param>
            <remarks>This method does not return.</remarks>
        </member>
        <member name="P:Sansar.Script.ScriptBase.ScriptEventMessageIdCount">
            <summary>
            Obsolete
            </summary>
            <returns>Always returns 0</returns>
            <remarks>No longer used as there is no longer a limited number of message ids. Obsoleted 2018-04.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean)">
            <summary>
            Subscribes to events sent by other scripts
            </summary>
            <param name="message">The event message to listen for.</param>
            <param name="callback">Handler to call when the event is generated</param>
            <param name="persistent">Set to false if a one time event is desired.</param>
            <returns>An IEventSubscription that can be used to Unsubscribe from these script events.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(Sansar.Script.ScriptId,System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean)">
            <summary>
            Subscribes to events sent only by a specific script.
            </summary>
            <param name="sourceScriptId">Ignore events posted by scripts not matching this id.</param>
            <param name="message">The event message to listen for.</param>
            <param name="callback">Handler to call when the event is generated.</param>
            <param name="persistent">Set to false if a one time event is desired.</param>
            <returns>An IEventSubscription that can be used to Unsubscribe from these script events.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String,Sansar.Script.Reflective)">
            <summary>
            Post the event for all scripts.
            </summary>
            <param name="message">The message id used to direct the event.</param>
            <param name="data">The object to be post.</param>
            <remarks/>
        </member>
        <member name="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String)">
            <summary>
            Post the event for all scripts.
            </summary>
            <param name="message">The message id used to direct the event.</param>
            <remarks/>
        </member>
        <member name="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">
            <summary>
            Post the event for the target script.
            </summary>
            <param name="targetScriptId">The id of the script to sent the event to. To broadcast the message to all subscripted scripts use <see cref="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String,Sansar.Script.Reflective)"/>. </param>
            <param name="message">The message id used to direct the event.</param>
            <param name="data">The object to be post.</param>
            <remarks/>
        </member>
        <member name="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object)">
            <summary>
            Deprecated. Use <see cref="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)"/>
            </summary>
            <param name="message">The message id used to direct the event.</param>
            <param name="data">An object to send to scripts subscribed to message.</param>
            <remarks>Deprecated. Use <see cref="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)"/></remarks>
            <seealso cref="M:Sansar.Script.ScriptBase.PostScriptEvent(System.String,Sansar.Script.Reflective)"/>
        </member>
        <member name="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(Sansar.Script.ScriptId,System.String,System.Object)">
            <summary>
            Deprecated. Use <see cref="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)"/>
            </summary>
            <param name="targetScriptId">The id of the script to sent the event to. To broadcast the message to all subscripted scripts use <see cref="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object)"/>. </param>
            <param name="message">The message id used to direct the event.</param>
            <param name="data">An object to send to script targetScriptId if subscribed to message.</param>
            <remarks>Deprecated. Use <see cref="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)"/></remarks>
            <seealso cref="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)"/>
        </member>
        <member name="T:Sansar.Script.ScriptBase.ScopedLock">
            <summary>
            Returned from <see cref="M:Sansar.Script.ScriptBase.Lock(System.Object)"/> for use in using statements.
            </summary>
            <remarks>Do not save or store a ScopedLock outside of a using statement.</remarks>
            <seealso cref="M:Sansar.Script.ScriptBase.Lock(System.Object)"/>
        </member>
        <member name="M:Sansar.Script.ScriptBase.ScopedLock.Finalize">
            <summary>Internal use only</summary>
            <remarks>Finalizer will not release any locks still held. Use ScopedLock only in using statements.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptBase.ScopedLock.Dispose">
            <summary>
            Releases the lock being held.
            </summary>
        </member>
        <member name="M:Sansar.Script.ScriptBase.Lock(System.Object)">
            <summary>
            Use to create a critical section with a using statement, ensuring that no other coroutines or scripts (via Reflective) may enter this code section while one coroutine or script is in it.
            </summary>
            <param name="Token">The object controlling the lock. Recommended to be a private object member.</param>
            <returns>A ScopedLock that holds the lock and will release it in IDisposable.Dispose.</returns>
            <remarks>Use with <see langword="using"/> to create a critical section. If a lock is already held on Token by a different coroutine then this coroutine will wait for the lock to be released before entering the critical section.
            Lock is conceptually similar to the <see langword='lock'/> keyword.</remarks>
            <example><code lang="C#"><![CDATA[
            private object BalanceLock = new Object();
            int Balance = 100;
            bool Spend(int value)
            {
                using (Lock(BalanceLock))
                {
                    if (Balance > value)
                    {
                        Balance -= value;
                        return true;
                    }
                    return false;
                }
            }]]></code></example>
            <exception cref="T:System.ArgumentNullException">Thrown if Token is null.</exception>
            <seealso cref="M:Sansar.Script.ScriptBase.ReleaseLock(System.Object)"/>
            <seealso cref="M:Sansar.Script.ScriptBase.WaitForLock(System.Object)"/>
        </member>
        <member name="M:Sansar.Script.ScriptBase.ReleaseLock(System.Object)">
            <summary>
            Release a lock if held.
            </summary>
            <param name="Token">The object controlling the lock. Recommended to be a private object member.</param>
            <remarks>It is highly recommended to use <see cref="M:Sansar.Script.ScriptBase.Lock(System.Object)"/> instead. This is for advanced use only and can easily create deadlocks that stop scripts from working. Can not be used to release a lock not currently held by this coroutine.</remarks>
            <seealso cref="M:Sansar.Script.ScriptBase.Lock(System.Object)"/>
            <seealso cref="M:Sansar.Script.ScriptBase.WaitForLock(System.Object)"/>
            <exception cref="T:System.ArgumentNullException">Thrown if Token is null.</exception>
            <example><code lang="C#"><![CDATA[
            private object BalanceLock = new Object();
            int Balance = 100;
            bool Spend(int value)
            {
                WaitForLock(BalanceLock);
                bool success = false;
                if (Balance > value)
                {
                    Balance -= value;
                    success = true;
                }
                ReleaseLock(BalanceLock);
                // Note that any early exit between WaitForLock and ReleaseLock fail to release the lock, creating a deadlock the next time this method is called.
                // For this reason it is highly recommended to instead do: using (Lock(BalanceLock))
                return success;
            }]]></code></example>
        </member>
        <member name="M:Sansar.Script.ScriptBase.WaitForLock(System.Object)">
            <summary>
            WaitFor and take a lock on Token
            </summary>
            <param name="Token">The object controlling the lock. Recommended to be a private object member.</param>
            <remarks>It is highly recommended to use <see cref="M:Sansar.Script.ScriptBase.Lock(System.Object)"/> instead. This is for advanced use only and can easily create deadlocks that stop scripts from working. If not paired with <see cref="M:Sansar.Script.ScriptBase.ReleaseLock(System.Object)"/> will create a deadlock preventing this code from being entered.</remarks>
            <seealso cref="M:Sansar.Script.ScriptBase.Lock(System.Object)"/>
            <seealso cref="M:Sansar.Script.ScriptBase.ReleaseLock(System.Object)"/>
            <exception cref="T:System.ArgumentNullException">Thrown if Token is null.</exception>
            <example><code lang="C#"><![CDATA[
            private object BalanceLock = new Object();
            int Balance = 100;
            bool Spend(int value)
            {
                WaitForLock(BalanceLock);
                bool success = false;
                if (Balance > value)
                {
                    Balance -= value;
                    success = true;
                }
                ReleaseLock(BalanceLock);
                // Note that any early exit between WaitForLock and ReleaseLock fail to release the lock, creating a deadlock the next time this method is called.
                // For this reason it is highly recommended to instead do: using (Lock(BalanceLock))
                return success;
            }]]></code></example>
        </member>
        <member name="T:Sansar.Script.Memory">
            <summary>The Memory class reports on script pool memory use.</summary>
            <remarks>
            <para>Memory use is tracked by groups of scripts called "pools". There are two types of pools:</para>
            <example>
            <code lang="C#" src="examples/MemoryExample.cs"/>
            </example>
            <list type="bullet">
              <item><term>Scene: The scripts attached to the scene and to objects built into the scene all share a pool that is allowed a large amount of memory.</term></item>
              <item><term>Guests: Scripts associated with each visiting user share their own pool independent of other users. These pools are allowed a smaller amount of memory.</term></item>
            </list>
            <para>The Policy properties will reflect the use levels for the type of pool this script is associated with.</para>
            </remarks>
            <sourceLocation>D:\work\e5de6f3b3a3d2e0b\Sansar\Code\Common\Libraries\LLScript/Apis/CsScriptMemory.h(55)</sourceLocation>
        </member>
        <member name="T:Sansar.Script.Memory.SubscriptionHandler">
            <summary>Notifies scripts about memory events.</summary><sourceLocation>L:\p\Sansar\SharedBranches\ServerScript\Code\Common\Libraries\LLScript/Apis/ScriptMemory.h(75)</sourceLocation>
            <remarks>See <see cref="T:Sansar.Script.Memory"/>.</remarks>
            <param name='TrackingId'> <summary>The id of the animation component.</summary><sourceLocation>L:\p\Sansar\SharedBranches\ServerScript\Code\Common\Libraries\LLScript/Apis/ScriptMemory.h(75)</sourceLocation>
            <remarks>The id of the component generating the event.</remarks></param>
            <param name='UseLevel'> <summary>The behavior name of the Animation.</summary><sourceLocation>L:\p\Sansar\SharedBranches\ServerScript\Code\Common\Libraries\LLScript/Apis/ScriptMemory.h(75)</sourceLocation>
            <remarks>This name will match the subscribed event.</remarks></param>
        </member>
        <member name="M:Sansar.Script.Memory.Subscribe(Sansar.Script.Memory.SubscriptionHandler,System.Boolean)">
            <summary>Subscribes to Memory Events.</summary><sourceLocation>DistributeEvent::writeSubscribe line 198</sourceLocation><remarks/>
            <param name='callback'>Callback which is executed when the event completes.</param>
            <param name='persistent'>Optional, set to false to unsubscribe after one event.</param>
        </member>
        <member name="P:Sansar.Script.Memory.UsedBytes">
            <summary>Total bytes used by this script pool as of last accounting.</summary>
            <remarks>Memory is counted based on script activity in the scene across all pools. <see cref='P:Sansar.Script.Memory.ActivityLevel'/></remarks>
            <value>uint</value>
        </member>
        <member name="P:Sansar.Script.Memory.PeakUsedBytes">
            <summary>The highest level of memory used by the script pool.</summary>
            <remarks>The peak is reset when the server resets.</remarks>
            <value>uint</value>
        </member>
        <member name="P:Sansar.Script.Memory.PolicyWarning">
            <summary>Current policy Warning level of used memory by the script pool</summary>
            <remarks>When the memory used in the pool passes this mark events will be sent to scripts subscribed to ScriptMemoryEvents</remarks>
            <value>uint</value>
        </member>
        <member name="P:Sansar.Script.Memory.PolicyCritical">
            <summary>Current policy Critical level of used memory by the script pool</summary>
            <remarks>When the memory used in the pool passes this mark events will be sent to scripts subscribed to ScriptMemoryEvents and memory counting will become more frequent.</remarks>
            <value>uint</value>
        </member>
        <member name="P:Sansar.Script.Memory.PolicyLimit">
            <summary>Current policy Limit level of used memory by the script pool</summary>
            <remarks>When the memory used in the pool passes this mark events will be sent to scripts subscribed to ScriptMemoryEvents. Script pools that stay above this limit may be stopped or removed from the scene.</remarks>
            <value>uint</value>
        </member>
        <member name="P:Sansar.Script.Memory.ActivityLevel">
            <summary>Current memory activity level since start of last memory counting.</summary>
            <remarks>Returns a measure of the pools memory activity since the start of the last counting run.
            <para>Activity levels are 0-1: Low activity, 1-2: Medium activity, >2: High activity.</para>
            Activity level is used to determine when memory counting is required with more active memory use requiring more frequent memory counting.</remarks>
            <value>uint</value>
        </member>
        <member name="M:Sansar.Script.Memory.Subscribe(System.Action{Sansar.Script.MemoryData},System.Boolean)">
            <summary>Subscribes to Memory Events.</summary><sourceLocation>DistributeEvent::writeSubscribe line 180</sourceLocation><remarks /><returns>An <see cref='T:Sansar.Script.IEventSubscription' /> that can be used to cancel the subscription.</returns><seealso cref='T:Sansar.Script.MemoryData'/><seealso cref='T:Sansar.Script.IEventSubscription'/>
            <param name='callback'>Callback which is executed when the event completes.</param>
            <param name='persistent'>Optional, set to false to unsubscribe after one event.</param>
        </member>
        <member name="M:Sansar.Script.Memory.ToString">
            <summary> A string representation of this object. </summary> <returns>A string representation of this object.</returns> <remarks>The format of this string may change between releases.</remarks>
        </member>
        <member name="T:Sansar.Script.Log">
            <summary>The Log class handles script logging and error reporting</summary>
            <remarks>
            <para>A simple script for viewing the log messages might be:</para>
            <example>
            <code lang="C#" src="examples/LogExample.cs"/>
            </example>
            </remarks>
            <sourceLocation>D:\work\e5de6f3b3a3d2e0b\Sansar\Code\Common\Libraries\LLScript/Apis/CsLog.h(27)</sourceLocation>
        </member>
        <member name="F:Sansar.Script.Log.ConsoleHistory">
            <summary>
            The maximum number of messages to store on this console.
            </summary>
            <value>The maximum number of messages stored by the console.</value>
            <remarks/>
        </member>
        <member name="T:Sansar.Script.Log.Message">
            <summary>
            Represents a single console message.
            </summary>
            <remarks/>
        </member>
        <member name="P:Sansar.Script.Log.Message.Text">
            <summary>
            The text of the message.
            </summary>
            <remarks/>
            <value>The text of the message.</value>
        </member>
        <member name="P:Sansar.Script.Log.Message.Tag">
            <summary>
            Free form tag which can be used to filter messages.
            </summary>
            <remarks/>
            <value>The tag of the message.</value>
        </member>
        <member name="P:Sansar.Script.Log.Message.LogLevel">
            <summary>
            Message severity for filtering.
            </summary>
            <remarks/>
            <value>The <see cref="T:Sansar.Script.LogLevel"/> of the message.</value>
        </member>
        <member name="P:Sansar.Script.Log.Message.ScriptId">
            <summary>
            Source script id.
            </summary>
            <remarks/>
            <value>The <see cref="T:Sansar.Script.ScriptId"/> of the source of the message.</value>
        </member>
        <member name="P:Sansar.Script.Log.Message.TimeStamp">
            <summary>
            Time the message was written.
            </summary>
            <remarks/>
            <value>The timestamp of the message.</value>
        </member>
        <member name="M:Sansar.Script.Log.Message.ToString">
            <summary>
            String representation of the message.
            </summary>
            <remarks/>
            <returns>String representation of the message.</returns>
        </member>
        <member name="P:Sansar.Script.Log.Messages">
            <summary>
            All current log messages.
            </summary>
            <value>All currently stored log messages.</value>
            <remarks/>
        </member>
        <member name="M:Sansar.Script.Log.Clear">
            <summary>
            Clears all messages in this console.
            </summary>
            <remarks>Messages cannot be restored.</remarks>
        </member>
        <member name="M:Sansar.Script.Log.Clear(System.String)">
            <summary>
            Clears all messages in this console with the given tag.
            </summary>
            <remarks>Messages cannot be restored.</remarks>
            <param name="tag">The tag to clear from the log.</param>
        </member>
        <member name="M:Sansar.Script.Log.Clear(Sansar.Script.LogLevel)">
            <summary>
            Clears all messages in this console with the given logLevel.
            </summary>
            <remarks>Messages cannot be restored.</remarks>
            <param name="logLevel">The LogLevel to clear from the log. Multiple loglevels can be cleared in a single cal.</param>
        </member>
        <member name="M:Sansar.Script.Log.Clear(Sansar.Script.ScriptId)">
            <summary>
            Clears all messages in this console with the given ScriptId.
            </summary>
            <remarks>Messages cannot be restored.</remarks>
            <param name="scriptId">The ScriptId to clear from the log.</param>
        </member>
        <member name="M:Sansar.Script.Log.SetTraceOptions(System.String)">
            <summary>Allows for setting MONO_TRACE options at run time. May be buggy, but should only be used for debugging anyway</summary>
        </member>
        <member name="F:Sansar.Script.Log.MaximumTagLength">
            <summary>
            The maximum length of the log message tag.
            </summary>
        </member>
        <member name="F:Sansar.Script.Log.MaximumMessageLength">
            <summary>
            The maximum length of a log message.
            </summary>
        </member>
        <member name="M:Sansar.Script.Log.Write(System.String)">
            <summary>Writes an info message to the server log.</summary>
            <param name="message">The message to be logged.</param>
            <remarks>Log messages may be throttled.</remarks>
        </member>
        <member name="M:Sansar.Script.Log.Write(System.String,System.String)">
            <summary>Writes an info message to the server log.</summary> 
            <param name="message">The message to be logged.</param> 
            <param name="tag">A freeform string tag for filtering the message.</param> 
            <remarks>Log messages may be throttled.</remarks>
        </member>
        <member name="M:Sansar.Script.Log.Write(Sansar.Script.LogLevel,System.String)">
            <summary>Writes a message to the server log.</summary>
            <param name="logLevel">LogLevel of the message.</param>
            <param name="message">The message to be logged.</param> 
            <remarks>Log messages may be throttled.</remarks>
        </member>
        <member name="M:Sansar.Script.Log.Write(Sansar.Script.LogLevel,System.String,System.String)">
            <summary> Writes a message to the server log.</summary>
            <param name="logLevel">LogLevel of the message.</param>
            <param name="tag">Tag for the message.</param>
            <param name="message">Message to log.</param>
            <remarks>Also tracks the sending script and the time. No messages are locally retained.
            </remarks>
        </member>
        <member name="M:Sansar.Script.Log.WriteDebug(System.String,System.String,System.String,System.Int32)">
            <summary>
            Writes a debug message to the server log, including caller information.
            </summary>
            <param name="message">(Optional) The message to be logged. When left empty, it will only log caller information.</param>
            <param name="tag">(Optional) A tag for the message.</param>
            <param name="callerMemberName">Automatically populated with the name of the calling member (method or property).</param>
            <param name="callerLineNumber">Automatically populated with the line number in the source code where the method was called.</param>
            <remarks>
            Log messages may be throttled. WriteDebug(..) serves the same purpose as using Write(LogLevel.Debug, ..), while also including automatically populated caller information for debugging purposes.
            </remarks>
        </member>
        <member name="T:Sansar.Script.MemoryUseLevel">
            <summary>
            Used by <see cref="T:Sansar.Script.Memory"/> to report script memory use.
            </summary>
            <remarks></remarks>
            <sourceLocation>D:\work\e5de6f3b3a3d2e0b\Sansar\Code\Common\Libraries\LLScript/Apis/CsScriptMemory.h(25)</sourceLocation>
        </member>
        <member name="F:Sansar.Script.MemoryUseLevel.Low">
            <summary>Script memory use under Warning level.</summary>
        </member>
        <member name="F:Sansar.Script.MemoryUseLevel.Warning">
            <summary>First warning of high memory use.</summary>
        </member>
        <member name="F:Sansar.Script.MemoryUseLevel.Critical">
            <summary>Critical memory use, nearing limit.</summary>
        </member>
        <member name="F:Sansar.Script.MemoryUseLevel.Limit">
            <summary>Memory limit hit, if nothing is done scripts in this pool may be disabled.</summary>
        </member>
        <member name="T:Sansar.Script.LogLevel">
            <summary>
            Used by <see cref="T:Sansar.Script.Log"/> to filter messages.
            </summary>
            <remarks></remarks>
            <sourceLocation>Common/Libraries\LLScript/InstanceInterface.h(52)</sourceLocation>
        </member>
        <member name="F:Sansar.Script.LogLevel.Debug">
            <summary>Debugging messages.</summary>
        </member>
        <member name="F:Sansar.Script.LogLevel.Info">
            <summary>Basic informational messages.</summary>
        </member>
        <member name="F:Sansar.Script.LogLevel.Warning">
            <summary>Warning messages.</summary>
        </member>
        <member name="F:Sansar.Script.LogLevel.Error">
            <summary>Error messages.</summary>
        </member>
        <member name="T:Sansar.Script.MemoryData">
            <summary>Notifies scripts about memory events.</summary><sourceLocation>D:\work\e5de6f3b3a3d2e0b\Sansar\Code\Common\Libraries\LLScript/Apis/CsScriptMemory.h(82)</sourceLocation>
            <remarks>See <see cref="T:Sansar.Script.Memory"/>.</remarks>
        </member>
        <member name="P:Sansar.Script.MemoryData.TrackingId">
            <summary> <summary>Internal Id of the memory tracker.</summary><sourceLocation>D:\work\e5de6f3b3a3d2e0b\Sansar\Code\Common\Libraries\LLScript/Apis/CsScriptMemory.h(82)</sourceLocation>
            <remarks>Used for routing the event.</remarks></summary><value/><remarks/>
        </member>
        <member name="P:Sansar.Script.MemoryData.UseLevel">
            <summary> <summary>The level subscribed to.</summary><sourceLocation>D:\work\e5de6f3b3a3d2e0b\Sansar\Code\Common\Libraries\LLScript/Apis/CsScriptMemory.h(82)</sourceLocation>
            <remarks></remarks></summary><value/><remarks/>
        </member>
        <member name="M:Sansar.Script.MemoryData.ToString">
            <summary> A string representation of this object. </summary> <returns>A string representation of this object.</returns> <remarks>The format of this string may change between releases.</remarks>
        </member>
        <member name="T:Sansar.Script.Timer">
            <summary>
            The Timer class is a static class that manages timer events for a script. 
            </summary>
            <remarks>The time the event is generated is 
            guaranteed to be no less than the given duration, but may be more depending on system load.</remarks>
        </member>
        <member name="M:Sansar.Script.Timer.Create(System.TimeSpan,System.Action)">
            <summary>
            Generates a one time event.
            </summary>
            <param name="initialDuration">The minimum duration to elapse before the event is generated.</param>
            <param name="handler">The <see cref="T:System.Action"/> to be called when the time has elapsed.</param>
        </member>
        <member name="M:Sansar.Script.Timer.Create(System.TimeSpan,System.Action,System.Boolean)">
            <summary>
            Generates a one time event.
            </summary>
            <param name="initialDuration">The minimum duration to elapse before the event is generated.</param>
            <param name="handler">The <see cref="T:System.Action"/> to be called when the time has elapsed.</param>
            <param name="persistent">Set to true to continue generating events, false to stop after the first one</param>
        </member>
        <member name="M:Sansar.Script.Timer.Create(System.TimeSpan,System.TimeSpan,System.Action)">
            <summary>
            Generates a repeating event with an initial duration and a repeat duration.
            </summary>
            <param name="initialDuration">The minimum duration to elapse before the event is generated.</param>
            <param name="repeatDuration">After the initial duration, events will be generated at this interval.</param>
            <param name="handler">The <see cref="T:System.Action"/> to be called when the time has elapsed.</param>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Script.Timer.Create(System.Double,System.Action)">
            <summary>
            Generates a one time event.
            </summary>
            <param name="initialDurationSeconds">The minimum duration to elapse before the event is generated in seconds.</param>
            <param name="handler">The <see cref="T:System.Action"/> to be called when the time has elapsed.</param>
        </member>
        <member name="M:Sansar.Script.Timer.Create(System.Double,System.Action,System.Boolean)">
            <summary>
            Generates a one time event.
            </summary>
            <param name="initialDurationSeconds">The minimum duration to elapse before the event is generated in seconds.</param>
            <param name="handler">The <see cref="T:System.Action"/> to be called when the time has elapsed.</param>
            <param name="persistent">Set to true to continue generating events, false to stop after the first one</param>
        </member>
        <member name="M:Sansar.Script.Timer.Create(System.Double,System.Double,System.Action)">
            <summary>
            Generates a repeating event with an initial duration and a repeat duration.
            </summary>
            <param name="initialDurationSeconds">The minimum duration to elapse before the event is generated in seconds.</param>
            <param name="repeatDurationSeconds">After the initial duration, events will be generated at this interval in seconds.</param>
            <param name="handler">The <see cref="T:System.Action"/> to be called when the time has elapsed.</param>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Script.Timer.Create(System.TimeSpan,System.TimeSpan,System.Action,System.String)">
            <summary>
            Generates a repeating event with an initial duration and a repeat duration. The event will continue as long as the script is 
            active or until <see cref="M:Sansar.Script.Timer.Cancel(System.String)"/> is called.
            </summary>
            <param name="initialDuration">The minimum duration to elapse before the event is generated.</param>
            <param name="repeatDuration">After the initial duration, events will be generated at this interval.</param>
            <param name="handler">The <see cref="T:System.Action"/> to be called when the time has elapsed.</param>
            <param name="name">A short string to identify this Timer. </param>
            <remarks> At most, one event will be generated per server frame, even if the duration is significantly shorter than the server frame time.
            Only the first 8 characters of the name are significant. Multiple timers with 
            the same name are allowed. When <see cref="M:Sansar.Script.Timer.Cancel(System.String)"/> is called the first timer with a matching name is stopped.</remarks>
        </member>
        <member name="M:Sansar.Script.Timer.Cancel(System.String)">
            <summary>
            Cancels a repeating timer.
            </summary>
            <param name="name">Name of the timer to cancel.</param>
            <remarks>If no timer with the given name exists, a <see cref="T:System.ArgumentException"/> will be thrown by the invocation.</remarks>
        </member>
        <member name="T:Sansar.Script.CancelData">
            <summary>
            The event is sent when a subscription is canceled.
            </summary>
            <remarks>Coroutines in a WaitFor will capture this event and throw <see cref="T:WaitCanceledException"/>. </remarks>
        </member>
        <member name="P:Sansar.Script.CancelData.Message">
            <summary>
            String description of the error condition.
            </summary>
            <remarks>Useful for debugging.</remarks><value>The error message string.</value>
        </member>
        <member name="P:Sansar.Script.CancelData.Reason">
            <summary>
            Integer error code. The value varies depending on the method called.
            </summary>
            <remarks>See the documentation for the Subscribe method for details.</remarks><value>Error message int reason.</value>
        </member>
        <member name="T:Sansar.Script.NonReflectiveAttribute">
            <summary>
            Prevents methods from being reflected using <see cref="M:Sansar.Script.Reflective.AsInterface``1"/>.
            </summary>
            <remarks>This can be used at the class level to block all methods or on individual methods or properties methods</remarks>
        </member>
        <member name="M:Sansar.Script.NonReflectiveAttribute.#ctor">
            <summary>Prevents methods from being reflected using <see cref="M:Sansar.Script.Reflective.AsInterface``1"/>.</summary>
            <remarks/>
        </member>
        <member name="T:Sansar.Script.RegisterReflectiveAttribute">
            <summary>
            Registers this class to be found via <see cref="M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String)"/>.
            </summary>
            <remarks>Every instance of a reflective object with this attribute will be automatically registered with the scene to be found by <see cref="M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String)"/>. Specific instances of a Reflective class can be registered manually with <see cref="M:Sansar.Script.Reflective.Register"/>. For script objects themselves it is recommended to use this attribute to minimize race conditions between Register calls and FindReflective calls.</remarks>
        </member>
        <member name="M:Sansar.Script.RegisterReflectiveAttribute.#ctor">
            <summary>Automatically registers all instances of this class to be found with <see cref="M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String)"/> .</summary>
            <remarks>Every instance of a reflective object with this attribute will be automatically registered with the scene to be found by <see cref="M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String)"/>. Specific instances of a Reflective class can be registered manually with <see cref="M:Sansar.Script.Reflective.Register"/>. For script objects themselves it is recommended to use this attribute to minimize race conditions between Register calls and FindReflective calls.</remarks>
        </member>
        <member name="T:Sansar.Script.ErrorModeAttribute">
            <summary>
            Set the Api Error mode for this script.
            </summary>
            <remarks>Set to ApiErrorMode.Default, ApiErrorMode.Relaxed, or ApiErrorMode.Strict</remarks>
            <seealso cref="T:Sansar.Microthreading.ApiErrorMode"/>
        </member>
        <member name="M:Sansar.Script.ErrorModeAttribute.#ctor(Sansar.Microthreading.ApiErrorMode)">
            <summary>
            Set the Api Error mode for this script.
            </summary>
            <remarks>Set to ApiErrorMode.Default, ApiErrorMode.Relaxed, or ApiErrorMode.Strict</remarks>
            <seealso cref="T:Sansar.Microthreading.ApiErrorMode"/>
        </member>
        <member name="T:Sansar.Script.EditorVisibleAttribute">
            <summary>
            Explicitly set the editor visibility of a script field or class.
            </summary>
            <remarks>By default only public fields are exposed in the editor. 
            Using this attribute, protected, private or internal fields may be exposed as well.
            Classes can be hidden setting this attribute to false. </remarks>
        </member>
        <member name="M:Sansar.Script.EditorVisibleAttribute.#ctor">
            <summary>
            Mark a non-public script field as visible in the editor.
            </summary><remarks></remarks>
        </member>
        <member name="M:Sansar.Script.EditorVisibleAttribute.#ctor(System.Boolean)">
            <summary>
            Explicitly set the visibility of a field or class
            </summary>
            <param name="visible"></param>
        </member>
        <member name="P:Sansar.Script.EditorVisibleAttribute.Visible">
            <summary>
            The visibility value.
            </summary><remarks></remarks>
        </member>
        <member name="T:Sansar.Script.DefaultValueAttribute">
             <summary>
             Set a default value to show in the script properties field.
             </summary>
             <remarks>Unless specified by this attribute all fields shown in script properties are initialized to the default value for their type (usually 0 or empty string equivalents.). 
             Note that Vector and Quaternions require an appropriately formatted string to work correctly.  <![CDATA[Vectors start with '<', have 3 or 4 numerical elements seperated by commas, and end with '>'.]]> Quaternions start with '[', have 4 numerical elements seperated by commas, and end with ']'</remarks>
             <example><code lang="C#">
             <![CDATA[
             [DefaultValue("Default Event")]
             public string MyString = null;
            
             [DefaultValue(12.34)]
             public float MyFloat = 1;
            
             [DefaultValue("<1.2,3.4,5.6>")]
             public Sansar.Vector MyVector;
            
             [DefaultValue("[1.2,3.4,5.6,7.8]")]
             public Sansar.Quaternion MyQuaternion;
             
             [DefaultValue("(1.2,3.4,5.6,7.8)")]
             public Sansar.Color MyColor;
            
             [DefaultValue(true)]
             public bool MyBool;
            
             [DefaultValue(2)]
             [Range(-20, 5)]
             public int myInt;]]></code></example>
        </member>
        <member name="M:Sansar.Script.DefaultValueAttribute.#ctor(System.String)">
             <summary>
             Set a default string value to show in the script properties for this field. Without the DefaultValue attribute strings default to an empty string.
             </summary>
             <remarks>The string DefaultValue attribute can be used to set Vector and Quaternions with an appropriately formatted string.  <![CDATA[Vectors start with '<', have 3 or 4 numerical elements seperated by commas, and end with '>'.]]> Quaternions start with '[', have 4 numerical elements seperated by commas, and end with ']'</remarks>
             <example><code lang="C#">
             <![CDATA[
             [DefaultValue("Default Event")]
             public string MyString = null;
            
             [DefaultValue("<1.2,3.4,5.6>")]
             public Sansar.Vector MyVector;
            
             [DefaultValue("[1.2,3.4,5.6,7.8]")]
             public Sansar.Quaternion MyQuaternion;
             
             [DefaultValue("(1.2,3.4,5.6,7.8)")]
             public Sansar.Quaternion MyQuaternion;
            ]]></code></example>
             <param name="value">The default value in string form for strings, quaternions and vector fields.</param>
        </member>
        <member name="M:Sansar.Script.DefaultValueAttribute.#ctor(System.Boolean)">
            <summary>
            Set a default bool value to show in the script properties for this field. Without the DefaultValue attribute bools default to 'false'.
            </summary>
            <param name="value">The default boolvalue to show in script properties.</param>
            <example><code lang="C#">
            [DefaultValue(true)]
            public bool MyBool;
            </code></example>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Script.DefaultValueAttribute.#ctor(System.Byte)">
            <summary>
            Set a default byte value to show in the script properties for this field.  Without the DefaultValue attribute bytes default to 0.
            </summary>
            <param name="value">The default byte value to show in script properties.</param>
            <remarks></remarks>
            <example><code lang="C#">
            [DefaultValue(0x00C9)]
            public byte MyField;
            </code></example>
        </member>
        <member name="M:Sansar.Script.DefaultValueAttribute.#ctor(System.Int16)">
            <summary>
            Set a default short value to show in the script properties for this field.  Without the DefaultValue attribute shorts default to 0.
            </summary>
            <param name="value">The default short value to show in script properties.</param>
            <remarks></remarks>
            <example><code lang="C#">
            [DefaultValue(123)]
            public short MyField;
            </code></example>
        </member>
        <member name="M:Sansar.Script.DefaultValueAttribute.#ctor(System.Int32)">
            <summary>
            Set a default int value to show in the script properties for this field.  Without the DefaultValue attribute ints default to 0.
            </summary>
            <param name="value">The default int value to show in script properties.</param>
            <remarks></remarks>
            <example><code lang="C#">
            [DefaultValue(123)]
            public int MyField;
            </code></example>
        </member>
        <member name="M:Sansar.Script.DefaultValueAttribute.#ctor(System.Int64)">
            <summary>
            Set a default long value to show in the script properties for this field.  Without the DefaultValue attribute longs default to 0.
            </summary>
            <param name="value">The default long value to show in script properties.</param>
            <remarks></remarks>
            <example><code lang="C#">
            [DefaultValue(123)]
            public long MyField;
            </code></example>
        </member>
        <member name="M:Sansar.Script.DefaultValueAttribute.#ctor(System.Single)">
            <summary>
            Set a default float value to show in the script properties for this field.  Without the DefaultValue attribute floats default to 0.0.
            </summary>
            <param name="value">The default float value to show in script properties.</param>
            <remarks>Script properties only support up to 32bit int, this constructor is provided for convenience and values will be converted to 32bit int.</remarks>
            <example><code lang="C#">
            [DefaultValue(12.34)]
            public float MyField;
            </code></example>
        </member>
        <member name="M:Sansar.Script.DefaultValueAttribute.#ctor(System.Double)">
            <summary>
            Set a default double value to show in the script properties for this field.  Without the DefaultValue attribute doubles default to 0.0.
            </summary>
            <param name="value">The default double value to show in script properties.</param>
            <remarks>Script properties only support floats, this constructor is provided for convenience and values will be converted to floats.</remarks>
            <example><code lang="C#">
            [DefaultValue(12.34)]
            public double MyField;
            </code></example>
        </member>
        <member name="M:Sansar.Script.DefaultValueAttribute.#ctor(System.Double[])">
            <summary>
            Set a default double value to show in the script properties for this field.  Without the DefaultValue attribute doubles default to 0.0.
            </summary>
            <param name="value">The default double value to show in script properties.</param>
            <remarks>Script properties only support floats, this constructor is provided for convenience and values will be converted to floats.</remarks>
            <example><code lang="C#">
            [DefaultValue(1.3, 2.4, 3.5)]
            public Vector MyField;
            </code></example>
        </member>
        <member name="T:Sansar.Script.RangeAttribute">
            <summary>
            Specify a range of valid values for the field in script properties.
            </summary>
            <remarks>Fields with a specified range will show in script properties with both a slider and an editable field. Values will be clamped to be within the range.</remarks>
        </member>
        <member name="M:Sansar.Script.RangeAttribute.#ctor">
            <summary>
            Specify the allowed range of values for a field using named arguments.
            </summary>
            <example><code lang="C#">
            [Range(Min=-10.0, Max=10.0)]
            public float namedRangeFloat;
            </code></example>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Script.RangeAttribute.#ctor(System.Double,System.Double)">
            <summary>
            Specify the allowed range of values for a float or double field.
            </summary>
            <param name="min">The lower bound on the range. Default: 0.0f</param>
            <param name="max">The upper bound on the range. Default: 0.0f</param>
            <example><code lang="C#">
            [Range(-10.0, 10.0)]
            public float namedRangeFloat;
            </code></example>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Script.RangeAttribute.#ctor(System.Int32,System.Int32)">
            <summary>
            Specify the allowed range of values for an int field.
            </summary>
            <param name="min">The lower bound on the range. Default: 0</param>
            <param name="max">The upper bound on the range. Default: 0</param>
            <example><code lang="C#">
            [Range(-10, 10)]
            public int rangeInt;
            </code></example>
            <remarks></remarks>
        </member>
        <member name="P:Sansar.Script.RangeAttribute.Min">
            <summary>
            Used with default constructor to specify the lower bound on the range.
            </summary>
            <example><code lang="C#">
            [Range(Min=-10.0, Max=10.0)]
            public float rangeFloat;
            </code></example>
            <remarks></remarks>
            <value>The minimum allowed value for the parameter.</value>
        </member>
        <member name="P:Sansar.Script.RangeAttribute.Max">
            <summary>
            Used with default constructor to specify the upper bound on the range.
            </summary>
            <example><code lang="C#">
            [Range(Min=-10.0, Max=10.0)]
            public float rangeFloat;
            </code></example>
            <remarks></remarks>
            <value>The maximum allowed value for the parameter.</value>
        </member>
        <member name="T:Sansar.Script.DisplayNameAttribute">
            <summary>
            Set the name to be shown in script properties for this field.
            </summary>
            <remarks>By default the variable name is used, replacing underscores ('_') with spaces.</remarks>
        </member>
        <member name="M:Sansar.Script.DisplayNameAttribute.#ctor(System.String)">
            <summary>
            Set the name to be shown in script properties for this field.
            </summary>
            <remarks>By default the variable name is used, replacing underscores ('_') with spaces.</remarks>
            <param name="name"></param>
            <example><code lang="C#">
            [DisplayName("")]
            public float namedFloat;
            </code></example>
        </member>
        <member name="T:Sansar.Script.MinEntriesAttribute">
            <summary>
            Set the Minimum number of array values allowed.
            </summary>
            <remarks>Defaults to 0, allowing the array to be empty.</remarks>
        </member>
        <member name="M:Sansar.Script.MinEntriesAttribute.#ctor(System.Int32)">
            <summary>
            Set the Minimum number of array values allowed.
            </summary>
            <remarks>Defaults to 0, allowing the array to be empty.</remarks>
            <param name="minCount"></param>
            <example><code lang="C#">
            [MinValues(1)]
            public IList&lt;float>floatList;
            </code></example>
        </member>
        <member name="T:Sansar.Script.MaxEntriesAttribute">
            <summary>
            Set the Maximum number of array values allowed.
            </summary>
            <remarks>Defaults to 20, which is the system maximum.</remarks>
        </member>
        <member name="M:Sansar.Script.MaxEntriesAttribute.#ctor(System.Int32)">
            <summary>
            Set the Maximum number of IList or IDictionary allowed.
            </summary>
            <remarks>Defaults to 20, which is the system maximum.</remarks>
            <param name="maxCount"></param>
            <example><code lang="C#">
            [MaxValues(1)]
            public IList&lt;float>floatList;
            </code></example>
        </member>
        <member name="T:Sansar.Script.AddEntryAttribute">
            <summary>
            A
            </summary>
            <seealso cref="T:Sansar.Script.EntriesAttribute"/>
        </member>
        <member name="M:Sansar.Script.AddEntryAttribute.#ctor(System.String,System.Object)">
            <summary>
            Add an entry to the initial values in the parameter for IList and IDictionary
            </summary>
            <remarks>For IDictionary sets both a key and a default value for that key.
            <br/>For IList the value is ignored.
            <br/>Has no effect for other parameter types.
            <br/>Can be used multiple times to add multiple entries for IDictionary</remarks>
            <param name="key">IDictionary: the key of the entry. IList: the default value.</param>
            <param name="value">IDictionary: the default value of the entry. IList: ignored</param>
            <seealso cref="T:Sansar.Script.EntriesAttribute"/>
        </member>
        <member name="M:Sansar.Script.AddEntryAttribute.#ctor(System.Object)">
            <summary>
            Add an entry to the initial values in the parameter for IList and IDictionary
            </summary>
            <remarks>For IDictionary sets the key only, with no default value.
            <br/>For IList sets the default value of the entry.
            <br/>Has no effect for other parameter types.</remarks>
            <param name="entry">IDictionary: the key of the entry. IList: the default value of the entry.</param>
            /// <seealso cref="T:Sansar.Script.EntriesAttribute"/>
        </member>
        <member name="T:Sansar.Script.EntriesAttribute">
            <summary>
            List of entries to populate IList or IDictionary properties with.
            </summary>
            <remarks>When used with IDictionary the entries are the keys with no default values.</remarks>
        </member>
        <member name="M:Sansar.Script.EntriesAttribute.#ctor(System.Object[])">
            <summary>
            List of entries to populate IList or IDictionary properties with.
            </summary>
            <remarks>When used with IDictionary the entries are the keys with no default values.
            <br/><br/>When used with IList the entries are the default values.
            <br/>To set an IList of a specific Count with no default values use <see cref="T:Sansar.Script.MinEntriesAttribute"/>
            <br/>Has no effect on other parameter types.
            <br/>For <see cref="T:Sansar.Vector"/>, <see cref="T:Sansar.Quaternion"/>, and <see cref="T:Sansar.Color"/>s in IList their default values must be specified in string form.</remarks>
            <param name="entries"></param>
            <example><code lang="C#">
            [Entries("King", "Queen", "Rook", "Bishop", "Knight", "Pawn")]
            public IDictionary&lt;string, ClusterResource> GamePieces;
            
            [Entries("&lt;1,2,3>", "&lt;2.5,3.6,4.6>")]
            public IList&lt;Sansar.Vector> GamePieces;
            </code></example>
        </member>
        <member name="F:Sansar.Script.EntriesAttribute.Locked">
            <summary>
            Locks the IDictionary or IList properties so entries can not be added or removed.
            </summary>
        </member>
        <member name="T:Sansar.Script.LockedAttribute">
            <summary>
            Locks the IDictionary or IList properties so entries can not be added or removed.
            </summary>
            <remarks>Should be used in conjunction with <see cref="T:Sansar.Script.EntriesAttribute"/> or <see cref="T:Sansar.Script.AddEntryAttribute"/>.</remarks>
        </member>
        <member name="M:Sansar.Script.LockedAttribute.#ctor">
            <summary>
            Locks the IDictionary or IList properties so entries can not be added or removed.
            </summary>
            <remarks>Should be used in conjunction with <see cref="T:Sansar.Script.EntriesAttribute" /> or <see cref="T:Sansar.Script.AddEntryAttribute" />.
            <br/>IDictionary keys will be unchangeable and entries can not be added or removed.
            <br/>IList entries can not be added or removed.
            <br/>Will not prevent values from being modified in either case.
            <br/>Has no effect on other parameter types.</remarks>
            <example><code lang="C#">
            [Locked]
            public IList&lt;float>floatList;
            </code></example>
        </member>
        <member name="T:Sansar.Script.TooltipAttribute">
            <summary>
            Set a helpful tooltip message to show in the editor.
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Script.TooltipAttribute.#ctor(System.String)">
            <summary>
            Set a helpful tooltip message to show in the editor.
            </summary>
            <remarks></remarks>
            <example><code lang="C#">
            [Tooltip("The position to move the object to, in world space.")]
            public Vector Destination;
            </code></example>
        </member>
        <member name="P:Sansar.Script.TooltipAttribute.Value">
            <summary>
            The string to display
            </summary>
        </member>
        <member name="T:Sansar.Script.DefaultScriptAttribute">
            <summary>
            Tags a class to be the default selection for this script project.
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Script.ProfileScope.Dispose">
            <summary>
            
            </summary>
        </member>
        <member name="T:Sansar.Script.CoroutineException">
            <summary>
            An exception occurred relating to Coroutine execution.
            </summary>
            <remarks>This exception is thrown when a coroutine method is called 
            outside of a coroutine context (i.e. in a method that was not started 
            with <see cref="M:Sansar.Script.ScriptBase.StartCoroutine(System.Action)"/>) or 
            attempting to use the context of another script.</remarks>
        </member>
        <member name="T:Sansar.Script.ICoroutine">
            <summary>
            Token representing a coroutine.
            </summary>
            <remarks>Manages an existing coroutine.</remarks>
        </member>
        <member name="M:Sansar.Script.ICoroutine.Abort">
            <summary>
            Stop the coroutine
            </summary>
            <remarks>The coroutine will be deleted and will not run again.</remarks>
        </member>
        <member name="P:Sansar.Script.ICoroutine.IsAlive">
            <summary>
            Is this coroutine is still working or waiting.
            </summary>
            <value>Will return false if this coroutine has completed or had Abort() called on it or thrown an exception.</value>
            <remarks/>
        </member>
        <member name="P:Sansar.Script.ICoroutine.IsWaiting">
            <summary>
            Is the coroutine waiting.
            </summary>
            <value>True if the coroutine is waiting for time or an event.</value>
            <remarks/>
        </member>
        <member name="M:Sansar.Script.ICoroutine.Signal">
            <summary>
            Send a signal to the coroutine and wake it up if it is waiting for a signal.
            </summary>
            <remarks/>
        </member>
        <member name="M:Sansar.Script.ICoroutine.ResetSignals">
            <summary>
            Reset the signal count on a coroutine. 
            </summary>
            <remarks>Will not re-sleep a coroutine that was waiting on signals.</remarks>
        </member>
        <member name="P:Sansar.Script.ICoroutine.Name">
            <summary>The name of the coroutine.</summary>
            <remarks>By default the function name, but can be overwritten by anyone with the ICoroutine interface handle.</remarks>
        </member>
        <member name="T:Sansar.Script.CoroutineHandler">
            <summary>
            
            </summary>
        </member>
        <member name="P:Sansar.Script.CoroutineHandler.IsAlive">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Sansar.Script.CoroutineHandler.IsWaiting">
            <summary>
            
            </summary>
        </member>
        <member name="M:Sansar.Script.CoroutineHandler.Abort">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Sansar.Script.CoroutineHandler.Signal">
            <summary>
            
            </summary>
        </member>
        <member name="P:Sansar.Script.CoroutineHandler.SignalCount">
            <summary>
            
            </summary>
        </member>
        <member name="M:Sansar.Script.CoroutineHandler.ResetSignals">
            <summary>
            
            </summary>
        </member>
        <member name="P:Sansar.Script.CoroutineHandler.OnComplete">
            <summary>
            
            </summary>
        </member>
        <member name="P:Sansar.Script.CoroutineHandler.Name">
            <summary>
            
            </summary>
        </member>
        <member name="T:Sansar.Script.Coroutine">
            <summary>
            Encapsulates the management of a coroutine.
            </summary>
        </member>
        <member name="M:Sansar.Script.Coroutine.Unblock(Sansar.Script.EventData)">
            <summary>
            Generic handler for any event type.
            </summary>
        </member>
        <member name="T:Sansar.Script.InstanceInterface">
            <summary>
            Base class for all Sansar Script Object Oriented C# interfaces.
            </summary>
            <remarks>Internal.</remarks>
        </member>
        <member name="M:Sansar.Script.InstanceInterface.op_Implicit(Sansar.Script.InstanceInterface)~System.IntPtr">
            <summary>
            Internal use only.
            </summary>
            <param name="instance"> </param>
            <returns> </returns>
            <remarks> </remarks>
        </member>
        <member name="P:Sansar.Script.InstanceInterface.IsValid">
            <summary>
            Whether or not this interface is valid.
            </summary>
            <value>True if this interface is valid</value>
            <remarks>This function is provided for debugging and convenience only. Scripts are frequently "force yielded", which means there is still the (rare) possibility that on the line after checking IsValid the interface could become invalid. It is highly recommended to "program defensively" and wrap the use of volatile interfaces, like AgentPrivate, in try/catch blocks. Once an interface becomes invalid it will never again become valid. Attempting to call any method on an invalid interface may throw an exception.</remarks>
        </member>
        <member name="T:Sansar.Script.IEventSubscription">
            <summary>
            Token representing an event subscription.
            </summary>
            <remarks>Manages existing events.</remarks>
        </member>
        <member name="M:Sansar.Script.IEventSubscription.Unsubscribe">
            <summary>
            Removes this subscription.
            </summary>
            <remarks>Events which have already been queued but not sent may be generated after this call.</remarks>
        </member>
        <member name="P:Sansar.Script.IEventSubscription.Active">
            <summary>
            Is this subscription is still listening for events.
            </summary>
            <value>Will return false if this subscription has been unsubscribed or is not persistent and an event has been generated.</value>
            <remarks></remarks>
        </member>
        <member name="P:Sansar.Script.EventCallback.Active">
            <summary>
            
            </summary>
        </member>
        <member name="T:Sansar.Script.SessionId">
            <summary>
            Encapsulates a Session Id.
            </summary>
            <remarks>
            Session ids are unique per agent session
            </remarks>
        </member>
        <member name="M:Sansar.Script.SessionId.op_Implicit(System.Int32)~Sansar.Script.SessionId">
            <summary>
            Internal explicit conversion from a int.
            </summary>
            <param name="id">A int representation of an SessionId</param>
            <remarks>Internal.</remarks>
            <returns>A new SessionId initialized with the given int.</returns>
        </member>
        <member name="M:Sansar.Script.SessionId.Equals(System.Object)">
            <summary>
            Value comparison for SessionId.
            </summary>
            <param name="obj">The session to compare.</param>
            <returns>true if the argument is an SessionId and has the same value.</returns>
            <remarks>This method overrides <see cref="M:System.ValueType.Equals"/>.</remarks>
        </member>
        <member name="M:Sansar.Script.SessionId.GetHashCode">
            <summary>
            Retrieves the hash code for this instance.
            </summary>
            <returns>The hash code for this instance.</returns>
            <remarks>This method overrides <see cref="M:System.ValueType.GetHashCode"/>.</remarks>
        </member>
        <member name="M:Sansar.Script.SessionId.op_Equality(Sansar.Script.SessionId,Sansar.Script.SessionId)">
            <summary>
            SessionId equality operator.
            </summary>
            <param name="a">First SessionId to compare.</param>
            <param name="b">Second SessionId to compare.</param>
            <returns>true if the SessionIds have the same value.</returns>
            <remarks>This is a value comparison.</remarks>
        </member>
        <member name="M:Sansar.Script.SessionId.op_Inequality(Sansar.Script.SessionId,Sansar.Script.SessionId)">
            <summary>
            SessionId inequality operator.
            </summary>
            <param name="a">First SessionId to compare.</param>
            <param name="b">Second SessionId to compare.</param>
            <returns>true if the SessionIds have a different value.</returns>
            <remarks>This is a value comparison.</remarks>
        </member>
        <member name="M:Sansar.Script.SessionId.ToString">
            <summary>
            Converts the id to a hexadecimal string representation. 
            </summary>
            <returns>The hexadecimal string representation.</returns>
            <remarks>This method overrides <see cref="M:System.Session.ToString"/>.</remarks>
        </member>
        <member name="F:Sansar.Script.SessionId.Invalid">
            <summary>
            The invalid id session.
            </summary>
            <remarks>This value is used by certain APIs to represent an invalid SessionId.</remarks>
        </member>
        <member name="T:Sansar.Script.ScriptId">
            <summary>
            Encapsulates an Script Id.
            </summary>
            <remarks>
            Script Ids are unique per running scene.
            </remarks>
        </member>
        <member name="M:Sansar.Script.ScriptId.Equals(System.Object)">
            <summary>
            Value comparison for ScriptId.
            </summary>
            <param name="obj">The script to compare.</param>
            <returns>true if the argument is an ScriptId and has the same value.</returns>
            <remarks>This method overrides <see cref="M:System.ValueType.Equals"/>.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptId.GetHashCode">
            <summary>
            Retrieves the hash code for this instance.
            </summary>
            <returns>The hash code for this instance.</returns>
            <remarks>This method overrides <see cref="M:System.ValueType.GetHashCode"/>.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptId.op_Equality(Sansar.Script.ScriptId,Sansar.Script.ScriptId)">
            <summary>
            ScriptId equality operator.
            </summary>
            <param name="a">First ScriptId to compare.</param>
            <param name="b">Second ScriptId to compare.</param>
            <returns>true if the ScriptIds have the same value.</returns>
            <remarks>This is a value comparison.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptId.op_Inequality(Sansar.Script.ScriptId,Sansar.Script.ScriptId)">
            <summary>
            ScriptId inequality operator.
            </summary>
            <param name="a">First ScriptId to compare.</param>
            <param name="b">Second ScriptId to compare.</param>
            <returns>true if the ScriptIds have a different value.</returns>
            <remarks>This is a value comparison.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptId.ToString">
            <summary>
            Converts the id to a hexadecimal string representation. 
            </summary>
            <returns>The hexadecimal string representation.</returns>
            <remarks>This method overrides <see cref="M:System.Script.ToString"/>.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptId.CompareTo(Sansar.Script.ScriptId)">
            <summary>
            Compares another instance to this scriptId.
            </summary>
            <param name="other">The scriptId to compare with this instance.</param>
            <returns>Returns -1 if this scriptId comes before the specified instance, 1 if this script id comes after
            the specified instance, 0 otherwise.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Script.ScriptId.CompareTo(System.Object)">
            <summary>
            Compares another instance to this scriptId.
            </summary>
            <param name="other">The scriptId to compare with this instance.</param>
            <returns>Returns -1 if this scriptId comes before the specified instance, 1 if this script id comes after
            the specified instance, 0 otherwise. Returns 2 if the object is not a scriptId.</returns>
            <remarks/>
        </member>
        <member name="F:Sansar.Script.ScriptId.Invalid">
            <summary>
            The invalid id script.
            </summary>
            <remarks>This value is used by certain APIs to represent an invalid ScriptId.</remarks>
        </member>
        <member name="P:Sansar.Script.ScriptId.IsValid">
            <summary>
            Gets the valid state for this ScriptId.
            </summary>
            <value>
            True if this id is a valid ScriptId.
            </value>
            <remarks></remarks>
        </member>
        <member name="F:Sansar.Script.ScriptId.AllScripts">
            <summary>
            The invalid id script.
            </summary>
            <remarks>This value is used by certain APIs to represent all scripts.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptId.op_Implicit(Sansar.Script.ScriptId)~System.UInt32">
            <summary>
            Convert a ScriptId to a uint.
            </summary>
            <param name="id">The Script id to convert.</param>
            <remarks></remarks>
            <returns/>
        </member>
        <member name="T:Sansar.Script.ObjectId">
            <summary>
            Encapsulates an Object Id.
            </summary>
            <remarks>
            An ObjectId can be derived from a <see cref="T:Sansar.Script.ComponentId"/>.
            </remarks>
        </member>
        <member name="M:Sansar.Script.ObjectId.op_Implicit(System.UInt32)~Sansar.Script.ObjectId">
            <summary>
            Internal explicit conversion from a uint.
            </summary>
            <param name="id">A uint representation of an ObjectId</param>
            <remarks>Internal.</remarks>
            <returns>A new ObjectId initialized with the given uint.</returns>
        </member>
        <member name="M:Sansar.Script.ObjectId.Equals(System.Object)">
            <summary>
            Value comparison for ObjectId.
            </summary>
            <param name="obj">The object to compare.</param>
            <returns>true if the argument is an ObjectId and has the same value.</returns>
            <remarks>This method overrides <see cref="M:System.ValueType.Equals"/>.</remarks>
        </member>
        <member name="M:Sansar.Script.ObjectId.GetHashCode">
            <summary>
            Retrieves the hash code for this instance.
            </summary>
            <returns>The hash code for this instance.</returns>
            <remarks>This method overrides <see cref="M:System.ValueType.GetHashCode"/>.</remarks>
        </member>
        <member name="M:Sansar.Script.ObjectId.op_Equality(Sansar.Script.ObjectId,Sansar.Script.ObjectId)">
            <summary>
            ObjectId equality operator.
            </summary>
            <param name="a">First ObjectId to compare.</param>
            <param name="b">Second ObjectId to compare.</param>
            <returns>true if the ObjectIds have the same value.</returns>
            <remarks>This is a value comparison.</remarks>
        </member>
        <member name="M:Sansar.Script.ObjectId.op_Inequality(Sansar.Script.ObjectId,Sansar.Script.ObjectId)">
            <summary>
            ObjectId inequality operator.
            </summary>
            <param name="a">First ObjectId to compare.</param>
            <param name="b">Second ObjectId to compare.</param>
            <returns>true if the ObjectIds have a different value.</returns>
            <remarks>This is a value comparison.</remarks>
        </member>
        <member name="M:Sansar.Script.ObjectId.ToString">
            <summary>
            Converts the id to a hexadecimal string representation. 
            </summary>
            <returns>The hexadecimal string representation.</returns>
            <remarks>This method overrides <see cref="M:System.Object.ToString"/>.</remarks>
        </member>
        <member name="F:Sansar.Script.ObjectId.Invalid">
            <summary>
            The invalid id object.
            </summary>
            <remarks>This value is used by certain APIs to represent an invalid ObjectId.</remarks>
        </member>
        <member name="T:Sansar.Script.ComponentId">
            <summary>
            Encapsulates a Component Id.
            </summary>
            <remarks>
            The ComponentId is a unique identifier for a component. An <see cref="T:Sansar.Script.ObjectId"/> can be derived from a ComponentId.
            </remarks>
        </member>
        <member name="M:Sansar.Script.ComponentId.op_Implicit(System.UInt64)~Sansar.Script.ComponentId">
            <summary>
            Internal explicit conversion from a ulong.
            </summary>
            <param name="id">A ulong representation of an ComponentId</param>
            <remarks>Internal.</remarks>
            <returns>A new ComponentId initialized with the given ulong.</returns>
        </member>
        <member name="P:Sansar.Script.ComponentId.ObjectId">
            <summary>
            Retrieves the <see cref="T:Sansar.Script.ObjectId"/> associated with this ComponentId.
            </summary>
            <value>The is exactly 1 ObjectId associated with each ComponentId.</value>
            <remarks>The ObjectId returned will be valid if the ComponentId is valid. An <see cref="F:Sansar.Script.ComponentId.Invalid"/> ComponentId will return <see cref="F:Sansar.Script.ObjectId.Invalid"/>.</remarks>
        </member>
        <member name="M:Sansar.Script.ComponentId.Equals(System.Object)">
            <summary>
            Value comparison for ComponentId.
            </summary>
            <param name="obj">The object to compare.</param>
            <returns>true if the argument is a ComponentId and has the same value.</returns>
            <remarks>This method overrides <see cref="M:System.ValueType.Equals"/>.</remarks>
        </member>
        <member name="M:Sansar.Script.ComponentId.GetHashCode">
            <summary>
            Retrieves the hash code for this instance.
            </summary>
            <returns>The hash code for this instance.</returns>
            <remarks>This method overrides <see cref="M:System.ValueType.GetHashCode"/>.</remarks>
        </member>
        <member name="M:Sansar.Script.ComponentId.op_Equality(Sansar.Script.ComponentId,Sansar.Script.ComponentId)">
            <summary>
            ComponentId equality operator.
            </summary>
            <param name="a">First ComponentId to compare.</param>
            <param name="b">Second ComponentId to compare.</param>
            <returns>true if the ComponentIds have the same value.</returns>
            <remarks>This is a value comparison.</remarks>
        </member>
        <member name="M:Sansar.Script.ComponentId.op_Inequality(Sansar.Script.ComponentId,Sansar.Script.ComponentId)">
            <summary>
            ComponentId inequality operator.
            </summary>
            <param name="a">First ComponentId to compare.</param>
            <param name="b">Second ComponentId to compare.</param>
            <returns>true if the ComponentIds have a different value.</returns>
            <remarks>This is a value comparison.</remarks>
        </member>
        <member name="M:Sansar.Script.ComponentId.ToString">
            <summary>
            Converts the id to a hexadecimal string representation. 
            </summary>
            <returns>The hexadecimal string representation.</returns>
            <remarks>This method overrides <see cref="M:System.Object.ToString"/>.</remarks>
        </member>
        <member name="F:Sansar.Script.ComponentId.Invalid">
            <summary>
            The invalid id object.
            </summary>
            <remarks>This value is used by certain APIs to represent an invalid ComponentId.</remarks>
        </member>
        <member name="T:Sansar.Script.Reflective">
            <summary>
            Base class which provides for simple reflection of methods through a given interface.
            </summary>
            <remarks>
            If the object already implements the interface it will return the object directly. Otherwise an facade class is created to map between
            public methods and properties. 
            Two scripts which use a Reflective object to communicate via script events are below:
            <example>
            <code lang="C#" src="examples/SharedStopwatchExample.cs" />
            </example>
            <example>
            <code lang="C#" src="examples/SharedStopwatchReaderExample.cs" />
            </example>
            </remarks>
        </member>
        <member name="M:Sansar.Script.Reflective.#ctor">
            <summary>
            Default constructor.
            </summary>
            <remarks/>
        </member>
        <member name="M:Sansar.Script.Reflective.AsInterface``1">
            <summary>
            Returns a TInterface object if one can be created, null otherwise
            </summary>
            <typeparam name="TInterface">The interface describing the methods and properties desired.</typeparam>
            <returns>A TInterface instance if this object is compatible. An object is compatible if it implements the interface or
            has public methods and properties which match TInterface.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Script.Reflective.FullInterface(System.String)">
            <summary>
            Generates a string which shows all the members which can be reflected.
            </summary>
            <returns>The generated string. If no members can be reflected, returns the empty string</returns>
            <param name="interfaceName">The name to give the generated interface class in the output.</param>
        </member>
        <member name="T:Sansar.Script.Reflective.Context">
            <summary>
            Use with <see cref="T:Sansar.Script.RegisterReflectiveAttribute"/>  and <see cref="M:Sansar.Script.Reflective.Register(Sansar.Script.Reflective.Context)"/> to specify specific access context for the registered reflective class. 
            </summary>
        </member>
        <member name="F:Sansar.Script.Reflective.Context.None">
            <summary>Not discoverable via FindReflective or FindScript</summary>
        </member>
        <member name="F:Sansar.Script.Reflective.Context.ScenePrivate">
            <summary>Findable in ScenePrivate.FindReflective</summary>
        </member>
        <member name="F:Sansar.Script.Reflective.Context.ScenePublic">
            <summary>Findable in ScenePublic.FindReflective</summary>
        </member>
        <member name="F:Sansar.Script.Reflective.Context.ObjectPrivate">
            <summary>Works only for running scripts, makes them findable in ObjectPrivate.FindScript and ObjectPrivate.FindScripts.</summary>
        </member>
        <member name="F:Sansar.Script.Reflective.Context.All">
            <summary>All contexts.</summary>
        </member>
        <member name="M:Sansar.Script.Reflective.Register">
            <summary>
            Register this object to be found with <see cref="M:Sansar.Simulation.ScenePrivate.FindReflective(System.String)"/>
            </summary>
            <remarks>Will register this reflective object with <see cref="P:Sansar.Script.Reflective.AllowedContexts" /> if overridden, otherwise with ScenePrivate and ObjectPrivate.</remarks>
        </member>
        <member name="M:Sansar.Script.Reflective.Unregister">
            <summary>
            Unregister this object so it will not be found with <see cref="M:Sansar.Simulation.ScenePrivate.FindReflective(System.String)"/>
            </summary>
            <remarks>If the Reflective object has not previously been registered nothing happens.</remarks>
        </member>
        <member name="P:Sansar.Script.Reflective.AllowedContexts">
            <summary>Internal Use Only. Overridden by subclasses to return only those contexts requested which are allowed for that type of script.</summary>
            <remarks>Used internally to prevent visiting guest scripts from registering as 'scene level' APIs.</remarks>
        </member>
        <member name="P:Sansar.Script.Reflective.ReflectiveContexts">
            <summary>
            Override ReflectiveContexts to limit which contexts this Reflective interface is available in when registered with.
            </summary>
            <remarks>This can be used to further restrict which contexts an interface is available in. It can not be used to register for a context not allowed by the running script type.
            SceneObjectScript allows ScenePrivate, ScenePublic, ObjectPrivate. ObjectScript allows ObjectPrivate.
            Overriding this will automatically register this Reflective </remarks>
        </member>
        <member name="P:Sansar.Script.Reflective.ReflectiveName">
            <summary>
            Override ReflectiveName to change which name this class will be registered as in the Reflective system.
            </summary>
            <remarks>Defaults to GetType().FullName</remarks>
        </member>
        <member name="M:Sansar.Script.Reflective.IsInterface``1">
            <summary>
            Checks to see if this object can be converted to the interface.
            </summary>
            <typeparam name="TInterface">The interface describing the methods and properties desired.</typeparam>
            <returns>True if the this type matches the given interface.</returns>
            
        </member>
        <member name="T:Sansar.Script.Reflective.ReflectiveBase">
            <summary>Internal support class.</summary>
        </member>
        <member name="F:Sansar.Script.Reflective.ReflectiveBase.originalObject">
            <summary>
            Wrapped object.
            </summary>
        </member>
        <member name="T:Sansar.Script.SimpleScriptEventData">
            <summary>
            Used by <see cref="M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object)"/> to send simple script events.  
            </summary><remarks>Supports sending a single Sansar or System type object between scripts, without the overhead of creating a custom Reflective class and Interface pair. Used by PostSimpleScriptEvent and SimpleScript.OnScriptEvent.</remarks>
        </member>
        <member name="M:Sansar.Script.SimpleScriptEventData.#ctor">
            <summary>Default constructor.</summary>
        </member>
        <member name="P:Sansar.Script.SimpleScriptEventData.Data">
            <summary>
            A single object to pass between scripts.
            </summary><remarks>To be useful, Data must be a system or Sansar type and not a locally defined type. Locally defined types will not be usable by other scripts.</remarks><value/>
        </member>
        <member name="T:Sansar.Script.ScriptEventData">
            <summary>
            Result of a <see cref="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)"/> call.
            </summary>
            <remarks>Use <see cref="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean)"/> to subscribe to events. </remarks>
        </member>
        <member name="P:Sansar.Script.ScriptEventData.Message">
            <summary>
            The message the event was created with.
            </summary>
            <value>This string should always match the corresponding <see cref="M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean)"/> call.</value>
            <remarks/>
        </member>
        <member name="P:Sansar.Script.ScriptEventData.SourceScriptId">
            <summary>
            The id of the script which generated the event.
            </summary>
            <value/>
            <remarks/>
        </member>
        <member name="P:Sansar.Script.ScriptEventData.Data">
            <summary>
            The data passed by <see cref="M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)"/>.
            </summary>
            <value>May be null.</value>
            <remarks/>
        </member>
        <member name="T:Sansar.Script.EventData">
            <summary>
            Base class for events.
            </summary>
            <remarks/>
        </member>
        <member name="M:Sansar.Script.EventData.ToString">
            <summary>
            Generates a string representation of the EventData.
            </summary>
            <returns>The EventData as a string.</returns>
            <remarks/>
        </member>
        <member name="T:Sansar.Script.OperationCompleteEvent">
            <summary>
            Many asynchronous APIs will trigger an OperationCompleteEvent when done.
            </summary>
            <remarks> See <see cref='M:Sansar.Simulation.RigidBodyComponent.SetPosition(Sansar.Vector,Sansar.Script.ScriptBase.OperationComplete)' /></remarks>
        </member>
        <member name="M:Sansar.Script.OperationCompleteEvent.#ctor">
            <summary>
            Default constructor
            </summary>
            <remarks></remarks>
        </member>
        <member name="P:Sansar.Script.OperationCompleteEvent.Success">
            <summary>
            Whether or not the operation completed successfully. 
            </summary>
            <remarks></remarks>
            <value>true if the operation succeeded.</value>
        </member>
        <member name="P:Sansar.Script.OperationCompleteEvent.Message">
            <summary>
            If Success is false, Message may contain more information about the failure.
            </summary>
            <value>Information about the failure.</value>
            <remarks></remarks>
        </member>
        <member name="P:Sansar.Script.OperationCompleteEvent.Exception">
            <summary>
            If an operation threw an exception Success will be false and the exception will be stored here.
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Script.EventDataExtentions.FromArg``1(Sansar.Script.EventData,System.Int32,``0@)">
            <summary>
            Overload this method to handle specific translations. This overload can happen in other assemblies.
            
            This is only intended to be used by generated code.
            </summary>
        </member>
        <member name="T:Sansar.Script.ThrottleException">
            <summary>Thrown when a method is called too often</summary>
            <remarks>The ThrottleException class has information about the throttle rate and how fast the throttle was hit. This exception is thrown before any action is taken by the method.</remarks>
        </member>
        <member name="F:Sansar.Script.ThrottleException.MaxEvents">
            <summary>The maximum number of method calls allowed per <see cref="F:Sansar.Script.ThrottleException.Interval"/></summary>
            <remarks>Method calls past MaxEvents during <see cref="F:Sansar.Script.ThrottleException.Interval"/> will throw an exception and not be counted as events.</remarks>
        </member>
        <member name="F:Sansar.Script.ThrottleException.Interval">
            <summary>The timespan during which at most <see cref="F:Sansar.Script.ThrottleException.MaxEvents"/> can happen without hitting the throttle.</summary> 
            <remarks><see cref="T:Sansar.Script.ThrottleException"/> is thrown if there have already been <see cref="F:Sansar.Script.ThrottleException.MaxEvents"/> successful calls to this method during the last Interval timespan.</remarks>
        </member>
        <member name="F:Sansar.Script.ThrottleException.Elapsed">
            <summary>The timespan during which <see cref="F:Sansar.Script.ThrottleException.MaxEvents"/> method calls have happened.</summary>
            <remarks>Elapsed is a measure of how fast the throttle was hit. All <see cref="F:Sansar.Script.ThrottleException.MaxEvents"/> have happened within the last Elapsed time span.</remarks>
        </member>
        <member name="T:Sansar.Script.ScriptHandle">
            <summary>
            A running script in Sansar.
            </summary>
            <remarks>This is a lightweight handle which is used by some API methods to refer to a script.</remarks>
        </member>
        <member name="M:Sansar.Script.ScriptHandle.Dispose">
            <summary>
            Internal use only
            </summary>
            <remarks></remarks>
        </member>
        <member name="P:Sansar.Script.ScriptHandle.ID">
            <summary>
            The Sansar Script's unique ID. This ID is unique among all scripts currently running in a scene.
            It will change every time the script or scene is started.
            </summary>
            <value>Returns <see cref="F:Sansar.Script.ScriptId.Invalid"/>  if the ScriptHandle is not initialized with a valid script.</value>
            <remarks>Internal.</remarks>
        </member>
        <member name="E:Sansar.Script.ScriptHandle.UnhandledException">
            <summary>
            Run by the script scheduler when the invocation of a script event handler or coroutine throws an 
            exception. </summary><remarks>If <see cref="P:Sansar.Script.ScriptHandle.UnhandledExceptionRecoverable"/> is true then the script may continue 
            running if the handler does not throw an exception. If the handler needs to stop execution of the 
            script it should throw the passed exception. The first argument is either the <see cref="T:Sansar.Script.EventData"/> 
            that was being processed, or the Coroutine that was running when the exception occurred.
            </remarks>
        </member>
        <member name="P:Sansar.Script.ScriptHandle.UnhandledExceptionRecoverable">
            <summary>
            If true a script can keep running if an unhandled exception is processed by <see cref="E:Sansar.Script.ScriptHandle.UnhandledException"/>.
            </summary>
            <value>Boolean indicating if the script may continue running after an unhandled exception occurs.</value>
            <remarks>If this is true and the <see cref="E:Sansar.Script.ScriptHandle.UnhandledException"/> event is set and does not throw and exception
            the script will continue to be scheduled.</remarks>
        </member>
        <member name="T:Sansar.Script.Testing.AssertionFailureException">
            <summary>
            Thrown by <see cref="T:Sansar.Script.Testing.Assertions"/> tests.
            </summary>
            <remarks>
            </remarks>
        </member>
        <member name="P:Sansar.Script.Testing.AssertionFailureException.Message">
            <summary>
            The exception message
            </summary>
        </member>
        <member name="P:Sansar.Script.Testing.AssertionFailureException.StackTrace">
            <summary>
            The stack trace
            </summary>
        </member>
        <member name="M:Sansar.Script.Testing.AssertionFailureException.ToString">
            <summary>
            Short description of the exception.
            </summary>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="T:Sansar.Script.Testing.Assertions">
            <summary>
            Contains various test methods which throw exceptions if the conditions are false.
            </summary>
            <remarks>This class is restricted and may not be included in scripts for sale in the marketplace.
            All APIs are subject to change.</remarks>
        </member>
        <member name="P:Sansar.Script.Testing.Assertions.LogSuccessful">
            <summary>
            Enables or disables logging of successful assertion diagnostic messages.
            </summary>
            <remarks>This can be used to trace test execution.</remarks>
            <value></value>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.Fail(System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception always.
            </summary>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.True(System.Boolean,System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception if the passed argument is false.
            </summary>
            <param name="val">The value that must be true.</param>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.NotNull(System.Object,System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception if the passed argument is not null.
            </summary>
            <param name="val">The value that must be null.</param>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.Null(System.Object,System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception if the passed argument is not null.
            </summary>
            <param name="val">The value that must be null.</param>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.Type``1(System.Object,System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception if the passed type is not of the same type as the type param or if the obj is null.
            </summary>
            <typeparam name="T">The type to confirm.</typeparam>
            <param name="obj">The object to check.</param>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.Type(System.String,System.Object,System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception if the <see cref="P:System.Type.FullName"/> of the passed object does not match the passed typename. 
            </summary>
            <param name="typename">The expected typename.</param>
            <param name="obj">The object to check.</param>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.Equal``1(``0,``0,System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception if the passed objects do not compare as equal via <see cref="M:System.IComparable.CompareTo(System.Object)"/>. Will NOT throw if both objects are null.
            </summary>
            <typeparam name="T">The type of the objects.</typeparam>
            <param name="a">The first object to check.</param>
            <param name="b">The second object to check.</param>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.NotEqual``1(``0,``0,System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception if the passed objects compare as equal via <see cref="M:System.IComparable.CompareTo(System.Object)"/>.
            </summary>
            <typeparam name="T">The type of the objects.</typeparam>
            <param name="a">The first object to check.</param>
            <param name="b">The second object to check.</param>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.AlmostEqual(System.Single,System.Single,System.Int32,System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception if the two floats are not approximately equal.
            </summary>
            <param name="a">The first float to check.</param>
            <param name="b">The second float to check.</param>
            <param name="significantFigures">The number of significant digits to compare</param>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.AlmostEqual(Sansar.Vector,Sansar.Vector,System.Int32,System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception if the two vectors are not approximately equal.
            </summary>
            <param name="a">The first vector to check.</param>
            <param name="b">The second vector to check.</param>
            <param name="significantFigures">The number of significant digits to compare</param>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.AlmostEqual(Sansar.Quaternion,Sansar.Quaternion,System.Int32,System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception if the two Quaternions are not approximately equal.
            </summary>
            <param name="a">The first Quaternion to check.</param>
            <param name="b">The second Quaternion to check.</param>
            <param name="significantFigures">The number of significant digits to compare</param>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.Greater``1(``0,``0,System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception if the first object does not compare as greater via <see cref="M:System.IComparable.CompareTo(System.Object)"/>.
            </summary>
            <typeparam name="T">The type of the objects.</typeparam>
            <param name="a">The first object to check.</param>
            <param name="b">The second object to check.</param>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="M:Sansar.Script.Testing.Assertions.NullOrMissing(System.String,System.String,System.String,System.String,System.String,System.Int32)">
            <summary>
            Throws an exception if the message is non-null and contains the contents substring.
            </summary>
            <param name="message">The message to check.</param>
            <param name="contents">A substring to check.</param>
            <param name="diagnostic">Optional string to show with a faulure exception.</param>
            <param name="memberName">generated by the compiler</param>
            <param name="sourceFilePath">generated by the compiler</param>
            <param name="sourceLineNumber">generated by the compiler</param>
            <remarks>The method is restricted. The method is subject to change or removal at any time and 
            may not be included in scripts for sale in the marketplace.</remarks>
        </member>
        <member name="T:Sansar.Color">
            <summary>
            The Color class holds RGBA values that define a color.
            </summary>
            <remarks></remarks>
        </member>
        <member name="F:Sansar.Color.Black">
            <summary>Black</summary><remarks/>
        </member>
        <member name="F:Sansar.Color.Blue">
            <summary>Blue</summary><remarks/>
        </member>
        <member name="F:Sansar.Color.Clear">
            <summary>Clear</summary><remarks/>
        </member>
        <member name="F:Sansar.Color.Cyan">
            <summary>Cyan</summary><remarks/>
        </member>
        <member name="F:Sansar.Color.Gray">
            <summary>Gray</summary><remarks/>
        </member>
        <member name="F:Sansar.Color.Green">
            <summary>Green</summary><remarks/>
        </member>
        <member name="F:Sansar.Color.Magenta">
            <summary>Magenta</summary><remarks/>
        </member>
        <member name="F:Sansar.Color.Red">
            <summary>Red</summary><remarks/>
        </member>
        <member name="F:Sansar.Color.White">
            <summary>White</summary><remarks/>
        </member>
        <member name="F:Sansar.Color.Yellow">
            <summary>Yellow</summary><remarks/>
        </member>
        <member name="M:Sansar.Color.#ctor(System.Single,System.Single,System.Single)">
            <summary>
            Creates a new solid color with the supplied values.
            </summary>
            <param name="r">The red component.</param>
            <param name="g">The green component.</param>
            <param name="b">The blue component.</param>
            <remarks>The alpha component is set to 1.</remarks>
        </member>
        <member name="M:Sansar.Color.#ctor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a new color with the supplied values.
            </summary>
            <param name="r">The red component.</param>
            <param name="g">The green component.</param>
            <param name="b">The blue component.</param>
            <param name="a">The alpha component.</param>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Color.#ctor(Mono.Simd.Vector4f)">
            <summary>
            Creates a new color with the supplied values.
            </summary>
            <remarks/>
            <param name="v">Initializes the color from a <see cref="T:Mono.Simd.Vector4f"/>.</param>
        </member>
        <member name="M:Sansar.Color.ToRGB">
            <summary>
            Generates a string RGB representation of the color.
            </summary>
            <returns>The RGB color as a string.</returns>
            <remarks>RGB values are based on a scale from 0 to 255.</remarks>
        </member>
        <member name="M:Sansar.Color.ToRGBA">
            <summary>
            Generates a string RGBA representation of the color.
            </summary>
            <returns>The RGBA color as a string.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Color.op_Implicit(Sansar.Color)~Mono.Simd.Vector4f">
            <summary>
            Converts a Color to a Mono.Simd.Vector4f
            </summary>
            <param name="c">Color to convert</param>
            <returns/>
            <remarks/>
        </member>
        <member name="M:Sansar.Color.op_Implicit(Mono.Simd.Vector4f)~Sansar.Color">
            <summary>
            Converts a Mono.Simd.Vector4f to a Color.
            </summary>
            <param name="v">Vector to convert</param>
            <returns/>
            <remarks/>
        </member>
        <member name="P:Sansar.Color.R">
            <summary>
            The red component of the color.
            </summary>
            <remarks/>
            <returns/>
        </member>
        <member name="P:Sansar.Color.G">
            <summary>
            The green component of the color.
            </summary>
            <remarks/>
            <returns/>
        </member>
        <member name="P:Sansar.Color.B">
            <summary>
            The blue component of the color.
            </summary>
            <remarks/>
            <returns/>
        </member>
        <member name="P:Sansar.Color.A">
            <summary>
            The alpha component of the color.
            </summary>
            <remarks>Alpha is 1 by default.</remarks>
            <returns/>
        </member>
        <member name="M:Sansar.Color.op_Addition(Sansar.Color,Sansar.Color)">
            <summary>
            Performs color addition.
            </summary>
            <param name="a">The first Color.</param>
            <param name="b">The second Color.</param>
            <returns>A new color that is the sum of the arguments.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Color.op_Subtraction(Sansar.Color,Sansar.Color)">
            <summary>
            Performs color subtraction.
            </summary>
            <param name="a">The first Color.</param>
            <param name="b">The second Color.</param>
            <returns>A new color that is the difference of the arguments.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Color.op_Multiply(Sansar.Color,System.Single)">
            <summary>
            Performs a color scalar multiplication.
            </summary>
            <param name="c">The color.</param>
            <param name="s">The scalar.</param>
            <returns>Returns a new color with value [c.R*s, c.G*s, c.B*s, c.A*s]</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Color.op_Multiply(System.Single,Sansar.Color)">
            <summary>
            Performs a color scalar multiplication.
            </summary>
            <param name="s">The scalar.</param>
            <param name="c">The color.</param>
            <returns>Returns a new color with value [s*c.R, s*c.G, s*c.B, s*c.A]</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Color.op_Division(Sansar.Color,System.Single)">
            <summary>
            Divides color components by a scalar.
            </summary>
            <param name="c">The color.</param>
            <param name="s">The scalar.</param>
            <returns>Returns a new color with value [c.R/s, c.G/s, c.B/s, c.A/s]</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Color.ToString">
            <summary>
            Generates a string representation of the color.
            </summary>
            <returns>The color as a string.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Color.ToString(System.String)">
            <summary>
            Generates a string representation of the color.
            </summary>
            <param name="format">Format to use for each of the coordinates.</param>
            <returns>The color as a string.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Color.Parse(System.String)">
            <summary>
            Parse a Color from a string.
            </summary>
            <param name='colorString'><![CDATA[A string of the format (R,G,B) or (R,G,B,A)]]></param>
            <returns>The Color parsed from the string.</returns>
            <exception cref="T:System.ArgumentException">If colorString is null.</exception>
            <exception cref="T:System.FormatException">If the string is not a valid color or its components are not valid floats.</exception>
            <example><code lang="C#"><![CDATA[Color myColor = Color.Parse("(0.5,1.0,0.8)");]]></code></example>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Color.TryParse(System.String,Sansar.Color@)">
            <summary>
            Attempt to parse a Color from a string
            </summary>
            <param name="colorString"><![CDATA[A string of the format (R,G,B) or (R,G,B,A)]]></param>
            <param name="color">The color that will be set if colorString represents a valid color.</param>
            <returns>True if successfully parsed a color, false if not.</returns>
            <example><code lang="C#">
            <![CDATA[Color myColor;
            if (Color.Parse("(0.5,1.0,0.8)", out myColor)
            {
                // myColor is set
            }]]></code></example>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Color.Lerp(Sansar.Color@,System.Single)">
            <summary>
            Performs a linear interpolation between two colors.
            </summary>
            <param name="c">The second color.</param>
            <param name="amount">Value from [0..1] indicating the weight for the second color.</param>
            <returns>A color that is linearly interpolated between the two sources.</returns>
            <remarks>This method performs the linear interpolation as:
            <para>a + (c-a) * amount.</para>
            An amount value of 0 returns the original color, and 1.0 returns color c.</remarks>
        </member>
        <member name="T:Sansar.Metadata.IAssemblyMetadata">
            <summary>
            Provides information about user assemblies.
            </summary><remarks>internal use only</remarks>
        </member>
        <member name="P:Sansar.Metadata.IAssemblyMetadata.CompilerApiVersion">
            <summary>
            Returns the API version the assembly was compiled against.
            </summary>
            <returns>The API version.</returns><remarks>internal use only</remarks>
        </member>
        <member name="P:Sansar.Metadata.IAssemblyMetadata.AllTypes">
            <summary>
            Collects all script types in the assembly, sorted by name
            </summary>
            <returns>An array of all script types in the assembly</returns><remarks>internal use only</remarks>
        </member>
        <member name="T:Sansar.Metadata.IScriptMetadata">
            <summary>
            Provides information about user scripts.
            </summary><remarks>internal use only</remarks>
        </member>
        <member name="P:Sansar.Metadata.IScriptMetadata.Name">
            <summary>
            The full name of the script class as returned by <see cref="P:System.Type.FullName"/>. 
            </summary><remarks>internal use only</remarks><value></value>
        </member>
        <member name="P:Sansar.Metadata.IScriptMetadata.Type">
            <summary>
            The type of the script.
            </summary><remarks>internal use only</remarks><value></value>
        </member>
        <member name="P:Sansar.Metadata.IScriptMetadata.RegisterReflective">
            <summary>
            True if <see cref="T:Sansar.Script.RegisterReflectiveAttribute"/> is attached to this type.
            </summary><remarks>internal use only</remarks><value></value>
        </member>
        <member name="M:Sansar.Metadata.IScriptMetadata.Create">
            <summary>
            Creates a new instance of the script.
            </summary>
            <returns>The new script</returns><remarks>internal use only</remarks>
        </member>
        <member name="T:Sansar.Mathf">
            <summary>
            Floating point math constants and utilities.
            </summary>
            <remarks>Provides common floating point constants static methods.</remarks>
        </member>
        <member name="M:Sansar.Mathf.IsApproximatelyEqualAbsolute(System.Single,System.Single)">
            <summary>
            Compares the two given floats and returns true if they are approximately equal
            </summary>
            <param name="a">The first float to compare.</param>
            <param name="b">The second float to compare.</param>
            <returns>true if the float are approximately equal, false otherwise.</returns>
            <remarks>Checks if the absolute value of the difference of the values is small.</remarks>
        </member>
        <member name="M:Sansar.Mathf.Clamp(System.Single,System.Single,System.Single)">
            <summary>
            Clamps a float to the specified range
            </summary>
            <param name="x">The float to compare.</param>
            <param name="min">The minimum of the clamp range.</param>
            <param name="max">The maximum of the clamp range.</param>
            <returns>x if x is within [min,max] otherwise min or max, whichever is closer to the value of x.</returns>
        </member>
        <member name="F:Sansar.Mathf.PI">
            <summary>
            Pi
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Mathf.E">
            <summary>
            E
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Mathf.RootTwo">
            <summary>
            The square root of two.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Mathf.RootTwoOverTwo">
            <summary>
            The square root of 2 divided by two.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Mathf.TwoPi">
            <summary>
            Two times pi.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Mathf.PiOverTwo">
            <summary>
            Pi divided by two.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Mathf.TwoOverPi">
            <summary>
            Two divided by pi.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Mathf.DegreesPerRadian">
            <summary>
            Conversion factor to convert radians to degrees.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Mathf.RadiansPerDegree">
            <summary>
            Conversion factor to convert degrees to radians.
            </summary>
            <remarks/>
        </member>
        <member name="T:Sansar.VectorExtensions">
            <summary>
            Additional methods for Mono.Simd.Vector4f.
            </summary>
            <remarks/>
        </member>
        <member name="M:Sansar.VectorExtensions.Sin(Mono.Simd.Vector4f)">
            <summary>
            Returns the approximate sine of the four floats in the vector.
            </summary>
            <param name="a">A vector of 4 floats</param>
            <returns>A fast approximation of the sine of each of the 4 floats.</returns>
            <remarks>Uses <see cref="M:Sansar.VectorExtensions.SinZeroPiOverTwo(Mono.Simd.Vector4f)"/></remarks>
        </member>
        <member name="M:Sansar.VectorExtensions.SinZeroPiOverTwo(Mono.Simd.Vector4f)">
            <summary>
            Returns the approximate sine of each element in the vector.
            </summary>
            <param name="x">A vector of 4 floats.</param>
            <returns>The approximate sine of each element.</returns>
            <remarks>Results only valid if each element is in [0 and Pi/2]. For unconstrained input use <see cref="M:Sansar.VectorExtensions.Sin(Mono.Simd.Vector4f)"/>.
            Calculated using the Maclaurin series as sin(x) = x - (x^3)/3! + (x^5)/5! - (x^7)/7! + (x^9)/9!
            </remarks>
        </member>
        <member name="M:Sansar.VectorExtensions.Cos(Mono.Simd.Vector4f)">
            <summary>
            Returns the approximate cosine of the four floats in the vector.
            </summary>
            <param name="x">A vector of 4 floats.</param>
            <returns>The approximate cosine of each element.</returns>
            <remarks>Uses <see cref="M:Sansar.VectorExtensions.Sin(Mono.Simd.Vector4f)"/></remarks>
        </member>
        <member name="M:Sansar.VectorExtensions.SinSlow(Mono.Simd.Vector4f)">
            <summary>
            Returns the sine each element of the vector.
            </summary>
            <param name="v1">A vector of 4 floats.</param>
            <returns>The sine of each element.</returns>
            <remarks>Uses <see cref="M:System.Math.Sin(System.Double)"/>. The results are more accurate than <see cref="M:Sansar.VectorExtensions.Sin(Mono.Simd.Vector4f)"/> but take 
            approximately 3x the time to calculate.</remarks>
        </member>
        <member name="M:Sansar.VectorExtensions.CosSlow(Mono.Simd.Vector4f)">
            <summary>
            Returns the cosine each element of the vector.
            </summary>
            <param name="v1">A vector of 4 floats.</param>
            <returns>The cosine of each element.</returns>
            <remarks>Uses <see cref="M:System.Math.Cos(System.Double)"/>. The results are more accurate than <see cref="M:Sansar.VectorExtensions.Cos(Mono.Simd.Vector4f)"/> but take 
            approximately 3x the time to calculate.</remarks>
        </member>
        <member name="M:Sansar.VectorExtensions.SelectWithMask(Mono.Simd.Vector4i,Mono.Simd.Vector4f,Mono.Simd.Vector4f)">
             <summary>
             Creates a new vector4f from the given vectors, selecting a component from 
             sourceIfTrue if the corresponding component in the mask is -1 and from sourceIfFalse
             if 0.
             </summary>
             <param name="sourceIfTrue">The vector containing the elements that are selected if the mask is true.</param>
             <param name="sourceIfFalse">The vector containing the elements that are selected if the maks is false.</param>
             <param name="mask">The mask vector.</param>
             <returns>A new vector with elements selected from the source vectors based on the mask.</returns>
             <remarks>Each element in the mask must be 0 or -1 or the results are undefined.
             Courtesy of Mark++, http://markplusplus.wordpress.com/2007/03/14/fast-sse-select-operation/ 
             <example>
             <code lang="C#">
             Vector4f left = new Vector4f(1, 2, 3, 4);
             Vector4f right = new Vector4f(5, 6, 7, 8);
            
             // Select X and Z from right and Y and W from left
             Vector4i mask = new Vector4i(0, -1, 0, -1);
             var result = mask.SelectWithMask(left, right);
             Assertions.AlmostEqual((Vector)new Vector4f(5, 2, 7, 4), result);
             </code>
             </example>
             </remarks>
        </member>
        <member name="P:Sansar.MethodTimingAttribute.Timing">
            <summary>
            A value in processor cycles to use for the tagged method instead of using introspection.
            </summary>
        </member>
        <member name="T:Sansar.Quaternion">
            <summary>
            Represents a quaternion orientation.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Quaternion.Identity">
            <summary>
            The identity quaternion.
            </summary>
            <remarks/>
        </member>
        <member name="M:Sansar.Quaternion.#ctor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a new quaternion with the supplied values.
            </summary>
            <param name="x">The x coordinate.</param>
            <param name="y">The y coordinate.</param>
            <param name="z">The z coordinate.</param>
            <param name="w">The w coordinate.</param>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Quaternion.#ctor(Mono.Simd.Vector4f@)">
            <summary>
            Creates a new quaternion with the supplied values.
            </summary>
            <param name="v">Initializes the quaternion from a <see cref="T:Mono.Simd.Vector4f"/>.</param>
            <remarks/>
        </member>
        <member name="M:Sansar.Quaternion.Dot(Sansar.Quaternion@)">
            <summary>
            Performs a scalar or dot product.
            </summary>
            <param name="b">The second quaternion.</param>
            <returns>Returns X*b.X+Y*b.Y+Z*b.Z+W*b.W </returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Quaternion.FromEulerAngles(Sansar.Vector@)">
            <summary>
            Generates the corresponding quaternion.
            </summary>
            <param name="a">A vector of angles in radians.</param>
            <returns>The corresponding quaternion.</returns>
            <remarks>Rotates first around X (local/global coincide), then around new local Y, and finally around new local Z.</remarks>
        </member>
        <member name="M:Sansar.Quaternion.op_Multiply(Sansar.Quaternion@,Sansar.Quaternion@)">
            <summary>
            Multiplies two quaternions.
            </summary>
            <param name="a">The first quaternion</param>
            <param name="b">The second quaternion</param>
            <returns>Returns the composed orientation for the two quaternions.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Quaternion.Inverse">
            <summary>
            Gets the Inverse of the quaternion.
            </summary>
            <returns>A quaternion inverse, such that q * q.Inverse() == q.Inverse() * q == Quaternion.Identity</returns>
            <remarks>Results are undefined if the length of the quaternion is close to 0.</remarks>
        </member>
        <member name="M:Sansar.Quaternion.op_UnaryNegation(Sansar.Quaternion)">
            <summary>
            Negates a quaternion
            </summary>
            <param name="q">The quaternion to negate</param>
            <returns></returns>
            <remarks>Note this is not a quaternion inverse, it is a component-wise multiplication by -1. For a quaternion inverse use <see cref="M:Sansar.Quaternion.Inverse"/></remarks>
        </member>
        <member name="M:Sansar.Quaternion.op_Implicit(Mono.Simd.Vector4f)~Sansar.Quaternion">
            <summary>
            Converts a Mono.Simd.Vector4f to a quaternion.
            </summary>
            <param name="v">The vector to convert</param>
            <returns/>
            <remarks/>
        </member>
        <member name="M:Sansar.Quaternion.op_Implicit(Sansar.Quaternion)~Mono.Simd.Vector4f">
            <summary>
            Converts a quaternion to a Mono.Simd.Vector4f.
            </summary>
            <param name="v">The quaternion to convert</param>
            <returns/>
            <remarks/>
        </member>
        <member name="P:Sansar.Quaternion.X">
            <summary>
            The X coordinate of the quaternion.
            </summary>
            <remarks/>
            <value/>
        </member>
        <member name="P:Sansar.Quaternion.Y">
            <summary>
            The Y coordinate of the quaternion.
            </summary>
            <remarks></remarks>
            <value/>
        </member>
        <member name="P:Sansar.Quaternion.Z">
            <summary>
            The Z coordinate of the quaternion.
            </summary>
            <remarks></remarks>
            <value/>
        </member>
        <member name="P:Sansar.Quaternion.W">
            <summary>
            The W coordinate of the quaternion.
            </summary>
            <remarks></remarks>
            <value/>
        </member>
        <member name="P:Sansar.Quaternion.Item(System.Int32)">
            <summary>
            Allows getting coordinates by index.
            </summary>
            <param name="index">0=>X, 1=>Y, 2=>Z, 3=>W</param>
            <returns></returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Quaternion.FromLook(Sansar.Vector,Sansar.Vector)">
            <summary>
            Creates a new rotation with the specified direction and up vectors.
            </summary>
            <param name="facing">The direction to look.</param>
            <param name="up">The up direction.</param>
            <returns>A new quaternion representing this rotation.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Quaternion.FromLookOrthoNormal(Sansar.Vector@,Sansar.Vector@,Sansar.Vector@)">
            <summary>
            Creates a new rotation with the specified direction and up vectors.
            </summary>
            <param name="facing">The direction to look.</param>
            <param name="up">The up direction.</param>
            <param name="left">The left direction.</param>
            <returns>A new quaternion representing this rotation.</returns>
            <remarks>The 3 given vectors must be orthonormal. See <see cref="M:Sansar.Vector.Orthonormalize(Sansar.Vector@,Sansar.Vector@)"/></remarks>
        </member>
        <member name="M:Sansar.Quaternion.Normalized">
            <summary>
            Returns a quaternion with the same orientation and unit length.
            </summary>
            <returns>A quaternion with the same orientation and unit length.</returns>
            <remarks>Results are undefined if the length of the quaternion is close to 0.</remarks>
        </member>
        <member name="M:Sansar.Quaternion.Length">
            <summary>
            Calculates the length of this quaternion.
            </summary>
            <returns>The length of the quaternion.</returns>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Quaternion.LengthSquared">
            <summary>
            Calculates the length squared of this quaternion.
            </summary>
            <returns>The magnitude of the vector.</returns>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Quaternion.ToString">
            <summary>
            Generates a string representation of the quaternion.
            </summary>
            <returns>The quaternion as a string.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Quaternion.ToString(System.String)">
            <summary>
            Generates a string representation of the quaternion.
            </summary>
            <param name="format">Format to use for each of the coordinates.</param>
            <returns>The quaternion as a string.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Quaternion.Parse(System.String)">
            <summary>
            Parse a Quaternion from a string.
            </summary>
            <param name="quaternionString">A string of the format "[X,Y,Z,W]"</param>
            <returns>The Quaternion parsed from quaternionString.</returns>
            <exception cref="T:System.ArgumentException">If quaternionString is null.</exception>
            <exception cref="T:System.FormatException">If the string is not a valid quaternion or its components are not valid floats.</exception>
            <remarks></remarks>
            <example><code lang="C#">Quaternion myQuat = Quaternion.Parse("[0.0,0.0,0.0,1.0]");</code></example>
        </member>
        <member name="M:Sansar.Quaternion.TryParse(System.String,Sansar.Quaternion@)">
            <summary>Try to parse a Quaternion from a string.</summary>
            <param name="quaternionString">A string of the format "[X,Y,Z,W]"</param>
            <param name="quaternion">The quaternion that will be set if quaternionString represents a valid quaternion.</param>
            <returns>True if successfully parsed a quaternion, false if not.</returns>
            <example><code lang="C#">Quaternion myQuat;
            if (Quaternion.TryParse("[0.0,0.0,0.0,1.0]", out myQuat)
            {
                // myQuat is set.
            }</code></example>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Quaternion.GetEulerAngles">
            <summary>
            Converts this rotation to a vector of euler angles
            </summary>
            <returns>A vector of angles in radians corresponding to this quaternion.</returns>
            <remarks>Returns angles which can be passed to <see cref="M:Sansar.Quaternion.FromEulerAngles(Sansar.Vector@)"/> to create this quaternion.</remarks>
        </member>
        <member name="M:Sansar.Quaternion.FromAngleAxis(System.Single,Sansar.Vector@)">
            <summary>
            Generates the corresponding quaternion.
            </summary>
            <param name="angle">Angle of rotation in radians</param>
            <param name="axis">A normalized axis of rotation</param>
            <returns>The corresponding quaternion.</returns>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Quaternion.ShortestRotation(Sansar.Vector@,Sansar.Vector@)">
            <summary>
            Generates the shortest rotation quaternion to rotate from one vector to another.
            </summary>
            <param name="from">Vector rotating from.</param>
            <param name="to">Vector rotating to.</param>
            <returns>The corresponding quaternion.</returns>
            <remarks>This function assumes that the passed in vectors are normalized.</remarks>
        </member>
        <member name="M:Sansar.Quaternion.ToAngleAxis(System.Single@,Sansar.Vector@)">
            <summary>
            Gets the angle-axis representation
            </summary>
            <param name="angle">Angle of rotation in radians</param>
            <param name="axis">A normalized axis of rotation</param>
            <remarks>For the Quaternion Identity (no rotation) the axis is undefined and will be given as (0,0,1)</remarks>
        </member>
        <member name="T:Sansar.Vector">
            <summary>
            The Vector class represents a 3 dimensional vector.
            </summary>
            <remarks></remarks>
        </member>
        <member name="F:Sansar.Vector.Up">
            <summary>
            Deprecated up Vector
            </summary>
            <remarks/>
            <seealso cref="F:Sansar.Vector.ObjectUp"/>
        </member>
        <member name="F:Sansar.Vector.Forward">
            <summary>
            Deprecated forward vector.
            </summary>
            <remarks/>
            <seealso cref="F:Sansar.Vector.ObjectForward"/>
        </member>
        <member name="F:Sansar.Vector.Left">
            <summary>
            Deprecated left vector.
            </summary>
            <remarks/>
            <seealso cref="F:Sansar.Vector.ObjectLeft"/>
        </member>
        <member name="F:Sansar.Vector.Right">
            <summary>
            Deprecated right vector.
            </summary>
            <remarks/>
            <seealso cref="F:Sansar.Vector.ObjectRight"/>
        </member>
        <member name="F:Sansar.Vector.Down">
            <summary>
            Deprecated down vector.
            </summary>
            <remarks/>
            <seealso cref="F:Sansar.Vector.ObjectDown"/>
        </member>
        <member name="F:Sansar.Vector.Back">
            <summary>
            Deprecated back vector.
            </summary>
            <remarks/>
            <seealso cref="F:Sansar.Vector.ObjectBack"/>
        </member>
        <member name="F:Sansar.Vector.ObjectUp">
            <summary>
            The default world up vector.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Vector.ObjectForward">
            <summary>
            The default world forward vector.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Vector.ObjectLeft">
            <summary>
            The default world left vector.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Vector.ObjectRight">
            <summary>
            The default world right vector.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Vector.ObjectDown">
            <summary>
            The default world down vector.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Vector.ObjectBack">
            <summary>
            The default world back vector.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Vector.Zero">
            <summary>
            A vector with all components 0.
            </summary>
            <remarks/>
        </member>
        <member name="F:Sansar.Vector.One">
            <summary>
            A vector with all components 1.
            </summary>
            <remarks/>
        </member>
        <member name="P:Sansar.Vector.X">
            <summary>
            The X coordinate of the vector.
            </summary>
            <remarks/>
            <returns/>
        </member>
        <member name="P:Sansar.Vector.Y">
            <summary>
            The Y coordinate of the vector.
            </summary>
            <remarks/>
            <returns/>
        </member>
        <member name="P:Sansar.Vector.Z">
            <summary>
            The Z coordinate of the vector.
            </summary>
            <remarks/>
            <returns/>
        </member>
        <member name="P:Sansar.Vector.W">
            <summary>
            The W coordinate of the vector.
            </summary>
            <remarks>W should be 0 and is assumed to be 0 for most operations.</remarks>
            <returns/>
        </member>
        <member name="P:Sansar.Vector.Item(System.Int32)">
            <summary>
            Allows getting coordinates by index.
            </summary>
            <param name="index">0=>X, 1=>Y, 2=>Z, 3=>W</param>
            <returns></returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.op_Addition(Sansar.Vector@,Sansar.Vector@)">
            <summary>
            Performs vector addition.
            </summary>
            <param name="a">The first Vector.</param>
            <param name="b">The second Vector.</param>
            <returns>A new vector that is the sum of the arguments.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.op_Subtraction(Sansar.Vector@,Sansar.Vector@)">
            <summary>
            Performs vector subtraction.
            </summary>
            <param name="a">The first Vector.</param>
            <param name="b">The second Vector.</param>
            <returns>A new vector that is the difference of the arguments.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.op_Multiply(Sansar.Vector@,System.Single)">
            <summary>
            Performs a vector scalar multiplication.
            </summary>
            <param name="a">The vector.</param>
            <param name="b">The scalar.</param>
            <returns>Returns a new vector with value [a.X*b, a.Y*b, a.Z*b, a.W*b]</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.op_Multiply(System.Single,Sansar.Vector@)">
            <summary>
            Performs a vector scalar multiplication.
            </summary>
            <param name="a">The scalar.</param>
            <param name="b">The vector.</param>
            <returns>Returns a new vector with value [a*b.X, a*b.Y, a*b.Z, a*b.W]</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.op_Division(Sansar.Vector@,System.Single)">
            <summary>
            Divides vector components by a scalar.
            </summary>
            <param name="a">The vector.</param>
            <param name="b">The scalar.</param>
            <returns>Returns a new vector with value [a.X/b, a.Y/b, a.Z/b, a.W/b]</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.Dot(Sansar.Vector@)">
            <summary>
            Performs a 3-component scalar or dot product.
            </summary>
            <param name="b">The second vector.</param>
            <returns>Returns X*b.X+Y*b.Y+Z*b.Z</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.ToString">
            <summary>
            Generates a string representation of the vector.
            </summary>
            <returns>The vector as a string.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.ToString(System.String)">
            <summary>
            Generates a string representation of the vector.
            </summary>
            <param name="format">Format to use for each of the coordinates.</param>
            <returns>The vector as a string.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.Parse(System.String)">
            <summary>
            Parse a Vector from a string.
            </summary>
            <param name='vectorString'><![CDATA[A string of the format <X,Y,Z> or <X,Y,Z,W>]]></param>
            <returns>The Vector parsed from the string.</returns>
            <exception cref="T:System.ArgumentException">If vectorString is null.</exception>
            <exception cref="T:System.FormatException">If the string is not a valid vector or its components are not valid floats.</exception>
            <example><code lang="C#"><![CDATA[Vector myVector = Vector.Parse("<1,2.34,5.6>");]]></code></example>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Vector.TryParse(System.String,Sansar.Vector@)">
            <summary>
            Attempt to parse a Vector from a string
            </summary>
            <param name="vectorString"><![CDATA[A string of the format <X,Y,Z> or <X,Y,Z,W>]]></param>
            <param name="vector">The vector that will be set if vectorString represents a valid vector.</param>
            <returns>True if successfully parsed a vector, false if not.</returns>
            <example><code lang="C#">
            <![CDATA[Vector myVector;
            if (Vector.Parse("<1,2.34,5.6>", out myVector)
            {
                // myVector is set
            }]]></code></example>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Vector.ToString4(System.String)">
            <summary>
            Generates a string representation of the vector, including the W parameter.
            </summary>
            <param name="format">Format to use for each of the coordinates.</param>
            <returns>The vector as a string.</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.#ctor(System.Single)">
            <summary>
            Creates a new vector with all values set to the given value.
            </summary>
            <param name="all">The value for all coordinates.</param>
            <remarks>W will be set to 0.</remarks>
        </member>
        <member name="M:Sansar.Vector.#ctor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a new vector with the supplied values.
            </summary>
            <param name="x">The x coordinate.</param>
            <param name="y">The y coordinate.</param>
            <param name="z">The z coordinate.</param>
            <param name="w">The w coordinate. W defaults to 0 and is assumed to be 0 for most operations.</param>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Vector.#ctor(Mono.Simd.Vector4f@)">
            <summary>
            Creates a new vector with the supplied values.
            </summary>
            <remarks/>
            <param name="v">Initializes the vector from a <see cref="T:Mono.Simd.Vector4f"/>. W should be 0 and is assumed to be 0 for most operations.</param>
        </member>
        <member name="M:Sansar.Vector.op_Implicit(Sansar.Vector@)~Mono.Simd.Vector4f">
            <summary>
            Converts a vector to a Mono.Simd.Vector4f
            </summary>
            <param name="v">The vector to convert</param>
            <returns/>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.op_Implicit(Mono.Simd.Vector4f@)~Sansar.Vector">
            <summary>
            Converts a Mono.Simd.Vector4f to a vector.
            </summary>
            <param name="v">The vector to convert</param>
            <returns/>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.Dot3(Sansar.Vector@)">
            <summary>
            Performs a 3-component scalar or dot product.
            </summary>
            <param name="b">The second vector.</param>
            <returns>Returns X*b.X+Y*b.Y+Z*b.Z</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.Dot4(Sansar.Vector@)">
            <summary>
            Performs a 4-component scalar or dot product.
            </summary>
            <param name="b">The second vector.</param>
            <returns>Returns X*b.X+Y*b.Y+Z*b.Z+W*b.W</returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.Normalized">
            <summary>
            Returns a vector with the same orientation and unit length.
            </summary>
            <returns>A vector with the same orientation and unit length.</returns>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Vector.Length">
            <summary>
            Calculates the magnitude of this vector.
            </summary>
            <returns>The 3D vector length.</returns>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Vector.LengthSquared">
            <summary>
            Calculates the square magnitude of this vector.
            </summary>
            <returns>The square of the 3D vector length.</returns>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Vector.Cross(Sansar.Vector@)">
            <summary>
            Calculates the cross product of 2 3D vectors.
            </summary>
            <param name="b">The second vector.</param>
            <returns>a X b</returns>
            <remarks></remarks>
        </member>
        <member name="M:Sansar.Vector.Rotate(Sansar.Quaternion@)">
            <summary>
            Returns a new vector which is this vector rotated by the given quaternion.
            </summary>
            <param name="q">The quaternion rotation.</param>
            <returns></returns>
            <remarks/>
        </member>
        <member name="M:Sansar.Vector.Lerp(Sansar.Vector@,System.Single)">
            <summary>
            Performs a linear interpolation between two vectors.
            </summary>
            <param name="b">The second vector.</param>
            <param name="amount">Value from [0..1] indicating the weight for the second vector.</param>
            <returns>A vector that is linearly interpolated between the two sources.</returns>
            <remarks>This method performs the linear interpolation as:
            <para>a + (b-a) * amount.</para>
            An amount value of 0 returns vector a, and 1 returns vector b.</remarks>
        </member>
        <member name="M:Sansar.Vector.AngleTo(Sansar.Vector@)">
            <summary>
            Returns the angle in radians between this vector and the given vector.
            </summary>
            <param name="b">The angle to compare.</param>
            <remarks></remarks>
            <returns>The smallest angle in radians between the two vectors.</returns>
        </member>
        <member name="M:Sansar.Vector.Orthonormalize(Sansar.Vector@,Sansar.Vector@)">
            <summary>
            Normalizes the two vectors and returns the normalized cross product.
            </summary>
            <param name="forward">The facing ve</param>
            <param name="up"></param>
            <returns></returns>
        </member>
        <member name="T:Sansar.Microthreading.Microthread">
            <summary>
            Internal class used to manage scheduling of tasks
            </summary>
        </member>
        <member name="M:Sansar.Microthreading.Microthread.InitMicrothread(Sansar.Microthreading.Script)">
            <summary>
            Initialize data.
            </summary>
        </member>
        <member name="T:Sansar.Microthreading.Microthread.State">
            <summary>
            Represents possible states for a Microthread
            </summary>
        </member>
        <member name="F:Sansar.Microthreading.Microthread.State.Running">
            <summary>
            The thread is in a running state.
            </summary>
        </member>
        <member name="F:Sansar.Microthreading.Microthread.State.Idle">
            <summary>
            The thread id active but waiting on events.
            </summary>
        </member>
        <member name="F:Sansar.Microthreading.Microthread.State.ThreadError">
            <summary>
            An unhandled exception or other non-recoverable error has occurred and the thread will not be run.
            </summary>
        </member>
        <member name="M:Sansar.Microthreading.Microthread.ThreadIsRunning">
            <summary>
            Returns true if the thread is in a running state.
            </summary>
            <returns>true if the current state is <see cref="F:Sansar.Microthreading.Microthread.State.Running"/>.</returns>
        </member>
        <member name="F:Sansar.Microthreading.Microthread.ThreadState">
            <summary>
            The current state of this thread.
            </summary>
        </member>
        <member name="M:Sansar.Microthreading.Microthread.RunThreaded(System.Action,System.TimeSpan)">
            <summary>
            Runs the given Action in this thread.
            </summary>
            <param name="run_action">The action to run.</param>
            <param name="runTime">The amount of time to run the thread.</param>
        </member>
        <member name="M:Sansar.Microthreading.Microthread.Yield">
            <summary>
            Explicitly yield the current thread or coroutine.
            </summary>
        </member>
        <member name="M:Sansar.Microthreading.Microthread.GetCurrentThreadTicks">
            <summary>
            Returns the time when this thread will yield.
            </summary>
            <returns>The timestamp as returned by <see cref="M:System.Diagnostics.Stopwatch.GetTimestamp"/> when this thread will yield.</returns>
        </member>
        <member name="M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded">
            <summary>
            Conditionally yields if the thread has exceeded its given runtime.
            </summary>
        </member>
        <member name="M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded(System.Int64)">
            <summary>
            Conditionally yields if the thread has exceeded its given runtime.
            </summary>
        </member>
        <member name="M:Sansar.Microthreading.Microthread.YieldCurrentThread">
            <summary>
            Explicitly yield the current thread or coroutine.
            </summary>
            <returns>Returns the current thread ticks as returned by <see cref="M:Sansar.Microthreading.Microthread.GetCurrentThreadTicks"/>.</returns>
        </member>
        <member name="M:Sansar.Microthreading.Microthread.Dispose">
            <summary>
            Disposes resources used by this microthread.
            </summary>
        </member>
        <member name="T:Sansar.Microthreading.ApiErrorMode">
            <summary>
            The error mode of the script. 
            </summary>
            <seealso cref="M:Sansar.Script.ScriptBase.SetRelaxedErrorMode"/>
            <seealso cref="M:Sansar.Script.ScriptBase.SetStrictErrorMode"/>
            <seealso cref="M:Sansar.Script.ScriptBase.SetDefaultErrorMode"/>
        </member>
        <member name="F:Sansar.Microthreading.ApiErrorMode.Default">
            <summary>
            Write errors to the Script Debug Console instead of throwing exceptions for some common API errors while still throwing exceptions for the more serious errors.
            </summary>
            <remarks>This only affects the Sansar API methods, other libraries or code errors may still throw exceptions.</remarks>
        </member>
        <member name="F:Sansar.Microthreading.ApiErrorMode.Relaxed">
            <summary>
            Write errors to the Script Debug Console instead of throwing exceptions for almost all Sansar API errors.
            </summary>
            <remarks>This only affects the Sansar API methods, other libraries or code errors may still throw exceptions.</remarks>
        </member>
        <member name="F:Sansar.Microthreading.ApiErrorMode.Strict">
            <summary>
            Throw exceptions for almost all Sansar API errors.
            </summary>
            <remarks>This only affects the Sansar API methods, other libraries or code errors may still throw exceptions.</remarks>
        </member>
        <member name="T:Sansar.Utility.GenericEnumerable`1">
            <summary>
            The GenericEnumerable takes a delegate which accesses items in a Array-like collection by index and allows iteration through enumeration.
            </summary>
            <typeparam name="T">The type of objects returned by <see cref="T:Sansar.Utility.GenericEnumerable`1.GetItem"/></typeparam>
            <remarks>This is used by some API methods to allow for enumerations on some colletions.</remarks>
        </member>
        <member name="T:Sansar.Utility.GenericEnumerable`1.GetItem">
            <summary>
            Delegate which returns an item by index.
            </summary>
            <param name="index">The index of the item to retrieve. Items in the range [0..<see cref="P:Sansar.Utility.GenericEnumerable`1.Count"/>) exist, though they may be null.</param>
            <returns>The item or null.</returns>
            <remarks>Indexing outside the bounds of the collection may return null or thow an exception.</remarks>
        </member>
        <member name="M:Sansar.Utility.GenericEnumerable`1.#ctor(System.UInt32,Sansar.Utility.GenericEnumerable{`0}.GetItem)">
            <summary>
            Initializes the enumerable with an item count and an accessor delegate.
            </summary>
            <param name="count">Total number of items in the collection. </param>
            <param name="getItem">Delegate which retrieves an item from a collection by index.</param>
            <remarks>If the item count changes or items change index the enumeration may be invalidated.</remarks>
        </member>
        <member name="P:Sansar.Utility.GenericEnumerable`1.Count">
            <summary>
            The total number of items in the collection.
            </summary>
            <value>The total number of items in the collection.</value>
            <remarks>Set by the constructor.</remarks>
        </member>
        <member name="M:Sansar.Utility.GenericEnumerable`1.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>An enumerator that can be used to iterate through the collection.</returns>
            <remarks>If the item count changes or items change index the enumeration may be invalidated.</remarks>
        </member>
        <member name="M:Sansar.Utility.GenericEnumerable`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>An enumerator that can be used to iterate through the collection.</returns>
            <remarks>If the item count changes or items change index the enumeration may be invalidated.</remarks>
        </member>
        <member name="T:Sansar.Utility.JsonSerializerOptions">
            <summary>
            Options which control the serialization and deserialization process
            </summary>
            <remarks/>
        </member>
        <member name="P:Sansar.Utility.JsonSerializerOptions.SerializeReferences">
            <summary>
            Controls how references are serialized.
            </summary>
            <value>When true references are maintained instead of serializing the objects directly.</value>
            <remarks>Referential loops with this setting false will throw an exception.</remarks>
        </member>
        <member name="T:Sansar.Utility.JsonSerializationData">
            <summary>
            The result of a serialization request.
            </summary>
            <remarks/>
        </member>
        <member name="P:Sansar.Utility.JsonSerializationData.JsonString">
            <summary>
            The Json string.
            </summary>
            <remarks/>
        </member>
        <member name="P:Sansar.Utility.JsonSerializationData.Object">
            <summary>
            The object.
            </summary>
            <remarks/>
        </member>
        <member name="T:Sansar.Utility.JsonSerializationData`1">
            <summary>
            The result of a serialization request.
            </summary>
            <remarks/>
        </member>
        <member name="P:Sansar.Utility.JsonSerializationData`1.Object">
            <summary>
            The object.
            </summary>
            <remarks/>
        </member>
        <member name="T:Sansar.Utility.JsonSerializer">
            <summary>
            Converts objects to and from javascript object notation strings
            </summary>
            <remarks/>
        </member>
        <member name="M:Sansar.Utility.JsonSerializer.Serialize``1(``0,System.Action{Sansar.Utility.JsonSerializationData{``0}})">
            <summary>
            Converts the given object to a Json string
            </summary>
            <typeparam name="T">The type of the object to convert. This can usually be inferred.</typeparam>
            <param name="value">The object to be converted</param>
            <param name="done">The action to call when the conversion is complete.</param>
            <remarks/>
        </member>
        <member name="M:Sansar.Utility.JsonSerializer.Serialize``1(``0,Sansar.Utility.JsonSerializerOptions,System.Action{Sansar.Utility.JsonSerializationData{``0}})">
            <summary>
            Converts the given object to a Json string
            </summary>
            <typeparam name="T">The type of the object to convert. This can usually be inferred.</typeparam>
            <param name="value">The object to be converted</param>
            <param name="options">Options to control the serialization.</param>
            <param name="done">The action to call when the conversion is complete.</param>
            <remarks/>
        </member>
        <member name="M:Sansar.Utility.JsonSerializer.Deserialize``1(System.String,System.Action{Sansar.Utility.JsonSerializationData{``0}})">
            <summary>
            Converts the given string to an object.
            </summary>
            <typeparam name="T">The type to create.</typeparam>
            <param name="value">The string in json notation.</param>
            <param name="done">The action to call when the conversion is complete.</param>
            <remarks/>
        </member>
        <member name="M:Sansar.Utility.JsonSerializer.Deserialize``1(System.String,Sansar.Utility.JsonSerializerOptions,System.Action{Sansar.Utility.JsonSerializationData{``0}})">
            <summary>
            Converts the given string to an object.
            </summary>
            <typeparam name="T">The type to create.</typeparam>
            <param name="value">The string in json notation.</param>
            <param name="options">Options to control the deserialization.</param>
            <param name="done">The action to call when the conversion is complete.</param>
            <remarks/>
        </member>
        <member name="T:Sansar.Utility.UnmanagedHelpers">
            <summary>
            Helper methods for doing things that are particularly horrible from the c++ side
            </summary>
        </member>
        <member name="T:WaitCanceledException">
            <summary>
            Thrown by WaitFor variants which can fail without returning data.
            </summary>
            <remarks/>
        </member>
        <member name="P:WaitCanceledException.Message">
            <summary>
            String description of the error condition.
            </summary>
            <remarks>Useful for debugging.</remarks>
        </member>
        <member name="P:WaitCanceledException.Reason">
            <summary>
            Integer error code. The value varies depending on the method called.
            </summary>
            <remarks>See the documentation for the Subscribe method for details.</remarks>
        </member>
    </members>
</doc>
