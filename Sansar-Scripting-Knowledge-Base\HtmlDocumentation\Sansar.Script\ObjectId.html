<html>
  <head>
    <title>Sansar.Script.ObjectId</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Script</a> : <a href="index.html">Sansar.Script Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Script.ObjectId">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Script.ObjectId:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Script.ObjectId:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Script.ObjectId:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Script.ObjectId">ObjectId  Struct</h1>
    <p class="Summary" id="T:Sansar.Script.ObjectId:Summary">
            Encapsulates an Object Id.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Script.ObjectId:Signature">public struct  <b>ObjectId</b></div>
    </div>
    <div class="Remarks" id="T:Sansar.Script.ObjectId:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Script.ObjectId:Docs:Remarks">
            An ObjectId can be derived from a <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a>.
            </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Script.ObjectId:Docs:Version Information">
        <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.ValueType">ValueType</a>.
							</p>
        <h2 class="Section">Public Fields</h2>
        <div class="SectionBox" id="Public Fields">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Script.ObjectId.Invalid">Invalid</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/ObjectId.html">ObjectId</a>
                  </i>. 
            The invalid id object.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ObjectId.Equals(System.Object)">Equals</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a></nobr><blockquote>
            Value comparison for ObjectId.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ObjectId.GetHashCode()">GetHashCode</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a></nobr><blockquote>
            Retrieves the hash code for this instance.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.ObjectId.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>
            Converts the id to a hexadecimal string representation. 
            </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Operators</h2>
        <div class="SectionBox" id="Public Operators">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td>
                  <b>
                    <a href="#M:Sansar.Script.ObjectId.op_Equality(Sansar.Script.ObjectId,Sansar.Script.ObjectId)">Equality</a>
                  </b>(<a href="../Sansar.Script/ObjectId.html">ObjectId</a>, <a href="../Sansar.Script/ObjectId.html">ObjectId</a>)</td>
                <td>
            ObjectId equality operator.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td>
                  <b>
                    <a href="#M:Sansar.Script.ObjectId.op_Inequality(Sansar.Script.ObjectId,Sansar.Script.ObjectId)">Inequality</a>
                  </b>(<a href="../Sansar.Script/ObjectId.html">ObjectId</a>, <a href="../Sansar.Script/ObjectId.html">ObjectId</a>)</td>
                <td>
            ObjectId inequality operator.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td>
                  <b>
                    <a href="#M:Sansar.Script.ObjectId.op_Implicit(System.UInt32)~Sansar.Script.ObjectId">Conversion to Sansar.Script.ObjectId</a>
                  </b>(Implicit)</td>
                <td>
            Internal explicit conversion from a uint.
            </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Script.ObjectId:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="M:Sansar.Script.ObjectId.Equals(System.Object)">Equals Method</h3>
        <blockquote id="M:Sansar.Script.ObjectId.Equals(System.Object):member">
          <div class="msummary">
            Value comparison for ObjectId.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>Equals</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a> obj)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ObjectId.Equals(System.Object):Parameters">
            <dl>
              <dt>
                <i>obj</i>
              </dt>
              <dd>The object to compare.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ObjectId.Equals(System.Object):Returns">true if the argument is an ObjectId and has the same value.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ObjectId.Equals(System.Object):Remarks">This method overrides <a href="http://www.go-mono.com/docs/monodoc.ashx?link=M:System.ValueType.Equals">ValueType.Equals</a>.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ObjectId.Equals(System.Object):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ObjectId.GetHashCode()">GetHashCode Method</h3>
        <blockquote id="M:Sansar.Script.ObjectId.GetHashCode():member">
          <div class="msummary">
            Retrieves the hash code for this instance.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>GetHashCode</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ObjectId.GetHashCode():Returns">The hash code for this instance.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ObjectId.GetHashCode():Remarks">This method overrides <a href="http://www.go-mono.com/docs/monodoc.ashx?link=M:System.ValueType.GetHashCode">ValueType.GetHashCode</a>.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ObjectId.GetHashCode():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Script.ObjectId.Invalid">Invalid Field</h3>
        <blockquote id="F:Sansar.Script.ObjectId.Invalid:member">
          <div class="msummary">
            The invalid id object.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="../Sansar.Script/ObjectId.html">ObjectId</a> <b>Invalid</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Script.ObjectId.Invalid:Remarks">This value is used by certain APIs to represent an invalid ObjectId.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Script.ObjectId.Invalid:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ObjectId.op_Equality(Sansar.Script.ObjectId,Sansar.Script.ObjectId)">op_Equality Method</h3>
        <blockquote id="M:Sansar.Script.ObjectId.op_Equality(Sansar.Script.ObjectId,Sansar.Script.ObjectId):member">
          <div class="msummary">
            ObjectId equality operator.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> operator== (<a href="../Sansar.Script/ObjectId.html">ObjectId</a> a, <a href="../Sansar.Script/ObjectId.html">ObjectId</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ObjectId.op_Equality(Sansar.Script.ObjectId,Sansar.Script.ObjectId):Parameters">
            <dl>
              <dt>
                <i>a</i>
              </dt>
              <dd>First ObjectId to compare.</dd>
              <dt>
                <i>b</i>
              </dt>
              <dd>Second ObjectId to compare.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ObjectId.op_Equality(Sansar.Script.ObjectId,Sansar.Script.ObjectId):Returns">true if the ObjectIds have the same value.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ObjectId.op_Equality(Sansar.Script.ObjectId,Sansar.Script.ObjectId):Remarks">This is a value comparison.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ObjectId.op_Equality(Sansar.Script.ObjectId,Sansar.Script.ObjectId):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ObjectId.op_Implicit(System.UInt32)~Sansar.Script.ObjectId">Conversion Method</h3>
        <blockquote id="M:Sansar.Script.ObjectId.op_Implicit(System.UInt32)~Sansar.Script.ObjectId:member">
          <div class="msummary">
            Internal explicit conversion from a uint.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static implicit operator <a href="../Sansar.Script/ObjectId.html">ObjectId</a> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a> id)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ObjectId.op_Implicit(System.UInt32)~Sansar.Script.ObjectId:Parameters">
            <dl>
              <dt>
                <i>id</i>
              </dt>
              <dd>A uint representation of an ObjectId</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ObjectId.op_Implicit(System.UInt32)~Sansar.Script.ObjectId:Returns">A new ObjectId initialized with the given uint.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ObjectId.op_Implicit(System.UInt32)~Sansar.Script.ObjectId:Remarks">Internal.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ObjectId.op_Implicit(System.UInt32)~Sansar.Script.ObjectId:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ObjectId.op_Inequality(Sansar.Script.ObjectId,Sansar.Script.ObjectId)">op_Inequality Method</h3>
        <blockquote id="M:Sansar.Script.ObjectId.op_Inequality(Sansar.Script.ObjectId,Sansar.Script.ObjectId):member">
          <div class="msummary">
            ObjectId inequality operator.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> operator!= (<a href="../Sansar.Script/ObjectId.html">ObjectId</a> a, <a href="../Sansar.Script/ObjectId.html">ObjectId</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ObjectId.op_Inequality(Sansar.Script.ObjectId,Sansar.Script.ObjectId):Parameters">
            <dl>
              <dt>
                <i>a</i>
              </dt>
              <dd>First ObjectId to compare.</dd>
              <dt>
                <i>b</i>
              </dt>
              <dd>Second ObjectId to compare.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ObjectId.op_Inequality(Sansar.Script.ObjectId,Sansar.Script.ObjectId):Returns">true if the ObjectIds have a different value.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ObjectId.op_Inequality(Sansar.Script.ObjectId,Sansar.Script.ObjectId):Remarks">This is a value comparison.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ObjectId.op_Inequality(Sansar.Script.ObjectId,Sansar.Script.ObjectId):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.ObjectId.ToString()">ToString Method</h3>
        <blockquote id="M:Sansar.Script.ObjectId.ToString():member">
          <div class="msummary">
            Converts the id to a hexadecimal string representation. 
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.ObjectId.ToString():Returns">The hexadecimal string representation.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.ObjectId.ToString():Remarks">This method overrides <a href="http://www.go-mono.com/docs/monodoc.ashx?link=M:System.Object.ToString">object.ToString</a>.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.ObjectId.ToString():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>