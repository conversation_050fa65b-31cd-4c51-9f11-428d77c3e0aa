<html>
  <head>
    <title>Sansar.Simulation.ObjectiveDefinition</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.ObjectiveDefinition">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.ObjectiveDefinition:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.ObjectiveDefinition:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.ObjectiveDefinition:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.ObjectiveDefinition">ObjectiveDefinition  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.ObjectiveDefinition:Summary">The ObjectiveDefinition stores the data that is used to create quest objective instances for users.</p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.ObjectiveDefinition:Signature">[Sansar.Script.Interface]<br />public class  <b>ObjectiveDefinition</b> : <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.ObjectiveDefinition:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.ObjectiveDefinition:Docs:Remarks">
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.ObjectiveDefinition:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.
							</p>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ObjectiveDefinition.Description">Description</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. The objective description.</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ObjectiveDefinition.InitialState">InitialState</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/ObjectiveState.html">ObjectiveState</a>
                  </i>. The initial state that this objective will have when a user starts the quest</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/InstanceInterface.html#P:Sansar.Script.InstanceInterface.IsValid">IsValid</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            Whether or not this interface is valid.
             (<i>Inherited from <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ObjectiveDefinition.Ready">Ready</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. If the QuestDefinition data is ready.</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ObjectiveDefinition.RequiredCount">RequiredCount</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>
                  </i>. The required count to complete the objective.</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ObjectiveDefinition.Title">Title</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. The objective title.</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Script.SessionId)">GetObjective</a>
                  </b>(<a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a>)<blockquote>Get the state of this objective for a particular user.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Simulation.AgentPrivate)">GetObjective</a>
                  </b>(<a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a>)<blockquote>Get the state of this objective for a particular user.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ObjectiveDefinition.GetObjectiveData})">GetObjective</a>
                  </b>(<a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ObjectiveDefinition.GetObjectiveData&gt;</a>)<blockquote>Get the state of this objective for a particular user.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Simulation.AgentPrivate,System.Action{Sansar.Simulation.ObjectiveDefinition.GetObjectiveData})">GetObjective</a>
                  </b>(<a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ObjectiveDefinition.GetObjectiveData&gt;</a>)<blockquote>Get the state of this objective for a particular user.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ObjectiveDefinition.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote> A string representation of this object. </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ObjectiveDefinition.Update()">Update</a>
                  </b>()<blockquote>Update the ObjectiveDefinition data.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ObjectiveDefinition.Update(System.Action{Sansar.Script.OperationCompleteEvent})">Update</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Update the ObjectiveDefinition data.</blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.ObjectiveDefinition:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="P:Sansar.Simulation.ObjectiveDefinition.Description">Description Property</h3>
        <blockquote id="P:Sansar.Simulation.ObjectiveDefinition.Description:member">
          <div class="msummary">The objective description.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>Description</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.Description:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.Description:Remarks">Returns null if Ready is false because the objective definition is not ready.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.Description:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Script.SessionId)">GetObjective Method</h3>
        <blockquote id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Script.SessionId):member">
          <div class="msummary">Get the state of this objective for a particular user.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>GetObjective</b> (<a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> agent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Script.SessionId):Parameters">
            <dl>
              <dt>
                <i>agent</i>
              </dt>
              <dd>The <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> of the user.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Script.SessionId):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Script.SessionId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Simulation.AgentPrivate)">GetObjective Method</h3>
        <blockquote id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Simulation.AgentPrivate):member">
          <div class="msummary">Get the state of this objective for a particular user.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>GetObjective</b> (<a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a> agent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Simulation.AgentPrivate):Parameters">
            <dl>
              <dt>
                <i>agent</i>
              </dt>
              <dd>Obtain the objective state for this user.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Simulation.AgentPrivate):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Simulation.AgentPrivate):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ObjectiveDefinition.GetObjectiveData})">GetObjective Method</h3>
        <blockquote id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ObjectiveDefinition.GetObjectiveData}):member">
          <div class="msummary">Get the state of this objective for a particular user.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>GetObjective</b> (<a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> agent, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ObjectiveDefinition.GetObjectiveData&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ObjectiveDefinition.GetObjectiveData}):Parameters">
            <dl>
              <dt>
                <i>agent</i>
              </dt>
              <dd>The <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> of the user.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ObjectiveDefinition.GetObjectiveData}):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ObjectiveDefinition.GetObjectiveData}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Simulation.AgentPrivate,System.Action{Sansar.Simulation.ObjectiveDefinition.GetObjectiveData})">GetObjective Method</h3>
        <blockquote id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Simulation.AgentPrivate,System.Action{Sansar.Simulation.ObjectiveDefinition.GetObjectiveData}):member">
          <div class="msummary">Get the state of this objective for a particular user.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>GetObjective</b> (<a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a> agent, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ObjectiveDefinition.GetObjectiveData&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Simulation.AgentPrivate,System.Action{Sansar.Simulation.ObjectiveDefinition.GetObjectiveData}):Parameters">
            <dl>
              <dt>
                <i>agent</i>
              </dt>
              <dd>Obtain the objective state for this user.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Simulation.AgentPrivate,System.Action{Sansar.Simulation.ObjectiveDefinition.GetObjectiveData}):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.GetObjective(Sansar.Simulation.AgentPrivate,System.Action{Sansar.Simulation.ObjectiveDefinition.GetObjectiveData}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ObjectiveDefinition.InitialState">InitialState Property</h3>
        <blockquote id="P:Sansar.Simulation.ObjectiveDefinition.InitialState:member">
          <div class="msummary">The initial state that this objective will have when a user starts the quest</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/ObjectiveState.html">ObjectiveState</a> <b>InitialState</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.InitialState:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.InitialState:Remarks">Returns None if Ready is false, because the objective definition is not ready.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.InitialState:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ObjectiveDefinition.Ready">Ready Property</h3>
        <blockquote id="P:Sansar.Simulation.ObjectiveDefinition.Ready:member">
          <div class="msummary">If the QuestDefinition data is ready.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>Ready</b>  { get; }</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.Ready:See Also">
            <div>
              <a href="../Sansar.Simulation/QuestDefinition.html#M:Sansar.Simulation.QuestDefinition.Update(System.Action{Sansar.Script.OperationCompleteEvent})">QuestDefinition.Update(Action&lt;Sansar.Script.OperationCompleteEvent&gt;)</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.Ready:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.Ready:Remarks">Returns false until the data has been fetched from the quest service. Once it returns true it will always return true.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.Ready:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ObjectiveDefinition.RequiredCount">RequiredCount Property</h3>
        <blockquote id="P:Sansar.Simulation.ObjectiveDefinition.RequiredCount:member">
          <div class="msummary">The required count to complete the objective.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>RequiredCount</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.RequiredCount:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.RequiredCount:Remarks">Returns -1 if Ready is false because the objective definition is not ready.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.RequiredCount:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ObjectiveDefinition.Title">Title Property</h3>
        <blockquote id="P:Sansar.Simulation.ObjectiveDefinition.Title:member">
          <div class="msummary">The objective title.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>Title</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.Title:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.Title:Remarks">Returns null if Ready is false because the objective definition is not ready.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ObjectiveDefinition.Title:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ObjectiveDefinition.ToString()">ToString Method</h3>
        <blockquote id="M:Sansar.Simulation.ObjectiveDefinition.ToString():member">
          <div class="msummary"> A string representation of this object. </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.ToString():Returns">A string representation of this object.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.ToString():Remarks">The format of this string may change between releases.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.ToString():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ObjectiveDefinition.Update()">Update Method</h3>
        <blockquote id="M:Sansar.Simulation.ObjectiveDefinition.Update():member">
          <div class="msummary">Update the ObjectiveDefinition data.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Update</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.Update():Remarks">Use in a WaitFor to wait for the data for the QuestDefinition to be ready. Once data is retrieved and Ready is true it will never again be false.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.Update():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ObjectiveDefinition.Update(System.Action{Sansar.Script.OperationCompleteEvent})">Update Method</h3>
        <blockquote id="M:Sansar.Simulation.ObjectiveDefinition.Update(System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Update the ObjectiveDefinition data.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Update</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.Update(System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.Update(System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">Use in a WaitFor to wait for the data for the QuestDefinition to be ready. Once data is retrieved and Ready is true it will never again be false.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ObjectiveDefinition.Update(System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>