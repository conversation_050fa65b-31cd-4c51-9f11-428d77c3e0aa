<html>
  <head>
    <title>Sansar.Mathf</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Script</a> : <a href="index.html">Sansar Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Mathf">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Mathf:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Mathf:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Mathf:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Mathf">Mathf  Class</h1>
    <p class="Summary" id="T:Sansar.Mathf:Summary">
            Floating point math constants and utilities.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Mathf:Signature">public static class  <b>Mathf</b></div>
    </div>
    <div class="Remarks" id="T:Sansar.Mathf:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Mathf:Docs:Remarks">Provides common floating point constants static methods.</div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Mathf:Docs:Version Information">
        <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>.
							</p>
        <h2 class="Section">Public Fields</h2>
        <div class="SectionBox" id="Public Fields">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>const </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Mathf.DegreesPerRadian">DegreesPerRadian</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i> (57.29578). 
            Conversion factor to convert radians to degrees.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>const </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Mathf.E">E</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i> (2.718282). 
            E
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>const </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Mathf.PI">PI</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i> (3.141593). 
            Pi
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>const </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Mathf.PiOverTwo">PiOverTwo</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i> (1.570796). 
            Pi divided by two.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>const </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Mathf.RadiansPerDegree">RadiansPerDegree</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i> (0.01745329). 
            Conversion factor to convert degrees to radians.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>const </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Mathf.RootTwo">RootTwo</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i> (1.414214). 
            The square root of two.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>const </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Mathf.RootTwoOverTwo">RootTwoOverTwo</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i> (0.7071068). 
            The square root of 2 divided by two.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>const </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Mathf.TwoOverPi">TwoOverPi</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i> (0.6366197). 
            Two divided by pi.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>const </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Mathf.TwoPi">TwoPi</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i> (6.283185). 
            Two times pi.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Mathf.Clamp(System.Single,System.Single,System.Single)">Clamp</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>
            Clamps a float to the specified range
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Mathf.IsApproximatelyEqualAbsolute(System.Single,System.Single)">IsApproximatelyEqualAbsolute</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a></nobr><blockquote>
            Compares the two given floats and returns true if they are approximately equal
            </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Mathf:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="M:Sansar.Mathf.Clamp(System.Single,System.Single,System.Single)">Clamp Method</h3>
        <blockquote id="M:Sansar.Mathf.Clamp(System.Single,System.Single,System.Single):member">
          <div class="msummary">
            Clamps a float to the specified range
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>Clamp</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> x, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> min, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> max)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Mathf.Clamp(System.Single,System.Single,System.Single):Parameters">
            <dl>
              <dt>
                <i>x</i>
              </dt>
              <dd>The float to compare.</dd>
              <dt>
                <i>min</i>
              </dt>
              <dd>The minimum of the clamp range.</dd>
              <dt>
                <i>max</i>
              </dt>
              <dd>The maximum of the clamp range.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Mathf.Clamp(System.Single,System.Single,System.Single):Returns">x if x is within [min,max] otherwise min or max, whichever is closer to the value of x.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Mathf.Clamp(System.Single,System.Single,System.Single):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Mathf.Clamp(System.Single,System.Single,System.Single):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Mathf.DegreesPerRadian">DegreesPerRadian Field</h3>
        <blockquote id="F:Sansar.Mathf.DegreesPerRadian:member">
          <div class="msummary">
            Conversion factor to convert radians to degrees.
            </div>
          <p>
            <b>Value: </b>57.29578</p>
          <h2>Syntax</h2>
          <div class="Signature">public const <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>DegreesPerRadian</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.DegreesPerRadian:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.DegreesPerRadian:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Mathf.E">E Field</h3>
        <blockquote id="F:Sansar.Mathf.E:member">
          <div class="msummary">
            E
            </div>
          <p>
            <b>Value: </b>2.718282</p>
          <h2>Syntax</h2>
          <div class="Signature">public const <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>E</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.E:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.E:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Mathf.IsApproximatelyEqualAbsolute(System.Single,System.Single)">IsApproximatelyEqualAbsolute Method</h3>
        <blockquote id="M:Sansar.Mathf.IsApproximatelyEqualAbsolute(System.Single,System.Single):member">
          <div class="msummary">
            Compares the two given floats and returns true if they are approximately equal
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>IsApproximatelyEqualAbsolute</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> a, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Mathf.IsApproximatelyEqualAbsolute(System.Single,System.Single):Parameters">
            <dl>
              <dt>
                <i>a</i>
              </dt>
              <dd>The first float to compare.</dd>
              <dt>
                <i>b</i>
              </dt>
              <dd>The second float to compare.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Mathf.IsApproximatelyEqualAbsolute(System.Single,System.Single):Returns">true if the float are approximately equal, false otherwise.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Mathf.IsApproximatelyEqualAbsolute(System.Single,System.Single):Remarks">Checks if the absolute value of the difference of the values is small.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Mathf.IsApproximatelyEqualAbsolute(System.Single,System.Single):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Mathf.PI">PI Field</h3>
        <blockquote id="F:Sansar.Mathf.PI:member">
          <div class="msummary">
            Pi
            </div>
          <p>
            <b>Value: </b>3.141593</p>
          <h2>Syntax</h2>
          <div class="Signature">public const <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>PI</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.PI:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.PI:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Mathf.PiOverTwo">PiOverTwo Field</h3>
        <blockquote id="F:Sansar.Mathf.PiOverTwo:member">
          <div class="msummary">
            Pi divided by two.
            </div>
          <p>
            <b>Value: </b>1.570796</p>
          <h2>Syntax</h2>
          <div class="Signature">public const <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>PiOverTwo</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.PiOverTwo:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.PiOverTwo:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Mathf.RadiansPerDegree">RadiansPerDegree Field</h3>
        <blockquote id="F:Sansar.Mathf.RadiansPerDegree:member">
          <div class="msummary">
            Conversion factor to convert degrees to radians.
            </div>
          <p>
            <b>Value: </b>0.01745329</p>
          <h2>Syntax</h2>
          <div class="Signature">public const <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>RadiansPerDegree</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.RadiansPerDegree:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.RadiansPerDegree:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Mathf.RootTwo">RootTwo Field</h3>
        <blockquote id="F:Sansar.Mathf.RootTwo:member">
          <div class="msummary">
            The square root of two.
            </div>
          <p>
            <b>Value: </b>1.414214</p>
          <h2>Syntax</h2>
          <div class="Signature">public const <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>RootTwo</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.RootTwo:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.RootTwo:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Mathf.RootTwoOverTwo">RootTwoOverTwo Field</h3>
        <blockquote id="F:Sansar.Mathf.RootTwoOverTwo:member">
          <div class="msummary">
            The square root of 2 divided by two.
            </div>
          <p>
            <b>Value: </b>0.7071068</p>
          <h2>Syntax</h2>
          <div class="Signature">public const <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>RootTwoOverTwo</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.RootTwoOverTwo:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.RootTwoOverTwo:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Mathf.TwoOverPi">TwoOverPi Field</h3>
        <blockquote id="F:Sansar.Mathf.TwoOverPi:member">
          <div class="msummary">
            Two divided by pi.
            </div>
          <p>
            <b>Value: </b>0.6366197</p>
          <h2>Syntax</h2>
          <div class="Signature">public const <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>TwoOverPi</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.TwoOverPi:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.TwoOverPi:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Mathf.TwoPi">TwoPi Field</h3>
        <blockquote id="F:Sansar.Mathf.TwoPi:member">
          <div class="msummary">
            Two times pi.
            </div>
          <p>
            <b>Value: </b>6.283185</p>
          <h2>Syntax</h2>
          <div class="Signature">public const <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>TwoPi</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.TwoPi:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Mathf.TwoPi:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>