<html>
  <head>
    <title>Sansar.Simulation.Chat</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.Chat">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.Chat:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.Chat:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.Chat:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.Chat">Chat  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.Chat:Summary">The Chat class handles chat subscriptions and sending messages to users and other scripts.</p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.Chat:Signature">[Sansar.Script.Interface]<br />public class  <b>Chat</b> : <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.Chat:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.Chat:Docs:Remarks">
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.Chat:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.
							</p>
        <h2 class="Section">Public Fields</h2>
        <div class="SectionBox" id="Public Fields">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.Chat.AllChannels">AllChannels</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>
                  </i>. Use as the channel argument for <a href="../Sansar.Simulation/Chat.html#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Chat.Subscribe(int, string, Action&lt;Sansar.Simulation.ChatData&gt;,System.Boolean)</a> to subscribe to all channels.</td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.Chat.DefaultChannel">DefaultChannel</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>
                  </i>. Use as the channel argument for <a href="../Sansar.Simulation/Chat.html#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Chat.Subscribe(int, string, Action&lt;Sansar.Simulation.ChatData&gt;,System.Boolean)</a> to subscribe to the default channel</td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.Chat.Script">Script</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. Indicates that the chat message originated from a script.</td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.Chat.Server">Server</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. Indicates that the chat message originated from the server.</td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.Chat.User">User</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. Indicates that the chat message originated from a user.</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/InstanceInterface.html#P:Sansar.Script.InstanceInterface.IsValid">IsValid</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            Whether or not this interface is valid.
             (<i>Inherited from <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.</i>)</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.MessageAllUsers(System.String)">MessageAllUsers</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<blockquote>Send a chat message to every user in the Experience.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.MessageAllUsers(System.String,System.Action{Sansar.Script.OperationCompleteEvent})">MessageAllUsers</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Send a chat message to every user in the Experience.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.MessageScript(System.String,Sansar.Script.ScriptId,System.Int32)">MessageScript</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<blockquote>Send a string message to a specific script.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe</a>
                  </b>(<a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe</a>
                  </b>(<a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Simulation/Chat+SubscriptionHandler.html">Chat.SubscriptionHandler</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a>, <a href="../Sansar.Simulation/Chat+SubscriptionHandler.html">Chat.SubscriptionHandler</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a>, <a href="../Sansar.Simulation/Chat+SubscriptionHandler.html">Chat.SubscriptionHandler</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a>, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a>, <a href="../Sansar.Simulation/Chat+SubscriptionHandler.html">Chat.SubscriptionHandler</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a>, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Chat Events.</blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.Chat:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="F:Sansar.Simulation.Chat.AllChannels">AllChannels Field</h3>
        <blockquote id="F:Sansar.Simulation.Chat.AllChannels:member">
          <div class="msummary">Use as the channel argument for <a href="../Sansar.Simulation/Chat.html#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Chat.Subscribe(int, string, Action&lt;Sansar.Simulation.ChatData&gt;,System.Boolean)</a> to subscribe to all channels.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("All channel subscription no longer allowed", false)]<br />public static readonly <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>AllChannels</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.Chat.AllChannels:Remarks">No longer in use, now the same behavior as DefaultChannel</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.Chat.AllChannels:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.Chat.DefaultChannel">DefaultChannel Field</h3>
        <blockquote id="F:Sansar.Simulation.Chat.DefaultChannel:member">
          <div class="msummary">Use as the channel argument for <a href="../Sansar.Simulation/Chat.html#M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Chat.Subscribe(int, string, Action&lt;Sansar.Simulation.ChatData&gt;,System.Boolean)</a> to subscribe to the default channel</div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>DefaultChannel</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.Chat.DefaultChannel:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.Chat.DefaultChannel:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.MessageAllUsers(System.String)">MessageAllUsers Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.MessageAllUsers(System.String):member">
          <div class="msummary">Send a chat message to every user in the Experience.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>MessageAllUsers</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> message)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.MessageAllUsers(System.String):Parameters">
            <dl>
              <dt>
                <i>message</i>
              </dt>
              <dd>The message to send.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.MessageAllUsers(System.String):Remarks">Messages may be throttled.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.MessageAllUsers(System.String):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.MessageAllUsers(System.String,System.Action{Sansar.Script.OperationCompleteEvent})">MessageAllUsers Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.MessageAllUsers(System.String,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Send a chat message to every user in the Experience.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>MessageAllUsers</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> message, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.MessageAllUsers(System.String,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>message</i>
              </dt>
              <dd>The message to send.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.MessageAllUsers(System.String,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">Messages may be throttled.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.MessageAllUsers(System.String,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.MessageScript(System.String,Sansar.Script.ScriptId,System.Int32)">MessageScript Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.MessageScript(System.String,Sansar.Script.ScriptId,System.Int32):member">
          <div class="msummary">Send a string message to a specific script.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>MessageScript</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> message, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a> scriptId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> channel)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.MessageScript(System.String,Sansar.Script.ScriptId,System.Int32):Parameters">
            <dl>
              <dt>
                <i>message</i>
              </dt>
              <dd>The message to send.</dd>
              <dt>
                <i>scriptId</i>
              </dt>
              <dd>The id of the script to send the message to.</dd>
              <dt>
                <i>channel</i>
              </dt>
              <dd>The channel to send the message on.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.MessageScript(System.String,Sansar.Script.ScriptId,System.Int32):Remarks">Only the script with the matching id will receive the message.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.MessageScript(System.String,Sansar.Script.ScriptId,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.Chat.Script">Script Field</h3>
        <blockquote id="F:Sansar.Simulation.Chat.Script:member">
          <div class="msummary">Indicates that the chat message originated from a script.</div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>Script</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.Chat.Script:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.Chat.Script:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.Chat.Server">Server Field</h3>
        <blockquote id="F:Sansar.Simulation.Chat.Server:member">
          <div class="msummary">Indicates that the chat message originated from the server.</div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>Server</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.Chat.Server:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.Chat.Server:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a> SourceScriptId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ChatData.html">ChatData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>SourceScriptId</i>
              </dt>
              <dd> Sansar.Script.ScriptId id of the chat source. May be <a href="../Sansar.Script/ScriptId.html#F:Sansar.Script.ScriptId.Invalid">Sansar.Script.ScriptId.Invalid</a> if the source is not a script.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> SourceId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ChatData.html">ChatData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>SourceId</i>
              </dt>
              <dd> Sansar.Script.SessionId id of the chat source. May be <a href="../Sansar.Script/SessionId.html#F:Sansar.Script.SessionId.Invalid">Sansar.Script.SessionId.Invalid</a> if the source is not an agent.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.Action{Sansar.Simulation.ChatData},System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> Channel, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.Action{Sansar.Simulation.ChatData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ChatData.html">ChatData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.Action{Sansar.Simulation.ChatData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Channel</i>
              </dt>
              <dd> int channel which the chat occurs on.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.Action{Sansar.Simulation.ChatData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.Action{Sansar.Simulation.ChatData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.Action{Sansar.Simulation.ChatData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> Source, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ChatData.html">ChatData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Source</i>
              </dt>
              <dd> string source of the chat: system, user or script.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> Channel, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a> SourceScriptId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ChatData.html">ChatData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Channel</i>
              </dt>
              <dd> int channel which the chat occurs on.</dd>
              <dt>
                <i>SourceScriptId</i>
              </dt>
              <dd> Sansar.Script.ScriptId id of the chat source. May be <a href="../Sansar.Script/ScriptId.html#F:Sansar.Script.ScriptId.Invalid">Sansar.Script.ScriptId.Invalid</a> if the source is not a script.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> Channel, <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> SourceId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ChatData.html">ChatData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Channel</i>
              </dt>
              <dd> int channel which the chat occurs on.</dd>
              <dt>
                <i>SourceId</i>
              </dt>
              <dd> Sansar.Script.SessionId id of the chat source. May be <a href="../Sansar.Script/SessionId.html#F:Sansar.Script.SessionId.Invalid">Sansar.Script.SessionId.Invalid</a> if the source is not an agent.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Use subscription callbacks of type Action&lt;Sansar.Simulation.ChatData&gt;", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> Channel, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> Source, <a href="../Sansar.Simulation/Chat+SubscriptionHandler.html">Chat.SubscriptionHandler</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Channel</i>
              </dt>
              <dd> int channel which the chat occurs on.</dd>
              <dt>
                <i>Source</i>
              </dt>
              <dd> string source of the chat: system, user or script.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> Channel, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> Source, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ChatData.html">ChatData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Channel</i>
              </dt>
              <dd> int channel which the chat occurs on.</dd>
              <dt>
                <i>Source</i>
              </dt>
              <dd> string source of the chat: system, user or script.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,System.Action{Sansar.Simulation.ChatData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> Source, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a> SourceScriptId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ChatData.html">ChatData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Source</i>
              </dt>
              <dd> string source of the chat: system, user or script.</dd>
              <dt>
                <i>SourceScriptId</i>
              </dt>
              <dd> Sansar.Script.ScriptId id of the chat source. May be <a href="../Sansar.Script/ScriptId.html#F:Sansar.Script.ScriptId.Invalid">Sansar.Script.ScriptId.Invalid</a> if the source is not a script.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> Source, <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> SourceId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ChatData.html">ChatData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Source</i>
              </dt>
              <dd> string source of the chat: system, user or script.</dd>
              <dt>
                <i>SourceId</i>
              </dt>
              <dd> Sansar.Script.SessionId id of the chat source. May be <a href="../Sansar.Script/SessionId.html#F:Sansar.Script.SessionId.Invalid">Sansar.Script.SessionId.Invalid</a> if the source is not an agent.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Use subscription callbacks of type Action&lt;Sansar.Simulation.ChatData&gt;", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> Channel, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> Source, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a> SourceScriptId, <a href="../Sansar.Simulation/Chat+SubscriptionHandler.html">Chat.SubscriptionHandler</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Channel</i>
              </dt>
              <dd> int channel which the chat occurs on.</dd>
              <dt>
                <i>Source</i>
              </dt>
              <dd> string source of the chat: system, user or script.</dd>
              <dt>
                <i>SourceScriptId</i>
              </dt>
              <dd> Sansar.Script.ScriptId id of the chat source. May be <a href="../Sansar.Script/ScriptId.html#F:Sansar.Script.ScriptId.Invalid">Sansar.Script.ScriptId.Invalid</a> if the source is not a script.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> Channel, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> Source, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a> SourceScriptId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ChatData.html">ChatData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Channel</i>
              </dt>
              <dd> int channel which the chat occurs on.</dd>
              <dt>
                <i>Source</i>
              </dt>
              <dd> string source of the chat: system, user or script.</dd>
              <dt>
                <i>SourceScriptId</i>
              </dt>
              <dd> Sansar.Script.ScriptId id of the chat source. May be <a href="../Sansar.Script/ScriptId.html#F:Sansar.Script.ScriptId.Invalid">Sansar.Script.ScriptId.Invalid</a> if the source is not a script.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Use subscription callbacks of type Action&lt;Sansar.Simulation.ChatData&gt;", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> Channel, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> Source, <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> SourceId, <a href="../Sansar.Simulation/Chat+SubscriptionHandler.html">Chat.SubscriptionHandler</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Channel</i>
              </dt>
              <dd> int channel which the chat occurs on.</dd>
              <dt>
                <i>Source</i>
              </dt>
              <dd> string source of the chat: system, user or script.</dd>
              <dt>
                <i>SourceId</i>
              </dt>
              <dd> Sansar.Script.SessionId id of the chat source. May be <a href="../Sansar.Script/SessionId.html#F:Sansar.Script.SessionId.Invalid">Sansar.Script.SessionId.Invalid</a> if the source is not an agent.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> Channel, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> Source, <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> SourceId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ChatData.html">ChatData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Channel</i>
              </dt>
              <dd> int channel which the chat occurs on.</dd>
              <dt>
                <i>Source</i>
              </dt>
              <dd> string source of the chat: system, user or script.</dd>
              <dt>
                <i>SourceId</i>
              </dt>
              <dd> Sansar.Script.SessionId id of the chat source. May be <a href="../Sansar.Script/SessionId.html#F:Sansar.Script.SessionId.Invalid">Sansar.Script.SessionId.Invalid</a> if the source is not an agent.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Use subscription callbacks of type Action&lt;Sansar.Simulation.ChatData&gt;", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> Channel, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> Source, <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> SourceId, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a> SourceScriptId, <a href="../Sansar.Simulation/Chat+SubscriptionHandler.html">Chat.SubscriptionHandler</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Channel</i>
              </dt>
              <dd> int channel which the chat occurs on.</dd>
              <dt>
                <i>Source</i>
              </dt>
              <dd> string source of the chat: system, user or script.</dd>
              <dt>
                <i>SourceId</i>
              </dt>
              <dd> Sansar.Script.SessionId id of the chat source. May be <a href="../Sansar.Script/SessionId.html#F:Sansar.Script.SessionId.Invalid">Sansar.Script.SessionId.Invalid</a> if the source is not an agent.</dd>
              <dt>
                <i>SourceScriptId</i>
              </dt>
              <dd> Sansar.Script.ScriptId id of the chat source. May be <a href="../Sansar.Script/ScriptId.html#F:Sansar.Script.ScriptId.Invalid">Sansar.Script.ScriptId.Invalid</a> if the source is not a script.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,Sansar.Simulation.Chat.SubscriptionHandler,System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean)">Subscribe Method</h3>
        <blockquote id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):member">
          <div class="msummary">Subscribes to Chat Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>Subscribe</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> Channel, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> Source, <a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> SourceId, <a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a> SourceScriptId, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ChatData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ChatData.html">ChatData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>Channel</i>
              </dt>
              <dd> int channel which the chat occurs on.</dd>
              <dt>
                <i>Source</i>
              </dt>
              <dd> string source of the chat: system, user or script.</dd>
              <dt>
                <i>SourceId</i>
              </dt>
              <dd> Sansar.Script.SessionId id of the chat source. May be <a href="../Sansar.Script/SessionId.html#F:Sansar.Script.SessionId.Invalid">Sansar.Script.SessionId.Invalid</a> if the source is not an agent.</dd>
              <dt>
                <i>SourceScriptId</i>
              </dt>
              <dd> Sansar.Script.ScriptId id of the chat source. May be <a href="../Sansar.Script/ScriptId.html#F:Sansar.Script.ScriptId.Invalid">Sansar.Script.ScriptId.Invalid</a> if the source is not a script.</dd>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Chat.Subscribe(System.Int32,System.String,Sansar.Script.SessionId,Sansar.Script.ScriptId,System.Action{Sansar.Simulation.ChatData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.Chat.User">User Field</h3>
        <blockquote id="F:Sansar.Simulation.Chat.User:member">
          <div class="msummary">Indicates that the chat message originated from a user.</div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>User</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.Chat.User:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.Chat.User:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>