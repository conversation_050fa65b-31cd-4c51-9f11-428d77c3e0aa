# Sansar Tetris V1 - Streamlined Architecture

## Overview

This Tetris implementation uses a **streamlined direct material control system** that eliminates the need for individual block scripts and provides immediate visual feedback.

## Core Components (4 Files)

### 1. GridManager.cs
**Role**: Grid spawning, material control, visual effects
- Spawns 200 blocks using `ScenePrivate.CreateCluster()`
- Stores `MeshComponent` references for direct material control
- Implements real-time color, transparency, and emissivity control
- <PERSON><PERSON> line clearing with flash effects
- Includes rainbow debug mode for testing material control

**Key Features**:
```csharp
// Direct material control - no intermediate scripts
MeshComponent mesh = blockMeshes[x, y];
var props = material.GetProperties();
props.Tint = PieceColors[pieceType];
material.SetProperties(props);
```

### 2. GameManager.cs
**Role**: Game logic, player input, scoring
- Manages game state and piece spawning
- Handles WASD + QE + R controls via command subscription
- Calculates piece positions using PieceLogic
- Sends events to GridManager for visual updates
- Implements scoring and level progression

**Event Communication**:
```csharp
// GameManager → GridManager
PostScriptEvent("show_piece", pieceData);
PostScriptEvent("hide_piece", pieceData);
PostScriptEvent("lock_piece", pieceData);
```

### 3. SeatController.cs
**Role**: Player detection and session management
- Uses Sansar's built-in sit point detection
- Detects player sit/stand events automatically
- Manages game session start/end
- No interaction components needed

**Sit Detection**:
```csharp
// Native Sansar API
rigidBody.SubscribeToSitObject(SitEventType.Start, OnPlayerSitDown);
rigidBody.SubscribeToSitObject(SitEventType.End, OnPlayerStandUp);
```

### 4. PieceLogic.cs
**Role**: Static calculations (no state)
- Pure mathematical functions for piece positioning
- Collision detection algorithms
- Rotation and movement calculations
- Used by GameManager for all piece operations

## Communication Flow

```
Player Sits → SeatController → GameManager → PieceLogic (calculations)
                                     ↓
                              GridManager → Direct Material API
                                     ↓
                              Immediate Visual Response
```

## Eliminated Components

- **BlockController.cs** - Individual block scripts no longer needed
- **200+ script instances** - Replaced by single GridManager with direct control
- **Event parsing overhead** - Direct material API calls
- **Script initialization delays** - Immediate response system

## Material Control System

**Direct API Usage**:
```csharp
// Get materials from spawned blocks
if (objectPrivate.TryGetFirstComponent(out MeshComponent mesh))
{
    blockMeshes[gridX, gridY] = mesh; // Store reference
    
    // Later: Direct control
    foreach (var material in mesh.GetRenderMaterials())
    {
        var props = material.GetProperties();
        props.Tint = newColor;
        props.EmissiveIntensity = glowLevel;
        material.SetProperties(props); // Immediate change
    }
}
```

**Capabilities**:
- **Tint**: 7 Tetris piece colors (Cyan, Yellow, Purple, etc.)
- **Emissivity**: Active piece glow (2.0f), line clear flash (15.0f)
- **Transparency**: Hidden blocks (0,0,0,0), visible blocks (full opacity)
- **Flash Effects**: White pulse for line clearing

## Performance Benefits

1. **Reduced Script Count**: 200+ → 4 scripts total
2. **Immediate Response**: No event routing delays
3. **Direct API Access**: No parsing or interpretation overhead
4. **Simplified Debugging**: All material control in one location
5. **Memory Efficiency**: No per-block script state management

## Setup Requirements

**TetrisBlock Resource**:
- Mesh marked as **Scriptable** in Sansar editor
- Material with **Tint and Emissive properties** enabled
- **No script attached** to the block resource

**Scene Objects**:
- **GridManager**: Invisible object with GridManager.cs script
- **GameManager**: Invisible object with GameManager.cs script  
- **Seat**: Chair with sit points + SeatController.cs script

## Debug Features

**Rainbow Debug Mode**: Automatic visual testing
1. Color cycling through all 7 piece colors
2. Horizontal and vertical wave patterns
3. Flash testing with emissivity
4. Immediate verification of material control

**Enable/Disable**: Set `DebugRainbowMode = true/false` in GridManager

This architecture provides robust Tetris gameplay with immediate visual feedback and minimal script overhead.