# Sansar Tetris - Unified Single Script Edition

A single-player Tetris game for Sansar using a unified script architecture with proper player input controls and direct material control.

## Features

- **Unified Architecture**: Single script combining all functionality - no inter-script communication issues
- **Working Player Input**: Proper `agent.Client.SubscribeToCommand` pattern for responsive controls
- **Dual Grid System**: 400 total blocks (200 game blocks + 200 background blocks)
  - Game blocks: Dynamic visibility and color control for active gameplay
  - Background blocks: Always-visible dark grid for visual reference
- **Material Control**: Immediate color tinting and emissivity effects via `MeshComponent.GetRenderMaterials()`
- **Rainbow Debug Mode**: Visual testing system to verify material control
- **Collision Detection**: Proper grid state management with accurate piece stacking
- **Line Clearing**: Complete line detection with fixed drop logic and consistent visual syncing
- **Key Repeat System**: Smooth left/right movement with configurable delays
- **Visual Consistency**: Clean separation between game state and visual state with master sync method
- **Automated Setup**: Uses `ScenePrivate.CreateCluster()` for block spawning

## Controls

### Getting Started
1. **Click the Tetris seat** to sit down and start playing
2. **Stand up** from the seat to exit the game

### Game Controls (while seated)
- **A**: Move piece left (Keypad4)
- **D**: Move piece right (Keypad6)
- **S**: Soft drop (move down faster) (Keypad2)
- **W**: Slow down piece (extends time before auto-drop) (Keypad8)
- **R**: Rotate piece clockwise (SecondaryAction)
- **F**: Hard drop (instant fall to bottom) (PrimaryAction)
- **1**: Rotate piece counter-clockwise (Action1)

## Script Files

### Current Architecture (Unified Single Script)
- **TetrisGame.cs**: All-in-one script combining all game functionality

### Architecture Overview

**Unified Design**: Single script eliminates inter-script communication issues and provides direct, immediate control over all game systems.

```
TetrisGame.cs ────┬─── Seat Detection (RigidBodyComponent.SubscribeToSitObject)
(Single Script)   │    Player Input (agent.Client.SubscribeToCommand)
                  │    Grid Management (gridState array + blockMeshes array)
                  │    Game Logic (piece spawning, movement, rotation, collision)
                  │    Material Control (MeshComponent.GetRenderMaterials)
                  │    Line Clearing (detection, flash effects, drop logic)
                  │    
                  ▼
400 Spawned ────── Direct material control within single script
Blocks Total      200 game blocks + 200 background blocks
                  No inter-script communication needed
```

### Key Architectural Improvements

1. **Unified Script**: Eliminated all inter-script communication and event timing issues
2. **Working Player Input**: Uses proper `agent.Client.SubscribeToCommand` pattern discovered through testing
3. **Direct Material API**: Uses `MeshComponent.GetRenderMaterials()` and `material.SetProperties()`
4. **Fixed Collision Detection**: Grid state and collision detection in same script for consistency
5. **Fixed Line Clearing**: Proper drop logic processes cleared lines from top to bottom
6. **Immediate Response**: No event delays - all operations within single script context

### Player Input Discovery

Through testing, we discovered that Sansar only passes specific command events to scripts:
- **Keypad4** = A key (Move left)
- **Keypad6** = D key (Move right)
- **Keypad2** = S key (Soft drop)
- **Keypad8** = W key (Slow down)
- **SecondaryAction** = R key (Rotate)
- **PrimaryAction** = F key (Hard drop)
- **Action1** = 1 key (Rotate counter-clockwise)

The proper pattern is:
```csharp
// Get the agent when they sit down
currentPlayer = ScenePrivate.FindAgent(data.ObjectId);

// Subscribe to their commands
agent.Client.SubscribeToCommand("Keypad4", CommandAction.Pressed, HandleMoveLeft, null);
```

## In-Game Setup Instructions

### Step 1: Create the Generic Block
1. In Sansar, create a new object (simple cube works best)
2. Make the object small (0.9 x 0.9 x 0.9 units recommended)
3. Apply a material that supports scripting (Standard Plus Emissive shader recommended)
4. **Critical**: Ensure material has both **Tint** and **Emissive** properties enabled
5. **Important**: Mark the mesh as **Scriptable** in the editor
6. **No script required** - GridManager controls materials directly
7. Save to your inventory as a ClusterResource named "TetrisBlock"

### Step 2: Scene Objects
Create this single object in your scene:

#### Tetris Seat with Game
- Place a chair/seat object where players will sit
- Add **sit points** to the chair in Sansar editor
- Add `TetrisGame.cs` script to the chair object
- **Set these script properties**:
  - `GenericBlock` = your TetrisBlock resource from Step 1
  - `GridOrigin` = position for bottom-left corner of board (e.g., 0,0,0)
  - `BatchSize` = 10 (default is fine)
  - `BatchDelay` = 0.1 (default is fine)
  - `DebugRainbowMode` = true (enables visual testing - set to false after testing)

**That's it!** The unified script handles everything:
- Seat detection via `RigidBodyComponent.SubscribeToSitObject`
- Player input via `agent.Client.SubscribeToCommand`
- Grid spawning and management
- Game logic and collision detection
- Material control and visual effects

#### Requirements for TetrisBlock Resource
- **Scriptable mesh** marked in Sansar editor
- **Material with Tint and Emissive properties** (Standard Plus Emissive shader)
- **No script attached** - GridManager controls all materials directly

### Step 3: Launch
1. Start your scene
2. Watch the logs - GridManager will spawn 200 blocks automatically
3. **Rainbow Debug Sequence**: If enabled, you'll see automatic color cycling to verify material control
4. Sit in the chair (click on it) to start playing
5. Use WASD + Q/E + R to play Tetris!

## Game Flow

1. **Scene Initialization**: TetrisGame spawns 400 blocks in batches (background first, then game blocks)
2. **Player Interaction**: Player clicks Tetris seat to start playing
3. **Game Start**: TetrisGame waits for grid ready, then starts game
4. **Piece Spawning**: Internal logic calculates positions, shows blocks
5. **Player Input**: A/D/S/W/R/F/1 controls trigger piece movement with key repeat
6. **Visual Updates**: Direct mesh control hides old positions, shows new positions
7. **Line Clearing**: Complete rows flash white, hide blocks, shift grid state down
8. **Game End**: Player stands up or blocks reach top

## Technical Details

### Direct Material Control System
The system uses direct material manipulation for immediate visual feedback:
```csharp
// GridManager spawns blocks and stores MeshComponent references
ScenePrivate.CreateCluster(GenericBlock, worldPosition, Quaternion.Identity, Vector.Zero, callback);

// Direct material control for immediate response
MeshComponent mesh = blockMeshes[x, y];
foreach (var material in mesh.GetRenderMaterials())
{
    var props = material.GetProperties();
    props.Tint = PieceColors[pieceType];
    props.EmissiveIntensity = isActivePiece ? 2.0f : 0.0f;
    material.SetProperties(props);
}
```

### Piece Representation
Pieces exist as data patterns with immediate visual feedback:
```csharp
// PieceLogic calculates block positions
Vector[] blocks = PieceLogic.GetPieceBlocks(pieceType, rotation, centerPosition);

// GridManager directly controls materials based on piece state
foreach (var pos in currentPieceBlocks)
{
    gridManager.SetBlockVisible((int)pos.X, (int)pos.Y, pieceType, true);
}

// Event-based communication with direct material response
PostScriptEvent("show_piece", pieceData); // GameManager → GridManager
gridManager.OnShowPieceEvent(data);      // Immediate material change
```

### Material Control System
Direct material manipulation provides instant visual feedback:
- **Tint**: Color based on piece type (Cyan=I, Yellow=O, Purple=T, etc.)
- **Emissivity**: Glow effects for active pieces (2.0f) and line clearing (15.0f)
- **Transparency**: Complete transparency (0,0,0,0) for hidden blocks
- **Flash Effects**: White emissive pulse for line clearing and debug modes
- **Rainbow Debug**: Automatic color cycling to verify material control is working

### Line Clearing Process
Complete rows are cleared with immediate visual feedback:
1. **Detection**: GridManager checks each row for complete coverage
2. **Flash Effect**: Direct material calls pulse emissivity to 15.0f on completed lines
3. **Clear**: Direct material calls set transparency to (0,0,0,0) for cleared blocks
4. **Drop**: Grid state shifts down with immediate material updates for moved blocks

## Troubleshooting

- **Blocks not spawning**: Check that GenericBlock resource is properly set in GridManager
- **No rainbow debug**: Verify materials support Tint properties and mesh is marked scriptable
- **No colors showing**: Check GridManager logs for material capability detection
- **Materials not scriptable**: Ensure TetrisBlock mesh is marked as scriptable in Sansar editor
- **Controls not working**: Ensure player is seated (sit in chair) before trying to play
- **Flash effects not working**: Confirm materials support Emissive properties
- **Performance issues**: Adjust BatchSize (try 5) and BatchDelay (try 0.2) in GridManager
- **Seat interaction not working**: Check that chair has sit points configured in Sansar editor

### Log Messages to Look For
- `"Block (x, y): 1 materials found, mesh scriptable: true"`
- `"Material 'Material.001' - HasTint: True, HasEmissive: True"`
- `"Rainbow debug sequence starting"`
- `"Block (x, y) set to visible - PieceType: 0, Color: (0, 1, 1, 1)"`

## Removed Files (V1 Architecture Cleanup)

The following files were removed as part of the streamlined architecture:
- **BlockController.cs** - Replaced by direct material control in GridManager
- **PieceController.cs** - Functionality moved to GameManager

If you have older versions of these files in your Sansar project, they are no longer needed and can be safely removed.