<html>
  <head>
    <title>Sansar.Simulation.CharacterComponent</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.CharacterComponent">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.CharacterComponent:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.CharacterComponent:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.CharacterComponent:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.CharacterComponent">CharacterComponent  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.CharacterComponent:Summary">The CharacterComponent handles character specific operations like playing CharacterAnimations.</p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.CharacterComponent:Signature">[Sansar.Script.Interface]<br />public class  <b>CharacterComponent</b> : <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.CharacterComponent:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.CharacterComponent:Docs:Remarks">
            Allows for doing operations specific to characters using the Sansar skeleton types
            </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.CharacterComponent:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.
							</p>
        <h2 class="Section">Public Fields</h2>
        <div class="SectionBox" id="Public Fields">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.CharacterComponent.ComponentType">ComponentType</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/ComponentType.html">ComponentType</a>
                  </i>. The <a href="../Sansar.Simulation/CharacterComponent.html#F:Sansar.Simulation.CharacterComponent.ComponentType">CharacterComponent.ComponentType</a> of this component</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.CharacterComponent.ComponentId">ComponentId</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a>
                  </i>. Retrieves the component id for this CharacterComponent.</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/InstanceInterface.html#P:Sansar.Script.InstanceInterface.IsValid">IsValid</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            Whether or not this interface is valid.
             (<i>Inherited from <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.CharacterComponent.UsingFemaleSkeleton">UsingFemaleSkeleton</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. Retrieves whether or not the avatar is using the female skeleton for this CharacterComponent.</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.CharacterComponent.UsingMaleSkeleton">UsingMaleSkeleton</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. Retrieves whether or not the avatar is using the male skeleton for this CharacterComponent.</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset)">PlayAnimation</a>
                  </b>(<a href="../Sansar.Simulation/CharacterAnimation.html">CharacterAnimation</a>, <a href="../Sansar.Simulation/AnimationPlaybackMode.html">AnimationPlaybackMode</a>, <a href="../Sansar.Simulation/AnimationBoneSubset.html">AnimationBoneSubset</a>)<blockquote>Plays an animation in server time synchronously for this CharacterComponent.
            If you want multiple agent's to play an animation together, use this function</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent})">PlayAnimation</a>
                  </b>(<a href="../Sansar.Simulation/CharacterAnimation.html">CharacterAnimation</a>, <a href="../Sansar.Simulation/AnimationPlaybackMode.html">AnimationPlaybackMode</a>, <a href="../Sansar.Simulation/AnimationBoneSubset.html">AnimationBoneSubset</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Plays an animation in server time synchronously for this CharacterComponent.
            If you want multiple agent's to play an animation together, use this function</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single)">PlayAnimation</a>
                  </b>(<a href="../Sansar.Simulation/CharacterAnimation.html">CharacterAnimation</a>, <a href="../Sansar.Simulation/AnimationPlaybackMode.html">AnimationPlaybackMode</a>, <a href="../Sansar.Simulation/AnimationBoneSubset.html">AnimationBoneSubset</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<blockquote>Plays an animation in server time synchronously for this CharacterComponent.
            If you want multiple agent's to play an animation together, use this function</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">PlayAnimation</a>
                  </b>(<a href="../Sansar.Simulation/CharacterAnimation.html">CharacterAnimation</a>, <a href="../Sansar.Simulation/AnimationPlaybackMode.html">AnimationPlaybackMode</a>, <a href="../Sansar.Simulation/AnimationBoneSubset.html">AnimationBoneSubset</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Plays an animation in server time synchronously for this CharacterComponent.
            If you want multiple agent's to play an animation together, use this function</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.CharacterComponent.StopAnimations()">StopAnimations</a>
                  </b>()<blockquote>Stops all animations in server time synchronously for this CharacterComponent.
            If you want multiple agent's to stop animations together, use this function</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset)">StopAnimations</a>
                  </b>(<a href="../Sansar.Simulation/AnimationBoneSubset.html">AnimationBoneSubset</a>)<blockquote>Stops all animations in server time synchronously for this CharacterComponent.
            If you want multiple agent's to stop animations together, use this function</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.CharacterComponent.StopAnimations(System.Action{Sansar.Script.OperationCompleteEvent})">StopAnimations</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Stops all animations in server time synchronously for this CharacterComponent.
            If you want multiple agent's to stop animations together, use this function</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent})">StopAnimations</a>
                  </b>(<a href="../Sansar.Simulation/AnimationBoneSubset.html">AnimationBoneSubset</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Stops all animations in server time synchronously for this CharacterComponent.
            If you want multiple agent's to stop animations together, use this function</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.CharacterComponent.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote> A string representation of this object. </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.CharacterComponent:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="P:Sansar.Simulation.CharacterComponent.ComponentId">ComponentId Property</h3>
        <blockquote id="P:Sansar.Simulation.CharacterComponent.ComponentId:member">
          <div class="msummary">Retrieves the component id for this CharacterComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a> <b>ComponentId</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.CharacterComponent.ComponentId:Value">The id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.CharacterComponent.ComponentId:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.CharacterComponent.ComponentId:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.CharacterComponent.ComponentType">ComponentType Field</h3>
        <blockquote id="F:Sansar.Simulation.CharacterComponent.ComponentType:member">
          <div class="msummary">The <a href="../Sansar.Simulation/CharacterComponent.html#F:Sansar.Simulation.CharacterComponent.ComponentType">CharacterComponent.ComponentType</a> of this component</div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="../Sansar.Simulation/ComponentType.html">ComponentType</a> <b>ComponentType</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.CharacterComponent.ComponentType:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.CharacterComponent.ComponentType:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset)">PlayAnimation Method</h3>
        <blockquote id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset):member">
          <div class="msummary">Plays an animation in server time synchronously for this CharacterComponent.
            If you want multiple agent's to play an animation together, use this function</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>PlayAnimation</b> (<a href="../Sansar.Simulation/CharacterAnimation.html">CharacterAnimation</a> animation, <a href="../Sansar.Simulation/AnimationPlaybackMode.html">AnimationPlaybackMode</a> playbackMode, <a href="../Sansar.Simulation/AnimationBoneSubset.html">AnimationBoneSubset</a> skeletonSubset)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset):Parameters">
            <dl>
              <dt>
                <i>animation</i>
              </dt>
              <dd>The animation to play on the agent.</dd>
              <dt>
                <i>playbackMode</i>
              </dt>
              <dd>How to play the animation. Only PlayOnce and Loop supported. </dd>
              <dt>
                <i>skeletonSubset</i>
              </dt>
              <dd>Which subset of the skeleton to play the animation on.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset):Remarks">This asynchronous method queues the write then returns.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent})">PlayAnimation Method</h3>
        <blockquote id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Plays an animation in server time synchronously for this CharacterComponent.
            If you want multiple agent's to play an animation together, use this function</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>PlayAnimation</b> (<a href="../Sansar.Simulation/CharacterAnimation.html">CharacterAnimation</a> animation, <a href="../Sansar.Simulation/AnimationPlaybackMode.html">AnimationPlaybackMode</a> playbackMode, <a href="../Sansar.Simulation/AnimationBoneSubset.html">AnimationBoneSubset</a> skeletonSubset, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>animation</i>
              </dt>
              <dd>The animation to play on the agent.</dd>
              <dt>
                <i>playbackMode</i>
              </dt>
              <dd>How to play the animation. Only PlayOnce and Loop supported. </dd>
              <dt>
                <i>skeletonSubset</i>
              </dt>
              <dd>Which subset of the skeleton to play the animation on.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single)">PlayAnimation Method</h3>
        <blockquote id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single):member">
          <div class="msummary">Plays an animation in server time synchronously for this CharacterComponent.
            If you want multiple agent's to play an animation together, use this function</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>PlayAnimation</b> (<a href="../Sansar.Simulation/CharacterAnimation.html">CharacterAnimation</a> animation, <a href="../Sansar.Simulation/AnimationPlaybackMode.html">AnimationPlaybackMode</a> playbackMode, <a href="../Sansar.Simulation/AnimationBoneSubset.html">AnimationBoneSubset</a> skeletonSubset, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> playbackSpeed)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single):Parameters">
            <dl>
              <dt>
                <i>animation</i>
              </dt>
              <dd>The animation to play on the agent.</dd>
              <dt>
                <i>playbackMode</i>
              </dt>
              <dd>How to play the animation. Only PlayOnce and Loop supported. </dd>
              <dt>
                <i>skeletonSubset</i>
              </dt>
              <dd>Which subset of the skeleton to play the animation on.</dd>
              <dt>
                <i>playbackSpeed</i>
              </dt>
              <dd>Animation playback speed. Must be betweeen 0.1 and 10.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single):Remarks">This asynchronous method queues the write then returns.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">PlayAnimation Method</h3>
        <blockquote id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Plays an animation in server time synchronously for this CharacterComponent.
            If you want multiple agent's to play an animation together, use this function</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>PlayAnimation</b> (<a href="../Sansar.Simulation/CharacterAnimation.html">CharacterAnimation</a> animation, <a href="../Sansar.Simulation/AnimationPlaybackMode.html">AnimationPlaybackMode</a> playbackMode, <a href="../Sansar.Simulation/AnimationBoneSubset.html">AnimationBoneSubset</a> skeletonSubset, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> playbackSpeed, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>animation</i>
              </dt>
              <dd>The animation to play on the agent.</dd>
              <dt>
                <i>playbackMode</i>
              </dt>
              <dd>How to play the animation. Only PlayOnce and Loop supported. </dd>
              <dt>
                <i>skeletonSubset</i>
              </dt>
              <dd>Which subset of the skeleton to play the animation on.</dd>
              <dt>
                <i>playbackSpeed</i>
              </dt>
              <dd>Animation playback speed. Must be betweeen 0.1 and 10.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.PlayAnimation(Sansar.Simulation.CharacterAnimation,Sansar.Simulation.AnimationPlaybackMode,Sansar.Simulation.AnimationBoneSubset,System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.CharacterComponent.StopAnimations()">StopAnimations Method</h3>
        <blockquote id="M:Sansar.Simulation.CharacterComponent.StopAnimations():member">
          <div class="msummary">Stops all animations in server time synchronously for this CharacterComponent.
            If you want multiple agent's to stop animations together, use this function</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>StopAnimations</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations():Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations():Remarks">This asynchronous method queues the write then returns.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset)">StopAnimations Method</h3>
        <blockquote id="M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset):member">
          <div class="msummary">Stops all animations in server time synchronously for this CharacterComponent.
            If you want multiple agent's to stop animations together, use this function</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>StopAnimations</b> (<a href="../Sansar.Simulation/AnimationBoneSubset.html">AnimationBoneSubset</a> skeletonSubset)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset):Parameters">
            <dl>
              <dt>
                <i>skeletonSubset</i>
              </dt>
              <dd>All Animations for this skeleton subset will be stopped. </dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset):Remarks">This asynchronous method queues the write then returns.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.CharacterComponent.StopAnimations(System.Action{Sansar.Script.OperationCompleteEvent})">StopAnimations Method</h3>
        <blockquote id="M:Sansar.Simulation.CharacterComponent.StopAnimations(System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Stops all animations in server time synchronously for this CharacterComponent.
            If you want multiple agent's to stop animations together, use this function</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>StopAnimations</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations(System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations(System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations(System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations(System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent})">StopAnimations Method</h3>
        <blockquote id="M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Stops all animations in server time synchronously for this CharacterComponent.
            If you want multiple agent's to stop animations together, use this function</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>StopAnimations</b> (<a href="../Sansar.Simulation/AnimationBoneSubset.html">AnimationBoneSubset</a> skeletonSubset, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>skeletonSubset</i>
              </dt>
              <dd>All Animations for this skeleton subset will be stopped. </dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">This asynchronous method queues the write then returns.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.StopAnimations(Sansar.Simulation.AnimationBoneSubset,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.CharacterComponent.ToString()">ToString Method</h3>
        <blockquote id="M:Sansar.Simulation.CharacterComponent.ToString():member">
          <div class="msummary"> A string representation of this object. </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.CharacterComponent.ToString():Returns">A string representation of this object.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.ToString():Remarks">The format of this string may change between releases.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.CharacterComponent.ToString():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.CharacterComponent.UsingFemaleSkeleton">UsingFemaleSkeleton Property</h3>
        <blockquote id="P:Sansar.Simulation.CharacterComponent.UsingFemaleSkeleton:member">
          <div class="msummary">Retrieves whether or not the avatar is using the female skeleton for this CharacterComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>UsingFemaleSkeleton</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.CharacterComponent.UsingFemaleSkeleton:Value">Whether or not the avatar is using the female skeleton</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.CharacterComponent.UsingFemaleSkeleton:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.CharacterComponent.UsingFemaleSkeleton:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.CharacterComponent.UsingMaleSkeleton">UsingMaleSkeleton Property</h3>
        <blockquote id="P:Sansar.Simulation.CharacterComponent.UsingMaleSkeleton:member">
          <div class="msummary">Retrieves whether or not the avatar is using the male skeleton for this CharacterComponent.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>UsingMaleSkeleton</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.CharacterComponent.UsingMaleSkeleton:Value">Whether or not the avatar is using the male skeleton</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.CharacterComponent.UsingMaleSkeleton:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.CharacterComponent.UsingMaleSkeleton:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>