<html>
  <head>
    <title>Sansar.Simulation.ScenePrivate</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.ScenePrivate">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.ScenePrivate:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.ScenePrivate:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.ScenePrivate:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.ScenePrivate">ScenePrivate  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.ScenePrivate:Summary">Interface for Scripts that are part of a Scene. A more complete and less limited API than <a href="../Sansar.Simulation/ScenePublic.html">Sansar.Simulation.ScenePublic</a></p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.ScenePrivate:Signature">[Sansar.Script.Interface]<br />public class  <b>ScenePrivate</b> : <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.ScenePrivate:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.ScenePrivate:Docs:Remarks">The ScenePrivate class provides services relating to the Scene.</div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.ScenePrivate:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.
							</p>
        <h2 class="Section">Public Fields</h2>
        <div class="SectionBox" id="Public Fields">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.ScenePrivate.GravityMaximum">GravityMaximum</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i>. 
            The maximum gravity magnitude in m/s^2, which is equal to 49.05 or 5 G's.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.ScenePrivate.GravityMinimum">GravityMinimum</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i>. 
            The minimum allowed gravity magnitude in m/s^2, which is equal to zero.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.ScenePrivate.MaximumCastRayResults">MaximumCastRayResults</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>
                  </i>. 
            The maximum number of results returned from a <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a>, <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> or <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> call. Anything over this will be truncated.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.ScenePrivate.MinimumCastRadius">MinimumCastRadius</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i>. 
            The minimum radius for a <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> call or minimum half extents for <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a>. Any value smaller than this will use <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> instead.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ScenePrivate.AgentCount">AgentCount</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a>
                  </i>. The number of agents in the Scene.</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ScenePrivate.Chat">Chat</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/Chat.html">Chat</a>
                  </i>. 
            Gets the Chat interface for this Scene
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ScenePrivate.DefaultGravity">DefaultGravity</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i>. 
            The default gravity magnitude in the scene in m/s^2, which is equal to 9.81 or 1 G.
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ScenePrivate.HttpClient">HttpClient</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/HttpClient.html">HttpClient</a>
                  </i>. 
            Gets the HttpClient for this Scene
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/InstanceInterface.html#P:Sansar.Script.InstanceInterface.IsValid">IsValid</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            Whether or not this interface is valid.
             (<i>Inherited from <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ScenePrivate.Reactions">Reactions</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/Reactions.html">Reactions</a>
                  </i>. 
            Gets the Reactions interface for this Scene
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ScenePrivate.SceneInfo">SceneInfo</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/SceneInfo.html">SceneInfo</a>
                  </i>. 
            Gets the SceneInfo for this Scene
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ScenePrivate.User">User</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/User.html">User</a>
                  </i>. 
            Gets the User interface for this Scene
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector)">CastBox</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Casts a box from start to end and returns an array of collisions along the path.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector)">CastBox</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Casts a box from start to end and returns an array of collisions along the path.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector,System.Int32)">CastBox</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Casts a box from start to end and returns an array of collisions along the path.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32)">CastBox</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Casts a box from start to end and returns an array of collisions along the path.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector)">CastCapsule</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Casts a capsule from start to end and returns an array of collisions along the path.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector)">CastCapsule</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Casts a capsule from start to end and returns an array of collisions along the path.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector,System.Int32)">CastCapsule</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Casts a capsule from start to end and returns an array of collisions along the path.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32)">CastCapsule</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Casts a capsule from start to end and returns an array of collisions along the path.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector)">CastRay</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Casts a ray from start to end and returns an array of collisions along the path.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector,System.Int32)">CastRay</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Casts a ray from start to end and returns an array of collisions along the path.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector)">CastSphere</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Casts a sphere from start to end and returns an array of collisions along the path.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector,System.Int32)">CastSphere</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Casts a sphere from start to end and returns an array of collisions along the path.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector)">CreateCluster</a>
                  </b>(<a href="../Sansar.Simulation/ClusterResource.html">ClusterResource</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>
            Creates a new cluster and adds it to the Scene.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Simulation.ScenePrivate.CreateClusterHandler)">CreateCluster</a>
                  </b>(<a href="../Sansar.Simulation/ClusterResource.html">ClusterResource</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar.Simulation/ScenePrivate+CreateClusterHandler.html">ScenePrivate.CreateClusterHandler</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>
             Creates a new cluster and adds it to the Scene.
             </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,System.Action{Sansar.Simulation.ScenePrivate.CreateClusterData})">CreateCluster</a>
                  </b>(<a href="../Sansar.Simulation/ClusterResource.html">ClusterResource</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ScenePrivate.CreateClusterData&gt;</a>)<blockquote>
            Creates a new cluster and adds it to the Scene.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.Guid)">CreateDataStore</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Guid">Guid</a>)<nobr> : <a href="../Sansar.Simulation/DataStore.html">DataStore</a></nobr><blockquote>
            Returns a new instance of the <a href="../Sansar.Simulation/DataStore.html">Sansar.Simulation.DataStore</a> class.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.String)">CreateDataStore</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="../Sansar.Simulation/DataStore.html">DataStore</a></nobr><blockquote>
            Returns a new instance of the <a href="../Sansar.Simulation/DataStore.html">Sansar.Simulation.DataStore</a> class.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.ObjectId)">FindAgent</a>
                  </b>(<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a>)<nobr> : <a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a></nobr><blockquote>
            Looks up an Agent associated with the given object id.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.SessionId)">FindAgent</a>
                  </b>(<a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a>)<nobr> : <a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a></nobr><blockquote>
            Looks up an Agent associated with the given session id if they are in the scene.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.FindAgent(System.Guid)">FindAgent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Guid">Guid</a>)<nobr> : <a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a></nobr><blockquote>
            Looks up an Agent associated with the given persona id, if they are in the scene.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.FindObject(Sansar.Script.ObjectId)">FindObject</a>
                  </b>(<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a>)<nobr> : <a href="../Sansar.Simulation/ObjectPrivate.html">ObjectPrivate</a></nobr><blockquote>
            Looks up a Object associated with the given object id.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String)">FindReflective&lt;TInterface&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerable`1">IEnumerable&lt;TInterface&gt;</a></nobr><blockquote>
            Looks up Reflective objects in the scene that match the interface type by class name.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.FindScript(Sansar.Script.ScriptId)">FindScript</a>
                  </b>(<a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a></nobr><blockquote>
            Looks up the script object associated with the given scriptId
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetAgent(System.UInt32)">GetAgent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a>)<nobr> : <a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a></nobr><blockquote>Get an <a href="../Sansar.Simulation/AgentPrivate.html">Sansar.Simulation.AgentPrivate</a> for a specific agent in the Scene.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetAgents()">GetAgents</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerable`1">IEnumerable&lt;AgentPrivate&gt;</a></nobr><blockquote>
            Returns the current list of Agents in the Scene.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single)">GetBoxClosestPoints</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Extends a box with the given half extents the maximum distance and returns any hits.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single)">GetBoxClosestPoints</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Extends a box with the given half extents the maximum distance and returns any hits.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,System.Int32)">GetBoxClosestPoints</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Extends a box with the given half extents the maximum distance and returns any hits.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32)">GetBoxClosestPoints</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Extends a box with the given half extents the maximum distance and returns any hits.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single)">GetCapsuleClosestPoints</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Extends a capsule with the given parameters the maximum distance and returns any hits.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single)">GetCapsuleClosestPoints</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Extends a capsule with the given parameters the maximum distance and returns any hits.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single,System.Int32)">GetCapsuleClosestPoints</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Extends a capsule with the given parameters the maximum distance and returns any hits.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32)">GetCapsuleClosestPoints</a>
                  </b>(<a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Extends a capsule with the given parameters the maximum distance and returns any hits.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetGravity()">GetGravity</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>Gets the scene's current acceleration of gravity in m/s^2.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetPortalCreatorName(Sansar.Script.ObjectId)">GetPortalCreatorName</a>
                  </b>(<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>The name of the user who created the specified portal.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetPortalDescription(Sansar.Script.ObjectId)">GetPortalDescription</a>
                  </b>(<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>Description for the specified portal.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetPortalUri(Sansar.Script.ObjectId)">GetPortalUri</a>
                  </b>(<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>URI destination for the specified portal.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single)">GetSphereClosestPoints</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Extends a sphere with the given radius the maximum distance and returns any hits.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single,System.Int32)">GetSphereClosestPoints</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<nobr> : <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[]</nobr><blockquote>Extends a sphere with the given radius the maximum distance and returns any hits.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.OverrideAudioStream(System.String)">OverrideAudioStream</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<blockquote>Overrides source of web audio streams.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.OverrideAudioStream(System.String,System.Action{Sansar.Script.OperationCompleteEvent})">OverrideAudioStream</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Overrides source of web audio streams.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String)">OverrideMediaSource</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<blockquote>Sets stream channel source.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String,System.Int32,System.Int32)">OverrideMediaSource</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<blockquote>Overrides source of media surfaces.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String,System.Int32,System.Int32,System.Action{Sansar.Script.OperationCompleteEvent})">OverrideMediaSource</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Overrides source of media surfaces.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.PerformMediaAction(Sansar.Simulation.MediaAction)">PerformMediaAction</a>
                  </b>(<a href="../Sansar.Simulation/MediaAction.html">MediaAction</a>)<blockquote>Performs a specific action on the current media surface.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.PerformMediaAction(Sansar.Simulation.MediaAction,System.Action{Sansar.Script.OperationCompleteEvent})">PerformMediaAction</a>
                  </b>(<a href="../Sansar.Simulation/MediaAction.html">MediaAction</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Performs a specific action on the current media surface.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.PlaySound(Sansar.Simulation.SoundResource,Sansar.Simulation.PlaySettings)">PlaySound</a>
                  </b>(<a href="../Sansar.Simulation/SoundResource.html">SoundResource</a>, <a href="../Sansar.Simulation/PlaySettings.html">PlaySettings</a>)<nobr> : <a href="../Sansar.Simulation/PlayHandle.html">PlayHandle</a></nobr><blockquote>Play sound to direct output.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.PlaySoundAtPosition(Sansar.Simulation.SoundResource,Sansar.Vector,Sansar.Simulation.PlaySettings)">PlaySoundAtPosition</a>
                  </b>(<a href="../Sansar.Simulation/SoundResource.html">SoundResource</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="../Sansar.Simulation/PlaySettings.html">PlaySettings</a>)<nobr> : <a href="../Sansar.Simulation/PlayHandle.html">PlayHandle</a></nobr><blockquote>Play sound at specified position.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.PlayStream(Sansar.Simulation.StreamChannel,System.Single)">PlayStream</a>
                  </b>(<a href="../Sansar.Simulation/StreamChannel.html">StreamChannel</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<nobr> : <a href="../Sansar.Simulation/PlayHandle.html">PlayHandle</a></nobr><blockquote>Play web audio stream to direct output.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.PlayStreamAtPosition(Sansar.Simulation.StreamChannel,Sansar.Vector,System.Single)">PlayStreamAtPosition</a>
                  </b>(<a href="../Sansar.Simulation/StreamChannel.html">StreamChannel</a>, <a href="../Sansar/Vector.html">Sansar.Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<nobr> : <a href="../Sansar.Simulation/PlayHandle.html">PlayHandle</a></nobr><blockquote>Play web audio stream at specified position.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.ResetScene()">ResetScene</a>
                  </b>()<blockquote>
            Restarts the Scene.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single)">SetGravity</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<blockquote>Sets the scene's current gravity acceleration in m/s^2.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetGravity</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>Sets the scene's current gravity acceleration in m/s^2.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetGravity</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the scene's current gravity acceleration in m/s^2.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.SetMegaphone(Sansar.Simulation.AgentPrivate,System.Boolean)">SetMegaphone</a>
                  </b>(<a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<blockquote>Sets the megaphone status of the user.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.SetMegaphone(Sansar.Simulation.AgentPrivate,System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent})">SetMegaphone</a>
                  </b>(<a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>Sets the megaphone status of the user.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote> A string representation of this object. </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStart(Sansar.Simulation.AgentPrivate)">VoiceBroadcastStart</a>
                  </b>(<a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a>)<blockquote>Start broadcasting agent's voice to the voice broadcast channel.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStop(Sansar.Simulation.AgentPrivate)">VoiceBroadcastStop</a>
                  </b>(<a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a>)<blockquote>Stop broadcasting agent's voice to voice broadcast channel.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStopAll()">VoiceBroadcastStopAll</a>
                  </b>()<blockquote>Stop broadcasting all voices to voice broadcast channel.</blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.ScenePrivate:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="P:Sansar.Simulation.ScenePrivate.AgentCount">AgentCount Property</h3>
        <blockquote id="P:Sansar.Simulation.ScenePrivate.AgentCount:member">
          <div class="msummary">The number of agents in the Scene.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a> <b>AgentCount</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ScenePrivate.AgentCount:Value">Unsigned integer count of the number of agents in the Scene.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.AgentCount:Remarks">This number changes when agents join and part.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.AgentCount:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector)">CastBox Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector):member">
          <div class="msummary">Casts a box from start to end and returns an array of collisions along the path.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>CastBox</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> halfExtents, <a href="../Sansar/Vector.html">Sansar.Vector</a> start, <a href="../Sansar/Vector.html">Sansar.Vector</a> end)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>halfExtents</i>
              </dt>
              <dd>The x,y,z distance from the center of the box to the corresponding face.</dd>
              <dt>
                <i>start</i>
              </dt>
              <dd>The initial point of the query.</dd>
              <dt>
                <i>end</i>
              </dt>
              <dd>The end point of the query.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector):Remarks">If all 3 dimensions are less than <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.MinimumCastRadius">ScenePrivate.MinimumCastRadius</a> the call will be forwarded to <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> instead.
            maxHits defaults to 1 if unspecified and will return only the nearest hit.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector)">CastBox Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector):member">
          <div class="msummary">Casts a box from start to end and returns an array of collisions along the path.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>CastBox</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> halfExtents, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> orientation, <a href="../Sansar/Vector.html">Sansar.Vector</a> start, <a href="../Sansar/Vector.html">Sansar.Vector</a> end)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>halfExtents</i>
              </dt>
              <dd>The x,y,z distance from the center of the box to the corresponding face.</dd>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation of the query shape.</dd>
              <dt>
                <i>start</i>
              </dt>
              <dd>The initial point of the query.</dd>
              <dt>
                <i>end</i>
              </dt>
              <dd>The end point of the query.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector):Remarks">If all 3 dimensions are less than <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.MinimumCastRadius">ScenePrivate.MinimumCastRadius</a> the call will be forwarded to <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> instead.
            </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector,System.Int32)">CastBox Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector,System.Int32):member">
          <div class="msummary">Casts a box from start to end and returns an array of collisions along the path.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>CastBox</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> halfExtents, <a href="../Sansar/Vector.html">Sansar.Vector</a> start, <a href="../Sansar/Vector.html">Sansar.Vector</a> end, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> maxHits)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector,System.Int32):Parameters">
            <dl>
              <dt>
                <i>halfExtents</i>
              </dt>
              <dd>The x,y,z distance from the center of the box to the corresponding face.</dd>
              <dt>
                <i>start</i>
              </dt>
              <dd>The initial point of the query.</dd>
              <dt>
                <i>end</i>
              </dt>
              <dd>The end point of the query.</dd>
              <dt>
                <i>maxHits</i>
              </dt>
              <dd>Limit the number of results returned.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector,System.Int32):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector,System.Int32):Remarks">If all 3 dimensions are less than <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.MinimumCastRadius">ScenePrivate.MinimumCastRadius</a> the call will be forwarded to <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> instead.
            maxHits defaults to 1 if unspecified and will return only the nearest hit.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Vector,Sansar.Vector,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32)">CastBox Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32):member">
          <div class="msummary">Casts a box from start to end and returns an array of collisions along the path.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>CastBox</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> halfExtents, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> orientation, <a href="../Sansar/Vector.html">Sansar.Vector</a> start, <a href="../Sansar/Vector.html">Sansar.Vector</a> end, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> maxHits)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32):Parameters">
            <dl>
              <dt>
                <i>halfExtents</i>
              </dt>
              <dd>The x,y,z distance from the center of the box to the corresponding face.</dd>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation of the query shape.</dd>
              <dt>
                <i>start</i>
              </dt>
              <dd>The initial point of the query.</dd>
              <dt>
                <i>end</i>
              </dt>
              <dd>The end point of the query.</dd>
              <dt>
                <i>maxHits</i>
              </dt>
              <dd>Limit the number of results returned.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32):Remarks">If all 3 dimensions are less than <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.MinimumCastRadius">ScenePrivate.MinimumCastRadius</a> the call will be forwarded to <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> instead.
            </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastBox(Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector)">CastCapsule Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector):member">
          <div class="msummary">Casts a capsule from start to end and returns an array of collisions along the path.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>CastCapsule</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleStart, <a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleEnd, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> capsuleRadius, <a href="../Sansar/Vector.html">Sansar.Vector</a> start, <a href="../Sansar/Vector.html">Sansar.Vector</a> end)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>capsuleStart</i>
              </dt>
              <dd>The base point of the capsule.</dd>
              <dt>
                <i>capsuleEnd</i>
              </dt>
              <dd>The end point of the capsule.</dd>
              <dt>
                <i>capsuleRadius</i>
              </dt>
              <dd>The radius of the capsule to query.</dd>
              <dt>
                <i>start</i>
              </dt>
              <dd>The initial point of the query.</dd>
              <dt>
                <i>end</i>
              </dt>
              <dd>The end point of the query.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector):Remarks">If the radius and capsule length are less than <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.MinimumCastRadius">ScenePrivate.MinimumCastRadius</a> the call will be forwarded to <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> instead.
            </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector)">CastCapsule Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector):member">
          <div class="msummary">Casts a capsule from start to end and returns an array of collisions along the path.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>CastCapsule</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleStart, <a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleEnd, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> capsuleRadius, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> orientation, <a href="../Sansar/Vector.html">Sansar.Vector</a> start, <a href="../Sansar/Vector.html">Sansar.Vector</a> end)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>capsuleStart</i>
              </dt>
              <dd>The base point of the capsule.</dd>
              <dt>
                <i>capsuleEnd</i>
              </dt>
              <dd>The end point of the capsule.</dd>
              <dt>
                <i>capsuleRadius</i>
              </dt>
              <dd>The radius of the capsule to query.</dd>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation of the query shape.</dd>
              <dt>
                <i>start</i>
              </dt>
              <dd>The initial point of the query.</dd>
              <dt>
                <i>end</i>
              </dt>
              <dd>The end point of the query.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector):Remarks">If the radius and capsule length are less than <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.MinimumCastRadius">ScenePrivate.MinimumCastRadius</a> the call will be forwarded to <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> instead.
            </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector,System.Int32)">CastCapsule Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector,System.Int32):member">
          <div class="msummary">Casts a capsule from start to end and returns an array of collisions along the path.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>CastCapsule</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleStart, <a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleEnd, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> capsuleRadius, <a href="../Sansar/Vector.html">Sansar.Vector</a> start, <a href="../Sansar/Vector.html">Sansar.Vector</a> end, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> maxHits)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector,System.Int32):Parameters">
            <dl>
              <dt>
                <i>capsuleStart</i>
              </dt>
              <dd>The base point of the capsule.</dd>
              <dt>
                <i>capsuleEnd</i>
              </dt>
              <dd>The end point of the capsule.</dd>
              <dt>
                <i>capsuleRadius</i>
              </dt>
              <dd>The radius of the capsule to query.</dd>
              <dt>
                <i>start</i>
              </dt>
              <dd>The initial point of the query.</dd>
              <dt>
                <i>end</i>
              </dt>
              <dd>The end point of the query.</dd>
              <dt>
                <i>maxHits</i>
              </dt>
              <dd>Limit the number of results returned.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector,System.Int32):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector,System.Int32):Remarks">If the radius and capsule length are less than <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.MinimumCastRadius">ScenePrivate.MinimumCastRadius</a> the call will be forwarded to <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> instead.
            </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Vector,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32)">CastCapsule Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32):member">
          <div class="msummary">Casts a capsule from start to end and returns an array of collisions along the path.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>CastCapsule</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleStart, <a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleEnd, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> capsuleRadius, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> orientation, <a href="../Sansar/Vector.html">Sansar.Vector</a> start, <a href="../Sansar/Vector.html">Sansar.Vector</a> end, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> maxHits)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32):Parameters">
            <dl>
              <dt>
                <i>capsuleStart</i>
              </dt>
              <dd>The base point of the capsule.</dd>
              <dt>
                <i>capsuleEnd</i>
              </dt>
              <dd>The end point of the capsule.</dd>
              <dt>
                <i>capsuleRadius</i>
              </dt>
              <dd>The radius of the capsule to query.</dd>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation of the query shape.</dd>
              <dt>
                <i>start</i>
              </dt>
              <dd>The initial point of the query.</dd>
              <dt>
                <i>end</i>
              </dt>
              <dd>The end point of the query.</dd>
              <dt>
                <i>maxHits</i>
              </dt>
              <dd>Limit the number of results returned.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32):Remarks">If the radius and capsule length are less than <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.MinimumCastRadius">ScenePrivate.MinimumCastRadius</a> the call will be forwarded to <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> instead.
            </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastCapsule(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Quaternion,Sansar.Vector,Sansar.Vector,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector)">CastRay Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector):member">
          <div class="msummary">Casts a ray from start to end and returns an array of collisions along the path.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>CastRay</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> start, <a href="../Sansar/Vector.html">Sansar.Vector</a> end)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>start</i>
              </dt>
              <dd>The initial point of the query.</dd>
              <dt>
                <i>end</i>
              </dt>
              <dd>The end point of the query.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector):Returns">An array of hit results, ordered from nearest to furthest. If the cast did not hit anything the array will be empty.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector):Remarks">maxHits defaults to 1 if unspecified and will return only the nearest hit.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector,System.Int32)">CastRay Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector,System.Int32):member">
          <div class="msummary">Casts a ray from start to end and returns an array of collisions along the path.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>CastRay</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> start, <a href="../Sansar/Vector.html">Sansar.Vector</a> end, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> maxHits)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector,System.Int32):Parameters">
            <dl>
              <dt>
                <i>start</i>
              </dt>
              <dd>The initial point of the query.</dd>
              <dt>
                <i>end</i>
              </dt>
              <dd>The end point of the query.</dd>
              <dt>
                <i>maxHits</i>
              </dt>
              <dd>Limit the number of results returned.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector,System.Int32):Returns">An array of hit results, ordered from nearest to furthest. If the cast did not hit anything the array will be empty.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector,System.Int32):Remarks">maxHits defaults to 1 if unspecified and will return only the nearest hit.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector)">CastSphere Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector):member">
          <div class="msummary">Casts a sphere from start to end and returns an array of collisions along the path.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>CastSphere</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> radius, <a href="../Sansar/Vector.html">Sansar.Vector</a> start, <a href="../Sansar/Vector.html">Sansar.Vector</a> end)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>radius</i>
              </dt>
              <dd>The radius of the sphere.</dd>
              <dt>
                <i>start</i>
              </dt>
              <dd>The initial point of the query.</dd>
              <dt>
                <i>end</i>
              </dt>
              <dd>The end point of the query.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector):Returns">An array of hit results, ordered from nearest to furthest. If the cast did not hit anything the array will be empty.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector):Remarks">If the radius is less than <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.MinimumCastRadius">ScenePrivate.MinimumCastRadius</a> the call will be forwarded to <a href="../Sansar.Simulation/ScenePrivate.html#M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector,System.Int32)">ScenePrivate.CastRay(Sansar.Vector, Sansar.Vector, int)</a> instead.
            maxHits defaults to 1 if unspecified and will return only the nearest hit.
            </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector,System.Int32)">CastSphere Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector,System.Int32):member">
          <div class="msummary">Casts a sphere from start to end and returns an array of collisions along the path.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>CastSphere</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> radius, <a href="../Sansar/Vector.html">Sansar.Vector</a> start, <a href="../Sansar/Vector.html">Sansar.Vector</a> end, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> maxHits)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector,System.Int32):Parameters">
            <dl>
              <dt>
                <i>radius</i>
              </dt>
              <dd>The radius of the sphere.</dd>
              <dt>
                <i>start</i>
              </dt>
              <dd>The initial point of the query.</dd>
              <dt>
                <i>end</i>
              </dt>
              <dd>The end point of the query.</dd>
              <dt>
                <i>maxHits</i>
              </dt>
              <dd>Limit the number of results returned.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector,System.Int32):Returns">An array of hit results, ordered from nearest to furthest. If the cast did not hit anything the array will be empty.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector,System.Int32):Remarks">If the radius is less than <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.MinimumCastRadius">ScenePrivate.MinimumCastRadius</a> the call will be forwarded to <a href="../Sansar.Simulation/ScenePrivate.html#M:Sansar.Simulation.ScenePrivate.CastRay(Sansar.Vector,Sansar.Vector,System.Int32)">ScenePrivate.CastRay(Sansar.Vector, Sansar.Vector, int)</a> instead.
            maxHits defaults to 1 if unspecified and will return only the nearest hit.
            </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CastSphere(System.Single,Sansar.Vector,Sansar.Vector,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ScenePrivate.Chat">Chat Property</h3>
        <blockquote id="P:Sansar.Simulation.ScenePrivate.Chat:member">
          <div class="msummary">
            Gets the Chat interface for this Scene
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/Chat.html">Chat</a> <b>Chat</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ScenePrivate.Chat:Value">The Chat Api</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.Chat:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.Chat:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector)">CreateCluster Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector):member">
          <div class="msummary">
            Creates a new cluster and adds it to the Scene.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>CreateCluster</b> (<a href="../Sansar.Simulation/ClusterResource.html">ClusterResource</a> asset, <a href="../Sansar/Vector.html">Sansar.Vector</a> position, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> rotation, <a href="../Sansar/Vector.html">Sansar.Vector</a> initialVelocity)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector):Parameters">
            <dl>
              <dt>
                <i>asset</i>
              </dt>
              <dd>Asset identifier to create.</dd>
              <dt>
                <i>position</i>
              </dt>
              <dd>Mono.Simd.Vector4f for the initial position.</dd>
              <dt>
                <i>rotation</i>
              </dt>
              <dd>Mono.Simd.Vector4f for the initial rotation.</dd>
              <dt>
                <i>initialVelocity</i>
              </dt>
              <dd>Mono.Simd.Vector4f for the initial velocity.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector):Returns">The internal event id for the completion event.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector):Remarks">The new object may start simulating before the event is triggered.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Simulation.ScenePrivate.CreateClusterHandler)">CreateCluster Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Simulation.ScenePrivate.CreateClusterHandler):member">
          <div class="msummary">
             Creates a new cluster and adds it to the Scene.
             </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>CreateCluster</b> (<a href="../Sansar.Simulation/ClusterResource.html">ClusterResource</a> asset, <a href="../Sansar/Vector.html">Sansar.Vector</a> position, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> rotation, <a href="../Sansar/Vector.html">Sansar.Vector</a> initialVelocity, <a href="../Sansar.Simulation/ScenePrivate+CreateClusterHandler.html">ScenePrivate.CreateClusterHandler</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Simulation.ScenePrivate.CreateClusterHandler):Parameters">
            <dl>
              <dt>
                <i>asset</i>
              </dt>
              <dd>Asset identifier to create.</dd>
              <dt>
                <i>position</i>
              </dt>
              <dd>Mono.Simd.Vector4f for the initial position.</dd>
              <dt>
                <i>rotation</i>
              </dt>
              <dd>Mono.Simd.Vector4f for the initial rotation.</dd>
              <dt>
                <i>initialVelocity</i>
              </dt>
              <dd>Mono.Simd.Vector4f for the initial velocity.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Simulation.ScenePrivate.CreateClusterHandler):Returns">The internal event id for the completion event.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Simulation.ScenePrivate.CreateClusterHandler):Remarks">The new object may start simulating before the event is triggered.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,Sansar.Simulation.ScenePrivate.CreateClusterHandler):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,System.Action{Sansar.Simulation.ScenePrivate.CreateClusterData})">CreateCluster Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,System.Action{Sansar.Simulation.ScenePrivate.CreateClusterData}):member">
          <div class="msummary">
            Creates a new cluster and adds it to the Scene.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>CreateCluster</b> (<a href="../Sansar.Simulation/ClusterResource.html">ClusterResource</a> asset, <a href="../Sansar/Vector.html">Sansar.Vector</a> position, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> rotation, <a href="../Sansar/Vector.html">Sansar.Vector</a> initialVelocity, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ScenePrivate.CreateClusterData&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,System.Action{Sansar.Simulation.ScenePrivate.CreateClusterData}):Parameters">
            <dl>
              <dt>
                <i>asset</i>
              </dt>
              <dd>Asset identifier to create.</dd>
              <dt>
                <i>position</i>
              </dt>
              <dd>Mono.Simd.Vector4f for the initial position.</dd>
              <dt>
                <i>rotation</i>
              </dt>
              <dd>Mono.Simd.Vector4f for the initial rotation.</dd>
              <dt>
                <i>initialVelocity</i>
              </dt>
              <dd>Mono.Simd.Vector4f for the initial velocity.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,System.Action{Sansar.Simulation.ScenePrivate.CreateClusterData}):Returns">The internal event id for the completion event.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,System.Action{Sansar.Simulation.ScenePrivate.CreateClusterData}):Remarks">The new object may start simulating before the event is triggered.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateCluster(Sansar.Simulation.ClusterResource,Sansar.Vector,Sansar.Quaternion,Sansar.Vector,System.Action{Sansar.Simulation.ScenePrivate.CreateClusterData}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.Guid)">CreateDataStore Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.Guid):member">
          <div class="msummary">
            Returns a new instance of the <a href="../Sansar.Simulation/DataStore.html">Sansar.Simulation.DataStore</a> class.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/DataStore.html">DataStore</a> <b>CreateDataStore</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Guid">Guid</a> id)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.Guid):Parameters">
            <dl>
              <dt>
                <i>id</i>
              </dt>
              <dd>The id of the <a href="../Sansar.Simulation/DataStore.html">Sansar.Simulation.DataStore</a> to create.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.Guid):Returns">A new <a href="../Sansar.Simulation/DataStore.html">Sansar.Simulation.DataStore</a> object.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.Guid):Remarks">All <a href="../Sansar.Simulation/DataStore.html">Sansar.Simulation.DataStore</a> objects with a given <a href="../Sansar.Simulation/DataStore.html#P:Sansar.Simulation.DataStore.Id">DataStore.Id</a> share the same database.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.Guid):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.String)">CreateDataStore Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.String):member">
          <div class="msummary">
            Returns a new instance of the <a href="../Sansar.Simulation/DataStore.html">Sansar.Simulation.DataStore</a> class.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/DataStore.html">DataStore</a> <b>CreateDataStore</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> id)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.String):Parameters">
            <dl>
              <dt>
                <i>id</i>
              </dt>
              <dd>String used to generate an id. If the id is a valid Guid then it will be used directly.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.String):Returns">A new <a href="../Sansar.Simulation/DataStore.html">Sansar.Simulation.DataStore</a> object.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.String):Remarks">All <a href="../Sansar.Simulation/DataStore.html">Sansar.Simulation.DataStore</a> objects with a given <a href="../Sansar.Simulation/DataStore.html#P:Sansar.Simulation.DataStore.Id">DataStore.Id</a> share the same database. Ensure that this value is kept secret and unique.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.CreateDataStore(System.String):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ScenePrivate.DefaultGravity">DefaultGravity Property</h3>
        <blockquote id="P:Sansar.Simulation.ScenePrivate.DefaultGravity:member">
          <div class="msummary">
            The default gravity magnitude in the scene in m/s^2, which is equal to 9.81 or 1 G.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>DefaultGravity</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ScenePrivate.DefaultGravity:Value">The default scene gravity magnitude.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.DefaultGravity:Remarks">The direction of the gravity vector is constant.See GravityExample.cs in the Examples folder for more details.</div>
          <h2 class="Section">Example</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.DefaultGravity:Example:1">
            <table class="CodeExampleTable">
              <tr>
                <td>
                  <b>
                    <font size="-1">C# Example</font>
                  </b>
                </td>
              </tr>
              <tr>
                <td>
                  <pre class="code-csharp">float sansarGravity = ScenePrivate.DefaultGravity; // Get default gravity in m/s^2</pre>
                </td>
              </tr>
            </table>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.DefaultGravity:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.ObjectId)">FindAgent Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.ObjectId):member">
          <div class="msummary">
            Looks up an Agent associated with the given object id.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a> <b>FindAgent</b> (<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a> objectId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.ObjectId):Parameters">
            <dl>
              <dt>
                <i>objectId</i>
              </dt>
              <dd>The object to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.ObjectId):Returns">The agent, or null if an agent cannot be found with that id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.ObjectId):Remarks">Agents may leave at any time.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.ObjectId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.SessionId)">FindAgent Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.SessionId):member">
          <div class="msummary">
            Looks up an Agent associated with the given session id if they are in the scene.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a> <b>FindAgent</b> (<a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> sessionId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.SessionId):Parameters">
            <dl>
              <dt>
                <i>sessionId</i>
              </dt>
              <dd>The session id of the agent to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.SessionId):Returns">The agent, or null if an agent cannot be found with that id in the scene.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.SessionId):Remarks">Agents may leave at any time. Attempting to use an Agent or AgentInfo interface for an agent no longer in the scene will throw a NullReferenceException.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.FindAgent(Sansar.Script.SessionId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.FindAgent(System.Guid)">FindAgent Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.FindAgent(System.Guid):member">
          <div class="msummary">
            Looks up an Agent associated with the given persona id, if they are in the scene.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a> <b>FindAgent</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Guid">Guid</a> personaId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindAgent(System.Guid):Parameters">
            <dl>
              <dt>
                <i>personaId</i>
              </dt>
              <dd>The object to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindAgent(System.Guid):Returns">The agent, or null if an agent cannot be found with that id in the scene.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.FindAgent(System.Guid):Remarks">Agents may leave at any time. Attempting to use an Agent or AgentInfo interface for an agent no longer in the scene will throw a NullReferenceException.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.FindAgent(System.Guid):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.FindObject(Sansar.Script.ObjectId)">FindObject Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.FindObject(Sansar.Script.ObjectId):member">
          <div class="msummary">
            Looks up a Object associated with the given object id.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/ObjectPrivate.html">ObjectPrivate</a> <b>FindObject</b> (<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a> objectId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindObject(Sansar.Script.ObjectId):Parameters">
            <dl>
              <dt>
                <i>objectId</i>
              </dt>
              <dd>The object to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindObject(Sansar.Script.ObjectId):Returns">The ObjectPrivate, or null if a Object cannot be found with that id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.FindObject(Sansar.Script.ObjectId):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.FindObject(Sansar.Script.ObjectId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String)">FindReflective&lt;TInterface&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String):member">
          <div class="msummary">
            Looks up Reflective objects in the scene that match the interface type by class name.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[Sansar.Script.NonReflective]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerable`1">IEnumerable&lt;TInterface&gt;</a> <b>FindReflective&lt;TInterface&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> name)<br /> where TInterface : class</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String):Type Parameters">
            <dl>
              <dt>
                <i>TInterface</i>
              </dt>
              <dd>The interface type to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String):Parameters">
            <dl>
              <dt>
                <i>name</i>
              </dt>
              <dd>The type name of the object's class to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String):Returns">An IEnumerable which contains all objects of the given type name that match the given interface.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String):Remarks">The name given corresponds to the <a href="javascript:alert(&quot;Documentation not found.&quot;)">Type.FullName</a> of the object. Multiple scripts may define unrelated types of the same name, but only registered objects that match the interface of TInterface will be returned.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.FindScript(Sansar.Script.ScriptId)">FindScript Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.FindScript(Sansar.Script.ScriptId):member">
          <div class="msummary">
            Looks up the script object associated with the given scriptId
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a> <b>FindScript</b> (<a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a> scriptId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindScript(Sansar.Script.ScriptId):Parameters">
            <dl>
              <dt>
                <i>scriptId</i>
              </dt>
              <dd>The scriptId of the object to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.FindScript(Sansar.Script.ScriptId):Returns">The script object, or null if an script cannot be found with that id in the scene.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.FindScript(Sansar.Script.ScriptId):Remarks">Objects and their associated scripts may be removed the scene at any time.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.FindScript(Sansar.Script.ScriptId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetAgent(System.UInt32)">GetAgent Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetAgent(System.UInt32):member">
          <div class="msummary">Get an <a href="../Sansar.Simulation/AgentPrivate.html">Sansar.Simulation.AgentPrivate</a> for a specific agent in the Scene.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a> <b>GetAgent</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a> index)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetAgent(System.UInt32):Parameters">
            <dl>
              <dt>
                <i>index</i>
              </dt>
              <dd>The index of the agent to get.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetAgent(System.UInt32):Returns">Returns null if the index is larger than the number of agents in the Scene, or if the agent is not fully loaded.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetAgent(System.UInt32):Remarks">Agents may appear at different indices as they join and part.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetAgent(System.UInt32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetAgents()">GetAgents Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetAgents():member">
          <div class="msummary">
            Returns the current list of Agents in the Scene.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerable`1">IEnumerable&lt;AgentPrivate&gt;</a> <b>GetAgents</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetAgents():Returns">The current list of agents in the Scene</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetAgents():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetAgents():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single)">GetBoxClosestPoints Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single):member">
          <div class="msummary">Extends a box with the given half extents the maximum distance and returns any hits.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>GetBoxClosestPoints</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> halfExtents, <a href="../Sansar/Vector.html">Sansar.Vector</a> origin, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> maximumDistance)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single):Parameters">
            <dl>
              <dt>
                <i>halfExtents</i>
              </dt>
              <dd>The x,y,z distance from the center of the box to the corresponding face.</dd>
              <dt>
                <i>origin</i>
              </dt>
              <dd>The center of the query.</dd>
              <dt>
                <i>maximumDistance</i>
              </dt>
              <dd>the maximum distance from the origin to check.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single)">GetBoxClosestPoints Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single):member">
          <div class="msummary">Extends a box with the given half extents the maximum distance and returns any hits.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>GetBoxClosestPoints</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> halfExtents, <a href="../Sansar/Vector.html">Sansar.Vector</a> origin, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> orientation, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> maximumDistance)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single):Parameters">
            <dl>
              <dt>
                <i>halfExtents</i>
              </dt>
              <dd>The x,y,z distance from the center of the box to the corresponding face.</dd>
              <dt>
                <i>origin</i>
              </dt>
              <dd>The center of the query.</dd>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation of the query shape.</dd>
              <dt>
                <i>maximumDistance</i>
              </dt>
              <dd>the maximum distance from the origin to check.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single):Returns">An array of hit results, ordered from nearest to furthest. If the cast did not hit anything the array will be empty.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,System.Int32)">GetBoxClosestPoints Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,System.Int32):member">
          <div class="msummary">Extends a box with the given half extents the maximum distance and returns any hits.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>GetBoxClosestPoints</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> halfExtents, <a href="../Sansar/Vector.html">Sansar.Vector</a> origin, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> maximumDistance, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> maxHits)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,System.Int32):Parameters">
            <dl>
              <dt>
                <i>halfExtents</i>
              </dt>
              <dd>The x,y,z distance from the center of the box to the corresponding face.</dd>
              <dt>
                <i>origin</i>
              </dt>
              <dd>The center of the query.</dd>
              <dt>
                <i>maximumDistance</i>
              </dt>
              <dd>the maximum distance from the origin to check.</dd>
              <dt>
                <i>maxHits</i>
              </dt>
              <dd>Limit the number of results returned.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,System.Int32):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,System.Int32):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32)">GetBoxClosestPoints Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32):member">
          <div class="msummary">Extends a box with the given half extents the maximum distance and returns any hits.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>GetBoxClosestPoints</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> halfExtents, <a href="../Sansar/Vector.html">Sansar.Vector</a> origin, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> orientation, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> maximumDistance, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> maxHits)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32):Parameters">
            <dl>
              <dt>
                <i>halfExtents</i>
              </dt>
              <dd>The x,y,z distance from the center of the box to the corresponding face.</dd>
              <dt>
                <i>origin</i>
              </dt>
              <dd>The center of the query.</dd>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation of the query shape.</dd>
              <dt>
                <i>maximumDistance</i>
              </dt>
              <dd>the maximum distance from the origin to check.</dd>
              <dt>
                <i>maxHits</i>
              </dt>
              <dd>Limit the number of results returned.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32):Returns">An array of hit results, ordered from nearest to furthest. If the cast did not hit anything the array will be empty.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetBoxClosestPoints(Sansar.Vector,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single)">GetCapsuleClosestPoints Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single):member">
          <div class="msummary">Extends a capsule with the given parameters the maximum distance and returns any hits.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>GetCapsuleClosestPoints</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleStart, <a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleEnd, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> capsuleRadius, <a href="../Sansar/Vector.html">Sansar.Vector</a> origin, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> maximumDistance)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single):Parameters">
            <dl>
              <dt>
                <i>capsuleStart</i>
              </dt>
              <dd>The base point of the capsule.</dd>
              <dt>
                <i>capsuleEnd</i>
              </dt>
              <dd>The end point of the capsule.</dd>
              <dt>
                <i>capsuleRadius</i>
              </dt>
              <dd>The radius of the capsule to query.</dd>
              <dt>
                <i>origin</i>
              </dt>
              <dd>The base of the capsule.</dd>
              <dt>
                <i>maximumDistance</i>
              </dt>
              <dd>the maximum distance from the origin to check.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single)">GetCapsuleClosestPoints Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single):member">
          <div class="msummary">Extends a capsule with the given parameters the maximum distance and returns any hits.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>GetCapsuleClosestPoints</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleStart, <a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleEnd, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> capsuleRadius, <a href="../Sansar/Vector.html">Sansar.Vector</a> origin, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> orientation, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> maximumDistance)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single):Parameters">
            <dl>
              <dt>
                <i>capsuleStart</i>
              </dt>
              <dd>The base point of the capsule.</dd>
              <dt>
                <i>capsuleEnd</i>
              </dt>
              <dd>The end point of the capsule.</dd>
              <dt>
                <i>capsuleRadius</i>
              </dt>
              <dd>The radius of the capsule to query.</dd>
              <dt>
                <i>origin</i>
              </dt>
              <dd>The base of the capsule.</dd>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation of the query shape.</dd>
              <dt>
                <i>maximumDistance</i>
              </dt>
              <dd>the maximum distance from the origin to check.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single,System.Int32)">GetCapsuleClosestPoints Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single,System.Int32):member">
          <div class="msummary">Extends a capsule with the given parameters the maximum distance and returns any hits.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>GetCapsuleClosestPoints</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleStart, <a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleEnd, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> capsuleRadius, <a href="../Sansar/Vector.html">Sansar.Vector</a> origin, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> maximumDistance, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> maxHits)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single,System.Int32):Parameters">
            <dl>
              <dt>
                <i>capsuleStart</i>
              </dt>
              <dd>The base point of the capsule.</dd>
              <dt>
                <i>capsuleEnd</i>
              </dt>
              <dd>The end point of the capsule.</dd>
              <dt>
                <i>capsuleRadius</i>
              </dt>
              <dd>The radius of the capsule to query.</dd>
              <dt>
                <i>origin</i>
              </dt>
              <dd>The base of the capsule.</dd>
              <dt>
                <i>maximumDistance</i>
              </dt>
              <dd>the maximum distance from the origin to check.</dd>
              <dt>
                <i>maxHits</i>
              </dt>
              <dd>Limit the number of results returned.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single,System.Int32):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single,System.Int32):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,System.Single,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32)">GetCapsuleClosestPoints Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32):member">
          <div class="msummary">Extends a capsule with the given parameters the maximum distance and returns any hits.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>GetCapsuleClosestPoints</b> (<a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleStart, <a href="../Sansar/Vector.html">Sansar.Vector</a> capsuleEnd, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> capsuleRadius, <a href="../Sansar/Vector.html">Sansar.Vector</a> origin, <a href="../Sansar/Quaternion.html">Sansar.Quaternion</a> orientation, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> maximumDistance, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> maxHits)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32):Parameters">
            <dl>
              <dt>
                <i>capsuleStart</i>
              </dt>
              <dd>The base point of the capsule.</dd>
              <dt>
                <i>capsuleEnd</i>
              </dt>
              <dd>The end point of the capsule.</dd>
              <dt>
                <i>capsuleRadius</i>
              </dt>
              <dd>The radius of the capsule to query.</dd>
              <dt>
                <i>origin</i>
              </dt>
              <dd>The base of the capsule.</dd>
              <dt>
                <i>orientation</i>
              </dt>
              <dd>The orientation of the query shape.</dd>
              <dt>
                <i>maximumDistance</i>
              </dt>
              <dd>the maximum distance from the origin to check.</dd>
              <dt>
                <i>maxHits</i>
              </dt>
              <dd>Limit the number of results returned.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetCapsuleClosestPoints(Sansar.Vector,Sansar.Vector,System.Single,Sansar.Vector,Sansar.Quaternion,System.Single,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetGravity()">GetGravity Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetGravity():member">
          <div class="msummary">Gets the scene's current acceleration of gravity in m/s^2.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>GetGravity</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetGravity():Returns">Number indicating the magnitude of gravity in the scene.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetGravity():Remarks">The default gravity for the scene is <a href="../Sansar.Simulation/ScenePrivate.html#P:Sansar.Simulation.ScenePrivate.DefaultGravity">ScenePrivate.DefaultGravity</a>See GravityExample.cs in the Examples folder for more details.</div>
          <h2 class="Section">Example</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetGravity():Example:1">
            <table class="CodeExampleTable">
              <tr>
                <td>
                  <b>
                    <font size="-1">C# Example</font>
                  </b>
                </td>
              </tr>
              <tr>
                <td>
                  <pre class="code-csharp">float currentGravity = ScenePrivate.GetGravity(); // Get the current scene gravity in m/s^2</pre>
                </td>
              </tr>
            </table>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetGravity():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetPortalCreatorName(Sansar.Script.ObjectId)">GetPortalCreatorName Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetPortalCreatorName(Sansar.Script.ObjectId):member">
          <div class="msummary">The name of the user who created the specified portal.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>GetPortalCreatorName</b> (<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a> objectId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetPortalCreatorName(Sansar.Script.ObjectId):Parameters">
            <dl>
              <dt>
                <i>objectId</i>
              </dt>
              <dd>
                <span class="NotEntered">Documentation for this section has not yet been entered.</span>
              </dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetPortalCreatorName(Sansar.Script.ObjectId):Returns">String</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetPortalCreatorName(Sansar.Script.ObjectId):Remarks">User who created the portal.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetPortalCreatorName(Sansar.Script.ObjectId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetPortalDescription(Sansar.Script.ObjectId)">GetPortalDescription Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetPortalDescription(Sansar.Script.ObjectId):member">
          <div class="msummary">Description for the specified portal.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>GetPortalDescription</b> (<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a> objectId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetPortalDescription(Sansar.Script.ObjectId):Parameters">
            <dl>
              <dt>
                <i>objectId</i>
              </dt>
              <dd>
                <span class="NotEntered">Documentation for this section has not yet been entered.</span>
              </dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetPortalDescription(Sansar.Script.ObjectId):Returns">String</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetPortalDescription(Sansar.Script.ObjectId):Remarks">The description of the portal.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetPortalDescription(Sansar.Script.ObjectId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetPortalUri(Sansar.Script.ObjectId)">GetPortalUri Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetPortalUri(Sansar.Script.ObjectId):member">
          <div class="msummary">URI destination for the specified portal.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>GetPortalUri</b> (<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a> objectId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetPortalUri(Sansar.Script.ObjectId):Parameters">
            <dl>
              <dt>
                <i>objectId</i>
              </dt>
              <dd>
                <span class="NotEntered">Documentation for this section has not yet been entered.</span>
              </dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetPortalUri(Sansar.Script.ObjectId):Returns">String</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetPortalUri(Sansar.Script.ObjectId):Remarks">The URI of the destination of the portal.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetPortalUri(Sansar.Script.ObjectId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single)">GetSphereClosestPoints Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single):member">
          <div class="msummary">Extends a sphere with the given radius the maximum distance and returns any hits.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>GetSphereClosestPoints</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> radius, <a href="../Sansar/Vector.html">Sansar.Vector</a> origin, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> maximumDistance)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single):Parameters">
            <dl>
              <dt>
                <i>radius</i>
              </dt>
              <dd>The radius of the sphere to query.</dd>
              <dt>
                <i>origin</i>
              </dt>
              <dd>The center of the query.</dd>
              <dt>
                <i>maximumDistance</i>
              </dt>
              <dd>the maximum distance from the origin to check.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single,System.Int32)">GetSphereClosestPoints Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single,System.Int32):member">
          <div class="msummary">Extends a sphere with the given radius the maximum distance and returns any hits.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/RayCastHit.html">RayCastHit</a>[] <b>GetSphereClosestPoints</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> radius, <a href="../Sansar/Vector.html">Sansar.Vector</a> origin, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> maximumDistance, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> maxHits)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single,System.Int32):Parameters">
            <dl>
              <dt>
                <i>radius</i>
              </dt>
              <dd>The radius of the sphere to query.</dd>
              <dt>
                <i>origin</i>
              </dt>
              <dd>The center of the query.</dd>
              <dt>
                <i>maximumDistance</i>
              </dt>
              <dd>the maximum distance from the origin to check.</dd>
              <dt>
                <i>maxHits</i>
              </dt>
              <dd>Limit the number of results returned.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single,System.Int32):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single,System.Int32):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.GetSphereClosestPoints(System.Single,Sansar.Vector,System.Single,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.ScenePrivate.GravityMaximum">GravityMaximum Field</h3>
        <blockquote id="F:Sansar.Simulation.ScenePrivate.GravityMaximum:member">
          <div class="msummary">
            The maximum gravity magnitude in m/s^2, which is equal to 49.05 or 5 G's.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>GravityMaximum</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.ScenePrivate.GravityMaximum:Remarks">A value greater than this will clamp to this value.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.ScenePrivate.GravityMaximum:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.ScenePrivate.GravityMinimum">GravityMinimum Field</h3>
        <blockquote id="F:Sansar.Simulation.ScenePrivate.GravityMinimum:member">
          <div class="msummary">
            The minimum allowed gravity magnitude in m/s^2, which is equal to zero.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>GravityMinimum</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.ScenePrivate.GravityMinimum:Remarks">A value less than this will clamp to this value.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.ScenePrivate.GravityMinimum:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ScenePrivate.HttpClient">HttpClient Property</h3>
        <blockquote id="P:Sansar.Simulation.ScenePrivate.HttpClient:member">
          <div class="msummary">
            Gets the HttpClient for this Scene
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/HttpClient.html">HttpClient</a> <b>HttpClient</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ScenePrivate.HttpClient:Value">The HttpClient Api</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.HttpClient:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.HttpClient:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.ScenePrivate.MaximumCastRayResults">MaximumCastRayResults Field</h3>
        <blockquote id="F:Sansar.Simulation.ScenePrivate.MaximumCastRayResults:member">
          <div class="msummary">
            The maximum number of results returned from a <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a>, <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> or <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> call. Anything over this will be truncated.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>MaximumCastRayResults</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.ScenePrivate.MaximumCastRayResults:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.ScenePrivate.MaximumCastRayResults:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.ScenePrivate.MinimumCastRadius">MinimumCastRadius Field</h3>
        <blockquote id="F:Sansar.Simulation.ScenePrivate.MinimumCastRadius:member">
          <div class="msummary">
            The minimum radius for a <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> call or minimum half extents for <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a>. Any value smaller than this will use <a href="javascript:alert(&quot;Documentation not found.&quot;)">.</a> instead.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>MinimumCastRadius</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.ScenePrivate.MinimumCastRadius:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.ScenePrivate.MinimumCastRadius:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.OverrideAudioStream(System.String)">OverrideAudioStream Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.OverrideAudioStream(System.String):member">
          <div class="msummary">Overrides source of web audio streams.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>OverrideAudioStream</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> url)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideAudioStream(System.String):Parameters">
            <dl>
              <dt>
                <i>url</i>
              </dt>
              <dd>URL to use.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideAudioStream(System.String):Remarks">Applies to all agents in this scene.  NOTE: this may cause a few seconds of silence while restarting the stream.  To avoid silence, assign URLs in your SceneSettings instead, which will pre-load them.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideAudioStream(System.String):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.OverrideAudioStream(System.String,System.Action{Sansar.Script.OperationCompleteEvent})">OverrideAudioStream Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.OverrideAudioStream(System.String,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Overrides source of web audio streams.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>OverrideAudioStream</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> url, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideAudioStream(System.String,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>url</i>
              </dt>
              <dd>URL to use.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideAudioStream(System.String,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">Applies to all agents in this scene.  NOTE: this may cause a few seconds of silence while restarting the stream.  To avoid silence, assign URLs in your SceneSettings instead, which will pre-load them.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideAudioStream(System.String,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String)">OverrideMediaSource Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String):member">
          <div class="msummary">Sets stream channel source.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>OverrideMediaSource</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> url)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String):Parameters">
            <dl>
              <dt>
                <i>url</i>
              </dt>
              <dd>URL to use.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String,System.Int32,System.Int32)">OverrideMediaSource Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String,System.Int32,System.Int32):member">
          <div class="msummary">Overrides source of media surfaces.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>OverrideMediaSource</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> url, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> mediaWidth, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> mediaHeight)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String,System.Int32,System.Int32):Parameters">
            <dl>
              <dt>
                <i>url</i>
              </dt>
              <dd>URL to use.</dd>
              <dt>
                <i>mediaWidth</i>
              </dt>
              <dd>(cMediaChannel only) source width.</dd>
              <dt>
                <i>mediaHeight</i>
              </dt>
              <dd>(cMediaChannel only) source height.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String,System.Int32,System.Int32):Remarks">Applies to all agents in this scene.  NOTE: this will cause a few seconds of silence while restarting starting the stream.  To avoid silence, assign URLs in your SceneSettings instead, which will pre-load them.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String,System.Int32,System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String,System.Int32,System.Int32,System.Action{Sansar.Script.OperationCompleteEvent})">OverrideMediaSource Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String,System.Int32,System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Overrides source of media surfaces.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>OverrideMediaSource</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> url, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> mediaWidth, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> mediaHeight, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String,System.Int32,System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>url</i>
              </dt>
              <dd>URL to use.</dd>
              <dt>
                <i>mediaWidth</i>
              </dt>
              <dd>(cMediaChannel only) source width.</dd>
              <dt>
                <i>mediaHeight</i>
              </dt>
              <dd>(cMediaChannel only) source height.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String,System.Int32,System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">Applies to all agents in this scene.  NOTE: this will cause a few seconds of silence while restarting the stream.  To avoid silence, assign URLs in your SceneSettings instead, which will pre-load them.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.OverrideMediaSource(System.String,System.Int32,System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.PerformMediaAction(Sansar.Simulation.MediaAction)">PerformMediaAction Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.PerformMediaAction(Sansar.Simulation.MediaAction):member">
          <div class="msummary">Performs a specific action on the current media surface.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>PerformMediaAction</b> (<a href="../Sansar.Simulation/MediaAction.html">MediaAction</a> action)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.PerformMediaAction(Sansar.Simulation.MediaAction):Parameters">
            <dl>
              <dt>
                <i>action</i>
              </dt>
              <dd>The action to perform.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.PerformMediaAction(Sansar.Simulation.MediaAction):Remarks">Applies to this agent only.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.PerformMediaAction(Sansar.Simulation.MediaAction):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.PerformMediaAction(Sansar.Simulation.MediaAction,System.Action{Sansar.Script.OperationCompleteEvent})">PerformMediaAction Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.PerformMediaAction(Sansar.Simulation.MediaAction,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Performs a specific action on the current media surface.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>PerformMediaAction</b> (<a href="../Sansar.Simulation/MediaAction.html">MediaAction</a> action, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.PerformMediaAction(Sansar.Simulation.MediaAction,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>action</i>
              </dt>
              <dd>The action to perform.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.PerformMediaAction(Sansar.Simulation.MediaAction,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">Applies to all agents currently in the scene.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.PerformMediaAction(Sansar.Simulation.MediaAction,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.PlaySound(Sansar.Simulation.SoundResource,Sansar.Simulation.PlaySettings)">PlaySound Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.PlaySound(Sansar.Simulation.SoundResource,Sansar.Simulation.PlaySettings):member">
          <div class="msummary">Play sound to direct output.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/PlayHandle.html">PlayHandle</a> <b>PlaySound</b> (<a href="../Sansar.Simulation/SoundResource.html">SoundResource</a> soundResource, <a href="../Sansar.Simulation/PlaySettings.html">PlaySettings</a> playSettings)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.PlaySound(Sansar.Simulation.SoundResource,Sansar.Simulation.PlaySettings):Parameters">
            <dl>
              <dt>
                <i>soundResource</i>
              </dt>
              <dd>The sound resource to play.</dd>
              <dt>
                <i>playSettings</i>
              </dt>
              <dd>The play parameters.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.PlaySound(Sansar.Simulation.SoundResource,Sansar.Simulation.PlaySettings):Returns">The play handle for controlling playback.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.PlaySound(Sansar.Simulation.SoundResource,Sansar.Simulation.PlaySettings):Remarks">Plays for all agents in this scene.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.PlaySound(Sansar.Simulation.SoundResource,Sansar.Simulation.PlaySettings):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.PlaySoundAtPosition(Sansar.Simulation.SoundResource,Sansar.Vector,Sansar.Simulation.PlaySettings)">PlaySoundAtPosition Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.PlaySoundAtPosition(Sansar.Simulation.SoundResource,Sansar.Vector,Sansar.Simulation.PlaySettings):member">
          <div class="msummary">Play sound at specified position.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/PlayHandle.html">PlayHandle</a> <b>PlaySoundAtPosition</b> (<a href="../Sansar.Simulation/SoundResource.html">SoundResource</a> soundResource, <a href="../Sansar/Vector.html">Sansar.Vector</a> position, <a href="../Sansar.Simulation/PlaySettings.html">PlaySettings</a> playSettings)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.PlaySoundAtPosition(Sansar.Simulation.SoundResource,Sansar.Vector,Sansar.Simulation.PlaySettings):Parameters">
            <dl>
              <dt>
                <i>soundResource</i>
              </dt>
              <dd>The sound resource to play.</dd>
              <dt>
                <i>position</i>
              </dt>
              <dd>The absolute position.</dd>
              <dt>
                <i>playSettings</i>
              </dt>
              <dd>The play parameters.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.PlaySoundAtPosition(Sansar.Simulation.SoundResource,Sansar.Vector,Sansar.Simulation.PlaySettings):Returns">The play handle for controlling playback.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.PlaySoundAtPosition(Sansar.Simulation.SoundResource,Sansar.Vector,Sansar.Simulation.PlaySettings):Remarks">Plays for all agents in this scene.  By setting an absolute position, the location of this sound will be static.  If you want the sound to move, you must play the sound on an audio component and move the audio component.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.PlaySoundAtPosition(Sansar.Simulation.SoundResource,Sansar.Vector,Sansar.Simulation.PlaySettings):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.PlayStream(Sansar.Simulation.StreamChannel,System.Single)">PlayStream Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.PlayStream(Sansar.Simulation.StreamChannel,System.Single):member">
          <div class="msummary">Play web audio stream to direct output.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/PlayHandle.html">PlayHandle</a> <b>PlayStream</b> (<a href="../Sansar.Simulation/StreamChannel.html">StreamChannel</a> streamChannel, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> loudness)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.PlayStream(Sansar.Simulation.StreamChannel,System.Single):Parameters">
            <dl>
              <dt>
                <i>streamChannel</i>
              </dt>
              <dd>Channel of the audio stream to play.</dd>
              <dt>
                <i>loudness</i>
              </dt>
              <dd>Relative loudness in dB.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.PlayStream(Sansar.Simulation.StreamChannel,System.Single):Returns">The play handle for controlling playback.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.PlayStream(Sansar.Simulation.StreamChannel,System.Single):Remarks">Plays for all agents in this scene.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.PlayStream(Sansar.Simulation.StreamChannel,System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.PlayStreamAtPosition(Sansar.Simulation.StreamChannel,Sansar.Vector,System.Single)">PlayStreamAtPosition Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.PlayStreamAtPosition(Sansar.Simulation.StreamChannel,Sansar.Vector,System.Single):member">
          <div class="msummary">Play web audio stream at specified position.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/PlayHandle.html">PlayHandle</a> <b>PlayStreamAtPosition</b> (<a href="../Sansar.Simulation/StreamChannel.html">StreamChannel</a> streamChannel, <a href="../Sansar/Vector.html">Sansar.Vector</a> position, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> loudness)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.PlayStreamAtPosition(Sansar.Simulation.StreamChannel,Sansar.Vector,System.Single):Parameters">
            <dl>
              <dt>
                <i>streamChannel</i>
              </dt>
              <dd>Channel of the audio stream to play.</dd>
              <dt>
                <i>position</i>
              </dt>
              <dd>The absolute position.</dd>
              <dt>
                <i>loudness</i>
              </dt>
              <dd>Relative loudness in dB.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.PlayStreamAtPosition(Sansar.Simulation.StreamChannel,Sansar.Vector,System.Single):Returns">The play handle for controlling playback.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.PlayStreamAtPosition(Sansar.Simulation.StreamChannel,Sansar.Vector,System.Single):Remarks">Plays for all agents in this scene.  By setting an absolute position, the location of this sound will be static.  If you want the sound to move, you must play the sound on an audio component and move the audio component.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.PlayStreamAtPosition(Sansar.Simulation.StreamChannel,Sansar.Vector,System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ScenePrivate.Reactions">Reactions Property</h3>
        <blockquote id="P:Sansar.Simulation.ScenePrivate.Reactions:member">
          <div class="msummary">
            Gets the Reactions interface for this Scene
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/Reactions.html">Reactions</a> <b>Reactions</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ScenePrivate.Reactions:Value">The Chat Api</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.Reactions:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.Reactions:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.ResetScene()">ResetScene Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.ResetScene():member">
          <div class="msummary">
            Restarts the Scene.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface(Restricted=true)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>ResetScene</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.ResetScene():Remarks">All clients will be momentarily disconnected.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.ResetScene():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ScenePrivate.SceneInfo">SceneInfo Property</h3>
        <blockquote id="P:Sansar.Simulation.ScenePrivate.SceneInfo:member">
          <div class="msummary">
            Gets the SceneInfo for this Scene
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/SceneInfo.html">SceneInfo</a> <b>SceneInfo</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ScenePrivate.SceneInfo:Value">The full SceneInfo Api</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.SceneInfo:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.SceneInfo:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single)">SetGravity Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single):member">
          <div class="msummary">Sets the scene's current gravity acceleration in m/s^2.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetGravity</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> scale)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single):Parameters">
            <dl>
              <dt>
                <i>scale</i>
              </dt>
              <dd>The gravity magnitude.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single):Returns">The return value is an internal event id which can be passed to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> from a coroutine for synchronous behavior.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single):Remarks">Set to <a href="../Sansar.Simulation/ScenePrivate.html#P:Sansar.Simulation.ScenePrivate.DefaultGravity">ScenePrivate.DefaultGravity</a> to return to the default. The valid range is from <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.GravityMinimum">ScenePrivate.GravityMinimum</a> to <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.GravityMaximum">ScenePrivate.GravityMaximum</a>. The direction cannot be changed.See GravityExample.cs in the Examples folder for more details.</div>
          <h2 class="Section">Example</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single):Example:1">
            <table class="CodeExampleTable">
              <tr>
                <td>
                  <b>
                    <font size="-1">C# Example</font>
                  </b>
                </td>
              </tr>
              <tr>
                <td>
                  <pre class="code-csharp">ScenePrivate.SetGravity(1.622f); // Set the scene to moon gravity</pre>
                </td>
              </tr>
            </table>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,Sansar.Script.ScriptBase.OperationComplete)">SetGravity Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">Sets the scene's current gravity acceleration in m/s^2.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetGravity</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> scale, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>scale</i>
              </dt>
              <dd>The gravity magnitude.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,Sansar.Script.ScriptBase.OperationComplete):Returns">The return value is an internal event id which can be passed to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> from a coroutine for synchronous behavior.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,Sansar.Script.ScriptBase.OperationComplete):Remarks">Set to <a href="../Sansar.Simulation/ScenePrivate.html#P:Sansar.Simulation.ScenePrivate.DefaultGravity">ScenePrivate.DefaultGravity</a> to return to the default. The valid range is from <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.GravityMinimum">ScenePrivate.GravityMinimum</a> to <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.GravityMaximum">ScenePrivate.GravityMaximum</a>. The direction cannot be changed.See GravityExample.cs in the Examples folder for more details.</div>
          <h2 class="Section">Example</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,Sansar.Script.ScriptBase.OperationComplete):Example:1">
            <table class="CodeExampleTable">
              <tr>
                <td>
                  <b>
                    <font size="-1">C# Example</font>
                  </b>
                </td>
              </tr>
              <tr>
                <td>
                  <pre class="code-csharp">ScenePrivate.SetGravity(1.622f); // Set the scene to moon gravity</pre>
                </td>
              </tr>
            </table>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,System.Action{Sansar.Script.OperationCompleteEvent})">SetGravity Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the scene's current gravity acceleration in m/s^2.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetGravity</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> scale, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>scale</i>
              </dt>
              <dd>The gravity magnitude.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">The return value is an internal event id which can be passed to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor">Sansar.Script.ScriptBase.WaitFor</a> from a coroutine for synchronous behavior.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">Set to <a href="../Sansar.Simulation/ScenePrivate.html#P:Sansar.Simulation.ScenePrivate.DefaultGravity">ScenePrivate.DefaultGravity</a> to return to the default. The valid range is from <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.GravityMinimum">ScenePrivate.GravityMinimum</a> to <a href="../Sansar.Simulation/ScenePrivate.html#F:Sansar.Simulation.ScenePrivate.GravityMaximum">ScenePrivate.GravityMaximum</a>. The direction cannot be changed.See GravityExample.cs in the Examples folder for more details.</div>
          <h2 class="Section">Example</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Example:1">
            <table class="CodeExampleTable">
              <tr>
                <td>
                  <b>
                    <font size="-1">C# Example</font>
                  </b>
                </td>
              </tr>
              <tr>
                <td>
                  <pre class="code-csharp">ScenePrivate.SetGravity(1.622f); // Set the scene to moon gravity</pre>
                </td>
              </tr>
            </table>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetGravity(System.Single,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.SetMegaphone(Sansar.Simulation.AgentPrivate,System.Boolean)">SetMegaphone Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.SetMegaphone(Sansar.Simulation.AgentPrivate,System.Boolean):member">
          <div class="msummary">Sets the megaphone status of the user.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetMegaphone</b> (<a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a> agent, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> enable)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.SetMegaphone(Sansar.Simulation.AgentPrivate,System.Boolean):Parameters">
            <dl>
              <dt>
                <i>agent</i>
              </dt>
              <dd>Agent to broadcast.</dd>
              <dt>
                <i>enable</i>
              </dt>
              <dd>If true, user's voice will be heard by everyone in the scene.  If false, user's voice will be spatialized normally.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetMegaphone(Sansar.Simulation.AgentPrivate,System.Boolean):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetMegaphone(Sansar.Simulation.AgentPrivate,System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.SetMegaphone(Sansar.Simulation.AgentPrivate,System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent})">SetMegaphone Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.SetMegaphone(Sansar.Simulation.AgentPrivate,System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">Sets the megaphone status of the user.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetMegaphone</b> (<a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a> agent, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> enable, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.SetMegaphone(Sansar.Simulation.AgentPrivate,System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>agent</i>
              </dt>
              <dd>Agent to broadcast.</dd>
              <dt>
                <i>enable</i>
              </dt>
              <dd>If true, user's voice will be heard by everyone in the scene.  If false, user's voice will be spatialized normally.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetMegaphone(Sansar.Simulation.AgentPrivate,System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.SetMegaphone(Sansar.Simulation.AgentPrivate,System.Boolean,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.ToString()">ToString Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.ToString():member">
          <div class="msummary"> A string representation of this object. </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.ToString():Returns">A string representation of this object.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.ToString():Remarks">The format of this string may change between releases.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.ToString():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ScenePrivate.User">User Property</h3>
        <blockquote id="P:Sansar.Simulation.ScenePrivate.User:member">
          <div class="msummary">
            Gets the User interface for this Scene
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/User.html">User</a> <b>User</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ScenePrivate.User:Value">The User Api</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.User:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePrivate.User:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStart(Sansar.Simulation.AgentPrivate)">VoiceBroadcastStart Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStart(Sansar.Simulation.AgentPrivate):member">
          <div class="msummary">Start broadcasting agent's voice to the voice broadcast channel.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. use ScenePrivate.SetMegaphone(agent, true) instead", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>VoiceBroadcastStart</b> (<a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a> agent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStart(Sansar.Simulation.AgentPrivate):Parameters">
            <dl>
              <dt>
                <i>agent</i>
              </dt>
              <dd>Agent to broadcast.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStart(Sansar.Simulation.AgentPrivate):Remarks">Plays for all agents in this scene.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStart(Sansar.Simulation.AgentPrivate):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStop(Sansar.Simulation.AgentPrivate)">VoiceBroadcastStop Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStop(Sansar.Simulation.AgentPrivate):member">
          <div class="msummary">Stop broadcasting agent's voice to voice broadcast channel.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. use ScenePrivate.SetMegaphone(agent, false) instead", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>VoiceBroadcastStop</b> (<a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a> agent)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStop(Sansar.Simulation.AgentPrivate):Parameters">
            <dl>
              <dt>
                <i>agent</i>
              </dt>
              <dd>Agent to stop broadcasting.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStop(Sansar.Simulation.AgentPrivate):Remarks">Stops for all agents in this scene.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStop(Sansar.Simulation.AgentPrivate):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStopAll()">VoiceBroadcastStopAll Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStopAll():member">
          <div class="msummary">Stop broadcasting all voices to voice broadcast channel.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. use AgentPrivate.SetMegaphone(false) instead", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>VoiceBroadcastStopAll</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStopAll():Remarks">Stops for all agents in this scene.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePrivate.VoiceBroadcastStopAll():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>