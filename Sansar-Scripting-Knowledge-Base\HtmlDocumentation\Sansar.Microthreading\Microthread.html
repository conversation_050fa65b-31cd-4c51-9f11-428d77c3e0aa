<html>
  <head>
    <title>Sansar.Microthreading.Microthread</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Script</a> : <a href="index.html">Sansar.Microthreading Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Microthreading.Microthread">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Microthreading.Microthread:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Microthreading.Microthread:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Microthreading.Microthread:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Microthreading.Microthread">Microthread  Class</h1>
    <p class="Summary" id="T:Sansar.Microthreading.Microthread:Summary">
            Internal class used to manage scheduling of tasks
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Microthreading.Microthread:Signature">public class  <b>Microthread</b> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.IDisposable">IDisposable</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Microthreading.Microthread:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Microthreading.Microthread:Docs:Remarks">
        <span class="NotEntered">Documentation for this section has not yet been entered.</span>
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Microthreading.Microthread:Docs:Version Information">
        <b>Namespace: </b>Sansar.Microthreading<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>.
							</p>
        <h2 class="Section">Public Constructors</h2>
        <div class="SectionBox" id="Public Constructors">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <div>
                    <b>
                      <a href="#C:Sansar.Microthreading.Microthread()">Microthread</a>
                    </b>()</div>
                </td>
                <td>
                  <span class="NotEntered">Documentation for this section has not yet been entered.</span>
                </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Protected Fields</h2>
        <div class="SectionBox" id="Protected Fields">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Microthreading.Microthread.ThreadState">ThreadState</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Microthreading/Microthread+State.html">Sansar.Microthreading.Microthread.State</a>
                  </i>. 
            The current state of this thread.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Microthreading.Microthread.Dispose()">Dispose</a>
                  </b>()<blockquote>
            Disposes resources used by this microthread.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Microthreading.Microthread.GetCurrentThreadTicks()">GetCurrentThreadTicks</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int64">long</a></nobr><blockquote>
            Returns the time when this thread will yield.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Microthreading.Microthread.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote><span class="NotEntered">Documentation for this section has not yet been entered.</span></blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Microthreading.Microthread.Yield()">Yield</a>
                  </b>()<blockquote>
            Explicitly yield the current thread or coroutine.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Microthreading.Microthread.YieldCurrentThread()">YieldCurrentThread</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int64">long</a></nobr><blockquote>
            Explicitly yield the current thread or coroutine.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded()">YieldIfQuantaExceeded</a>
                  </b>()<blockquote>
            Conditionally yields if the thread has exceeded its given runtime.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded(System.Int64)">YieldIfQuantaExceeded</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int64">long</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int64">long</a></nobr><blockquote>
            Conditionally yields if the thread has exceeded its given runtime.
            </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Microthreading.Microthread:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="C:Sansar.Microthreading.Microthread()">Microthread Constructor</h3>
        <blockquote id="C:Sansar.Microthreading.Microthread():member">
          <div class="msummary">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2>Syntax</h2>
          <div class="Signature">public  <b>Microthread</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="C:Sansar.Microthreading.Microthread():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="C:Sansar.Microthreading.Microthread():Version Information">
            <b>Namespace: </b>Sansar.Microthreading<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Microthreading.Microthread.Dispose()">Dispose Method</h3>
        <blockquote id="M:Sansar.Microthreading.Microthread.Dispose():member">
          <div class="msummary">
            Disposes resources used by this microthread.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public virtual <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Dispose</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.Dispose():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.Dispose():Version Information">
            <b>Namespace: </b>Sansar.Microthreading<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Microthreading.Microthread.GetCurrentThreadTicks()">GetCurrentThreadTicks Method</h3>
        <blockquote id="M:Sansar.Microthreading.Microthread.GetCurrentThreadTicks():member">
          <div class="msummary">
            Returns the time when this thread will yield.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int64">long</a> <b>GetCurrentThreadTicks</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Microthreading.Microthread.GetCurrentThreadTicks():Returns">The timestamp as returned by <a href="http://www.go-mono.com/docs/monodoc.ashx?link=M:System.Diagnostics.Stopwatch.GetTimestamp">System.Diagnostics.Stopwatch.GetTimestamp</a> when this thread will yield.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.GetCurrentThreadTicks():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.GetCurrentThreadTicks():Version Information">
            <b>Namespace: </b>Sansar.Microthreading<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Microthreading.Microthread.ThreadState">ThreadState Field</h3>
        <blockquote id="F:Sansar.Microthreading.Microthread.ThreadState:member">
          <div class="msummary">
            The current state of this thread.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected <a href="../Sansar.Microthreading/Microthread+State.html">Microthread.State</a> <b>ThreadState</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Microthreading.Microthread.ThreadState:Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Microthreading.Microthread.ThreadState:Version Information">
            <b>Namespace: </b>Sansar.Microthreading<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Microthreading.Microthread.ToString()">ToString Method</h3>
        <blockquote id="M:Sansar.Microthreading.Microthread.ToString():member">
          <div class="msummary">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2>Syntax</h2>
          <div class="Signature">public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Microthreading.Microthread.ToString():Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.ToString():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.ToString():Version Information">
            <b>Namespace: </b>Sansar.Microthreading<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Microthreading.Microthread.Yield()">Yield Method</h3>
        <blockquote id="M:Sansar.Microthreading.Microthread.Yield():member">
          <div class="msummary">
            Explicitly yield the current thread or coroutine.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Yield</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.Yield():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.Yield():Version Information">
            <b>Namespace: </b>Sansar.Microthreading<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Microthreading.Microthread.YieldCurrentThread()">YieldCurrentThread Method</h3>
        <blockquote id="M:Sansar.Microthreading.Microthread.YieldCurrentThread():member">
          <div class="msummary">
            Explicitly yield the current thread or coroutine.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int64">long</a> <b>YieldCurrentThread</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Microthreading.Microthread.YieldCurrentThread():Returns">Returns the current thread ticks as returned by <a href="../Sansar.Microthreading/Microthread.html#M:Sansar.Microthreading.Microthread.GetCurrentThreadTicks">Microthread.GetCurrentThreadTicks</a>.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.YieldCurrentThread():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.YieldCurrentThread():Version Information">
            <b>Namespace: </b>Sansar.Microthreading<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded()">YieldIfQuantaExceeded Method</h3>
        <blockquote id="M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded():member">
          <div class="msummary">
            Conditionally yields if the thread has exceeded its given runtime.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>YieldIfQuantaExceeded</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded():Version Information">
            <b>Namespace: </b>Sansar.Microthreading<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded(System.Int64)">YieldIfQuantaExceeded Method</h3>
        <blockquote id="M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded(System.Int64):member">
          <div class="msummary">
            Conditionally yields if the thread has exceeded its given runtime.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int64">long</a> <b>YieldIfQuantaExceeded</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int64">long</a> ticks)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded(System.Int64):Parameters">
            <dl>
              <dt>
                <i>ticks</i>
              </dt>
              <dd>
                <span class="NotEntered">Documentation for this section has not yet been entered.</span>
              </dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded(System.Int64):Returns">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded(System.Int64):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Microthreading.Microthread.YieldIfQuantaExceeded(System.Int64):Version Information">
            <b>Namespace: </b>Sansar.Microthreading<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>