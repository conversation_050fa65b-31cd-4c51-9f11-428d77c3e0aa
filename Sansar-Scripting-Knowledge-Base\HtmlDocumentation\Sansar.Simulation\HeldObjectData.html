<html>
  <head>
    <title>Sansar.Simulation.HeldObjectData</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.HeldObjectData">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.HeldObjectData:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.HeldObjectData:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.HeldObjectData:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.HeldObjectData">HeldObjectData  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.HeldObjectData:Summary">Handler for agent grab events.</p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.HeldObjectData:Signature">[Sansar.Script.Interface]<br />public class  <b>HeldObjectData</b> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.HeldObjectData:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.HeldObjectData:Docs:Remarks">
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.HeldObjectData:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a>.
							</p>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.HeldObjectData.ComponentId">ComponentId</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a>
                  </i>.  The ComponentId of the held object's <a href="../Sansar.Simulation/RigidBodyComponent.html">Sansar.Simulation.RigidBodyComponent</a>.</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.HeldObjectData.HeldObjectEvent">HeldObjectEvent</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/HeldObjectEventType.html">HeldObjectEventType</a>
                  </i>.  The type of held object event which occurred.</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.HeldObjectData.HeldObjectInfo">HeldObjectInfo</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/HeldObjectInfo.html">HeldObjectInfo</a>
                  </i>.  information about the held object</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.HeldObjectData.ObjectId">ObjectId</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a>
                  </i>.  The ObjectId of the held object.</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.HeldObjectData.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote> A string representation of this object. </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/EventData.html#M:Sansar.Script.EventData.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>
            Generates a string representation of the EventData.
             (<i>Inherited from <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a>.</i>)</blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.HeldObjectData:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="P:Sansar.Simulation.HeldObjectData.ComponentId">ComponentId Property</h3>
        <blockquote id="P:Sansar.Simulation.HeldObjectData.ComponentId:member">
          <div class="msummary"> The ComponentId of the held object's <a href="../Sansar.Simulation/RigidBodyComponent.html">Sansar.Simulation.RigidBodyComponent</a>.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Script/ComponentId.html">Sansar.Script.ComponentId</a> <b>ComponentId</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.HeldObjectData.ComponentId:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.HeldObjectData.ComponentId:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.HeldObjectData.ComponentId:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.HeldObjectData.HeldObjectEvent">HeldObjectEvent Property</h3>
        <blockquote id="P:Sansar.Simulation.HeldObjectData.HeldObjectEvent:member">
          <div class="msummary"> The type of held object event which occurred.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/HeldObjectEventType.html">HeldObjectEventType</a> <b>HeldObjectEvent</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.HeldObjectData.HeldObjectEvent:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.HeldObjectData.HeldObjectEvent:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.HeldObjectData.HeldObjectEvent:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.HeldObjectData.HeldObjectInfo">HeldObjectInfo Property</h3>
        <blockquote id="P:Sansar.Simulation.HeldObjectData.HeldObjectInfo:member">
          <div class="msummary"> information about the held object</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/HeldObjectInfo.html">HeldObjectInfo</a> <b>HeldObjectInfo</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.HeldObjectData.HeldObjectInfo:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.HeldObjectData.HeldObjectInfo:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.HeldObjectData.HeldObjectInfo:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.HeldObjectData.ObjectId">ObjectId Property</h3>
        <blockquote id="P:Sansar.Simulation.HeldObjectData.ObjectId:member">
          <div class="msummary"> The ObjectId of the held object.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a> <b>ObjectId</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.HeldObjectData.ObjectId:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.HeldObjectData.ObjectId:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.HeldObjectData.ObjectId:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.HeldObjectData.ToString()">ToString Method</h3>
        <blockquote id="M:Sansar.Simulation.HeldObjectData.ToString():member">
          <div class="msummary"> A string representation of this object. </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.HeldObjectData.ToString():Returns">A string representation of this object.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.HeldObjectData.ToString():Remarks">The format of this string may change between releases.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.HeldObjectData.ToString():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>