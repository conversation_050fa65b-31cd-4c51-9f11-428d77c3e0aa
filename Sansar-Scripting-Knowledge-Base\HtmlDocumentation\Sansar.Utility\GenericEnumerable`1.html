<html>
  <head>
    <title>Sansar.Utility.GenericEnumerable&lt;T&gt;</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Script</a> : <a href="index.html">Sansar.Utility Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Utility.GenericEnumerable`1">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Utility.GenericEnumerable`1:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Utility.GenericEnumerable`1:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Utility.GenericEnumerable`1:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Utility.GenericEnumerable`1">GenericEnumerable&lt;T&gt; Generic Class</h1>
    <p class="Summary" id="T:Sansar.Utility.GenericEnumerable`1:Summary">
            The GenericEnumerable takes a delegate which accesses items in a Array-like collection by index and allows iteration through enumeration.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Utility.GenericEnumerable`1:Signature">public class  <b>GenericEnumerable&lt;T&gt;</b> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerable`1">IEnumerable&lt;T&gt;</a><br /> where T : class</div>
    </div>
    <div class="Remarks" id="T:Sansar.Utility.GenericEnumerable`1:Docs">
      <h4 class="Subsection">Type Parameters</h4>
      <blockquote class="SubsectionBox" id="T:Sansar.Utility.GenericEnumerable`1:Docs:Type Parameters">
        <dl>
          <dt>
            <i>T</i>
          </dt>
          <dd>The type of objects returned by <a href="../Sansar.Utility/GenericEnumerable`1+GetItem.html">Sansar.Utility.GenericEnumerable`1.GetItem</a></dd>
        </dl>
      </blockquote>
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Utility.GenericEnumerable`1:Docs:Remarks">This is used by some API methods to allow for enumerations on some colletions.</div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Utility.GenericEnumerable`1:Docs:Version Information">
        <b>Namespace: </b>Sansar.Utility<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>.
							</p>
        <h2 class="Section">Public Constructors</h2>
        <div class="SectionBox" id="Public Constructors">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <div>
                    <b>
                      <a href="#C:Sansar.Utility.GenericEnumerable`1(System.UInt32,Sansar.Utility.GenericEnumerable{`0}.GetItem)">GenericEnumerable</a>
                    </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a>, <a href="../Sansar.Utility/GenericEnumerable`1+GetItem.html">GenericEnumerable&lt;T&gt;.GetItem</a>)</div>
                </td>
                <td>
            Initializes the enumerable with an item count and an accessor delegate.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Utility.GenericEnumerable`1.Count">Count</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a>
                  </i>. 
            The total number of items in the collection.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Utility.GenericEnumerable`1.GetEnumerator()">GetEnumerator</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerator`1">IEnumerator&lt;T&gt;</a></nobr><blockquote>
            Returns an enumerator that iterates through the collection.
            </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Explicitly Implemented Interface Members</h2>
        <div class="SectionBox" id="Explicitly Implemented Interface Members">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <a href="#M:Sansar.Utility.GenericEnumerable`1.System#Collections#IEnumerable#GetEnumerator()">
                    <b>IEnumerable.GetEnumerator</b>
                  </a>
                </td>
                <td>
            Returns an enumerator that iterates through the collection.
            </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Utility.GenericEnumerable`1:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="C:Sansar.Utility.GenericEnumerable`1(System.UInt32,Sansar.Utility.GenericEnumerable{`0}.GetItem)">GenericEnumerable Constructor</h3>
        <blockquote id="C:Sansar.Utility.GenericEnumerable`1(System.UInt32,Sansar.Utility.GenericEnumerable{`0}.GetItem):member">
          <div class="msummary">
            Initializes the enumerable with an item count and an accessor delegate.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public  <b>GenericEnumerable</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a> count, <a href="../Sansar.Utility/GenericEnumerable`1+GetItem.html">GenericEnumerable&lt;T&gt;.GetItem</a> getItem)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="C:Sansar.Utility.GenericEnumerable`1(System.UInt32,Sansar.Utility.GenericEnumerable{`0}.GetItem):Parameters">
            <dl>
              <dt>
                <i>count</i>
              </dt>
              <dd>Total number of items in the collection. </dd>
              <dt>
                <i>getItem</i>
              </dt>
              <dd>Delegate which retrieves an item from a collection by index.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="C:Sansar.Utility.GenericEnumerable`1(System.UInt32,Sansar.Utility.GenericEnumerable{`0}.GetItem):Remarks">If the item count changes or items change index the enumeration may be invalidated.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="C:Sansar.Utility.GenericEnumerable`1(System.UInt32,Sansar.Utility.GenericEnumerable{`0}.GetItem):Version Information">
            <b>Namespace: </b>Sansar.Utility<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Utility.GenericEnumerable`1.Count">Count Property</h3>
        <blockquote id="P:Sansar.Utility.GenericEnumerable`1.Count:member">
          <div class="msummary">
            The total number of items in the collection.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a> <b>Count</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Utility.GenericEnumerable`1.Count:Value">The total number of items in the collection.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Utility.GenericEnumerable`1.Count:Remarks">Set by the constructor.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Utility.GenericEnumerable`1.Count:Version Information">
            <b>Namespace: </b>Sansar.Utility<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Utility.GenericEnumerable`1.GetEnumerator()">GetEnumerator Method</h3>
        <blockquote id="M:Sansar.Utility.GenericEnumerable`1.GetEnumerator():member">
          <div class="msummary">
            Returns an enumerator that iterates through the collection.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerator`1">IEnumerator&lt;T&gt;</a> <b>GetEnumerator</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Utility.GenericEnumerable`1.GetEnumerator():Returns">An enumerator that can be used to iterate through the collection.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Utility.GenericEnumerable`1.GetEnumerator():Remarks">If the item count changes or items change index the enumeration may be invalidated.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Utility.GenericEnumerable`1.GetEnumerator():Version Information">
            <b>Namespace: </b>Sansar.Utility<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Utility.GenericEnumerable`1.System#Collections#IEnumerable#GetEnumerator()">System.Collections.IEnumerable.GetEnumerator Method</h3>
        <blockquote id="M:Sansar.Utility.GenericEnumerable`1.System#Collections#IEnumerable#GetEnumerator():member">
          <div class="msummary">
            Returns an enumerator that iterates through the collection.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">
            <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.IEnumerator">IEnumerator</a> <b>System.Collections.IEnumerable.GetEnumerator</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Utility.GenericEnumerable`1.System#Collections#IEnumerable#GetEnumerator():Returns">An enumerator that can be used to iterate through the collection.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Utility.GenericEnumerable`1.System#Collections#IEnumerable#GetEnumerator():Remarks">If the item count changes or items change index the enumeration may be invalidated.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Utility.GenericEnumerable`1.System#Collections#IEnumerable#GetEnumerator():Version Information">
            <b>Namespace: </b>Sansar.Utility<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>