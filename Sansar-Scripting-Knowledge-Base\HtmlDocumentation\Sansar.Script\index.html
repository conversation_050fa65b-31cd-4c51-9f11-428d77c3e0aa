<html>
  <head>
    <title>Sansar Script API: Sansar.Script</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar Script API</a>
    </div>
    <h1 class="PageTitle">Sansar.Script Namespace</h1>
    <p class="Summary">
    </p>
    <div>
    </div>
    <div class="Remarks">
      <h2 class="Section"> Namespace</h2>
      <p>
        <span class="NotEntered">Documentation for this section has not yet been entered.</span>
      </p>
      <table class="TypesListing" style="margin-top: 1em">
        <tr>
          <th>Type</th>
          <th>Description</th>
        </tr>
        <tr valign="top">
          <td>
            <a href="./AddEntryAttribute.html">AddEntryAttribute</a>
          </td>
          <td>
            A
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./CancelData.html">CancelData</a>
          </td>
          <td>
            The event is sent when a subscription is canceled.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./ComponentId.html">ComponentId</a>
          </td>
          <td>
            Encapsulates a Component Id.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./CoroutineException.html">CoroutineException</a>
          </td>
          <td>
            An exception occurred relating to Coroutine execution.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./DefaultScriptAttribute.html">DefaultScriptAttribute</a>
          </td>
          <td>
            Tags a class to be the default selection for this script project.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./DefaultValueAttribute.html">DefaultValueAttribute</a>
          </td>
          <td>
             Set a default value to show in the script properties field.
             </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./DisplayNameAttribute.html">DisplayNameAttribute</a>
          </td>
          <td>
            Set the name to be shown in script properties for this field.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./EditorVisibleAttribute.html">EditorVisibleAttribute</a>
          </td>
          <td>
            Explicitly set the editor visibility of a script field or class.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./EntriesAttribute.html">EntriesAttribute</a>
          </td>
          <td>
            List of entries to populate IList or IDictionary properties with.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./ErrorModeAttribute.html">ErrorModeAttribute</a>
          </td>
          <td>
            Set the Api Error mode for this script.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./EventData.html">EventData</a>
          </td>
          <td>
            Base class for events.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./ICoroutine.html">ICoroutine</a>
          </td>
          <td>
            Token representing a coroutine.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./IEventSubscription.html">IEventSubscription</a>
          </td>
          <td>
            Token representing an event subscription.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./InstanceInterface.html">InstanceInterface</a>
          </td>
          <td>
            Base class for all Sansar Script Object Oriented C# interfaces.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./LockedAttribute.html">LockedAttribute</a>
          </td>
          <td>
            Locks the IDictionary or IList properties so entries can not be added or removed.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./Log.html">Log</a>
          </td>
          <td>The Log class handles script logging and error reporting</td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./Log+Message.html">Log.Message</a>
          </td>
          <td>
            Represents a single console message.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./LogLevel.html">LogLevel</a>
          </td>
          <td>
            Used by <a href="../Sansar.Script/Log.html">Sansar.Script.Log</a> to filter messages.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./MaxEntriesAttribute.html">MaxEntriesAttribute</a>
          </td>
          <td>
            Set the Maximum number of array values allowed.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./Memory.html">Memory</a>
          </td>
          <td>The Memory class reports on script pool memory use.</td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./Memory+SubscriptionHandler.html">Memory.SubscriptionHandler</a>
          </td>
          <td>Notifies scripts about memory events.</td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./MemoryData.html">MemoryData</a>
          </td>
          <td>Notifies scripts about memory events.</td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./MemoryUseLevel.html">MemoryUseLevel</a>
          </td>
          <td>
            Used by <a href="../Sansar.Script/Memory.html">Sansar.Script.Memory</a> to report script memory use.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./MinEntriesAttribute.html">MinEntriesAttribute</a>
          </td>
          <td>
            Set the Minimum number of array values allowed.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./NonReflectiveAttribute.html">NonReflectiveAttribute</a>
          </td>
          <td>
            Prevents methods from being reflected using <a href="../Sansar.Script/Reflective.html#M:Sansar.Script.Reflective.AsInterface``1">Sansar.Script.Reflective.AsInterface``1</a>.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./ObjectId.html">ObjectId</a>
          </td>
          <td>
            Encapsulates an Object Id.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./OperationCompleteEvent.html">OperationCompleteEvent</a>
          </td>
          <td>
            Many asynchronous APIs will trigger an OperationCompleteEvent when done.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./RangeAttribute.html">RangeAttribute</a>
          </td>
          <td>
            Specify a range of valid values for the field in script properties.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./Reflective.html">Reflective</a>
          </td>
          <td>
            Base class which provides for simple reflection of methods through a given interface.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./Reflective+Context.html">Reflective.Context</a>
          </td>
          <td>
            Use with <a href="../Sansar.Script/RegisterReflectiveAttribute.html">Sansar.Script.RegisterReflectiveAttribute</a>  and <a href="../Sansar.Script/Reflective.html#M:Sansar.Script.Reflective.Register(Sansar.Script.Reflective.Context)">Sansar.Script.Reflective.Register(Sansar.Script.Reflective.Context)</a> to specify specific access context for the registered reflective class. 
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./Reflective+ReflectiveBase.html">Reflective.ReflectiveBase</a>
          </td>
          <td>Internal support class.</td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./RegisterReflectiveAttribute.html">RegisterReflectiveAttribute</a>
          </td>
          <td>
            Registers this class to be found via <a href="../Sansar.Simulation/ScenePrivate.html#M:Sansar.Simulation.ScenePrivate.FindReflective``1(System.String)">Sansar.Simulation.ScenePrivate.FindReflective``1(string)</a>.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./ScriptBase.html">ScriptBase</a>
          </td>
          <td>
            Base class for all scripts, has ScriptHandle accessors and coroutine utilities.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./ScriptBase+OperationComplete.html">ScriptBase.OperationComplete</a>
          </td>
          <td>
            Used to obtain notification that the operation has completed.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./ScriptBase+ScopedLock.html">ScriptBase.ScopedLock</a>
          </td>
          <td>
            Returned from <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Lock(System.Object)">Sansar.Script.ScriptBase.Lock(object)</a> for use in using statements.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./ScriptEventData.html">ScriptEventData</a>
          </td>
          <td>
            Result of a <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId, string, Sansar.Script.Reflective)</a> call.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./ScriptHandle.html">ScriptHandle</a>
          </td>
          <td>
            A running script in Sansar.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./ScriptId.html">ScriptId</a>
          </td>
          <td>
            Encapsulates an Script Id.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./SessionId.html">SessionId</a>
          </td>
          <td>
            Encapsulates a Session Id.
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./SimpleScriptEventData.html">SimpleScriptEventData</a>
          </td>
          <td>
            Used by <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object)">Sansar.Script.ScriptBase.PostSimpleScriptEvent(string, object)</a> to send simple script events.  
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./ThrottleException.html">ThrottleException</a>
          </td>
          <td>Thrown when a method is called too often</td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./Timer.html">Timer</a>
          </td>
          <td>
            The Timer class is a static class that manages timer events for a script. 
            </td>
        </tr>
        <tr valign="top">
          <td>
            <a href="./TooltipAttribute.html">TooltipAttribute</a>
          </td>
          <td>
            Set a helpful tooltip message to show in the editor.
            </td>
        </tr>
      </table>
    </div>
    <div class="Members">
    </div>
    <hr size="1" />
    <div class="Copyright">Copyright ©2018</div>
  </body>
</html>