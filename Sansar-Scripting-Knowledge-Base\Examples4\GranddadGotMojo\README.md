# Sansar
**C# Code for Project Sansar**

This code is all written to be used in modular scripting using Simple Script or custom Reflective events.  These scripts add to the Simple Scripts published by Linden Labs.  There is a .md file with documentation for each script listed in this folder.

**Action Audio Controller** - turn volume up and down using chat commands.

**Action Complex Mover** - Move and rotate objects.

**Action Light** - control disco lights using effects that change light color and intensity.

**Action Synth Module** - the guts of my synthesizers made for Sansar.

**Action Two Step Mover** - a script that moves an object based on commands.  Great for making interactive buttons.

**Camera** - a remote controlled cut action camera.  Great for Machinima or Streaming in Sansar.

**Custom Looper Controller** - the guts of my Looper used to create looped EDM Music in Sansar.

**Custom Security Sender** - a general user access list to send valid users to other scripts to restrict who can do what.

**Detect and Teleport People** - detects people with range of where this script lives and teleports them.

**Detect People** - detects people and reports them to other scripts.

**Display Modal Message From Trigger Volume** - displays a modal dialog with your custom messages when entering a trigger volume.

**Display Web Page Dialog from Trigger Volume** - displays a http url to a web site in chat when entering a trigger volume.

**gotMojo Media Screen** - the guts of my media screen.

**Hint** - displays a hint / message when mousing over an object the script is in.

**Letter Animation2** - a scripted using an animated mesh.  That displays letters based on messages on which letter to display.

**Letter Display Phrase** = a script that sends text typed in the chat window to the Letter Animation objects to display the text in world on these letter objects.

**Logic Sequence Checker** = a script that checks if a stream of inputs match a certain sequence.  Can be used as a pass code or password checking mechanism.

**Logic Sequence Player** - sends a series of outputs based a on a sequence you set.  Can be used to control music, lights, movement, etc.

**NPC Animation** - used to control multiple animations within an animated object.

**Timed Slide Show** - plays a series of slides based on timing signals.  Great for Karaoke.

**Trigger Complex Interaction** - this allows you to use a sinlge model with logical circular control surfaces to send messages based on the control surface that was interacted with.

**Trigger Complex Interaction Rectangle** - this allows you to use a single model with rectangular control surfaces to send messages based on the control surface that was interacted with.

**Trigger Key Pressed** - maps 40 keys from the PC Keyboard (10 number keys across top of keyboard, 10 keypad numbers, Shift key plus the 10 number keys across top of keyboard, Shift key plus the 10 keypad numbers) to send messages.

**Trigger Megaphone** - gives people within detection range the megaphone.

**Trigger Multiple Chat Commands** - an easy way to hook up a bunch of chat commands.

**Trigger Proximity Detector** - detects if an avatar is within a certain range of the object the script is placed in.  Sends a message when the Avatar is within this range and leaves the range.  The message contains the avatar's name.
