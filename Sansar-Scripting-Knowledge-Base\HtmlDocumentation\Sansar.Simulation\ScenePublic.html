<html>
  <head>
    <title>Sansar.Simulation.ScenePublic</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.ScenePublic">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.ScenePublic:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.ScenePublic:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.ScenePublic:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.ScenePublic">ScenePublic  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.ScenePublic:Summary">The Public Scene API, a more limited subset of <a href="../Sansar.Simulation/ScenePrivate.html">Sansar.Simulation.ScenePrivate</a>.</p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.ScenePublic:Signature">[Sansar.Script.Interface]<br />public class  <b>ScenePublic</b> : <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.ScenePublic:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.ScenePublic:Docs:Remarks">
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.ScenePublic:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.
							</p>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ScenePublic.AgentCount">AgentCount</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a>
                  </i>. The number of agents in the Scene.</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ScenePublic.Chat">Chat</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/Chat.html">Chat</a>
                  </i>. 
            Gets the Chat interface for this Scene
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/InstanceInterface.html#P:Sansar.Script.InstanceInterface.IsValid">IsValid</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            Whether or not this interface is valid.
             (<i>Inherited from <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ScenePublic.SceneInfo">SceneInfo</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/SceneInfo.html">SceneInfo</a>
                  </i>. 
            Gets the SceneInfo for this Scene
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.ScenePublic.User">User</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/User.html">User</a>
                  </i>. 
            Gets the User interface for this Scene
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.ObjectId)">FindAgent</a>
                  </b>(<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a>)<nobr> : <a href="../Sansar.Simulation/AgentPublic.html">AgentPublic</a></nobr><blockquote>
            Looks up an AgentInfo associated with the given object id.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.SessionId)">FindAgent</a>
                  </b>(<a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a>)<nobr> : <a href="../Sansar.Simulation/AgentPublic.html">AgentPublic</a></nobr><blockquote>
            Looks up an AgentInfo associated with the given session id, if they are in the scene.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePublic.FindAgent(System.Guid)">FindAgent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Guid">Guid</a>)<nobr> : <a href="../Sansar.Simulation/AgentPublic.html">AgentPublic</a></nobr><blockquote>
            Looks up an AgentInfo associated with the given persona id, if they are in the scene.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePublic.FindObject(Sansar.Script.ObjectId)">FindObject</a>
                  </b>(<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a>)<nobr> : <a href="../Sansar.Simulation/ObjectPublic.html">ObjectPublic</a></nobr><blockquote>
            Looks up a Object associated with the given object id.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePublic.FindReflective``1(System.String)">FindReflective&lt;TInterface&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerable`1">IEnumerable&lt;TInterface&gt;</a></nobr><blockquote>
            Looks up Reflective objects in the scene that match the interface type by class name.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePublic.GetAgent(System.UInt32)">GetAgent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a>)<nobr> : <a href="../Sansar.Simulation/AgentPublic.html">AgentPublic</a></nobr><blockquote>Get an <a href="../Sansar.Simulation/AgentPublic.html">Sansar.Simulation.AgentPublic</a> for a specific agent in the Scene.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePublic.GetAgents()">GetAgents</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerable`1">IEnumerable&lt;AgentPublic&gt;</a></nobr><blockquote>
            Returns the current list of Agents in the Scene.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePublic.GetPortalCreatorName(Sansar.Script.ObjectId)">GetPortalCreatorName</a>
                  </b>(<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>The name of the user who created the specified portal.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePublic.GetPortalDescription(Sansar.Script.ObjectId)">GetPortalDescription</a>
                  </b>(<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>Description for the specified portal.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePublic.GetPortalUri(Sansar.Script.ObjectId)">GetPortalUri</a>
                  </b>(<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>URI destination for the specified portal.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.ScenePublic.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote> A string representation of this object. </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.ScenePublic:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="P:Sansar.Simulation.ScenePublic.AgentCount">AgentCount Property</h3>
        <blockquote id="P:Sansar.Simulation.ScenePublic.AgentCount:member">
          <div class="msummary">The number of agents in the Scene.</div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a> <b>AgentCount</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ScenePublic.AgentCount:Value">Unsigned integer count of the number of agents in the Scene.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePublic.AgentCount:Remarks">This number changes when agents join and part.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePublic.AgentCount:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ScenePublic.Chat">Chat Property</h3>
        <blockquote id="P:Sansar.Simulation.ScenePublic.Chat:member">
          <div class="msummary">
            Gets the Chat interface for this Scene
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/Chat.html">Chat</a> <b>Chat</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ScenePublic.Chat:Value">The Chat Api</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePublic.Chat:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePublic.Chat:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.ObjectId)">FindAgent Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.ObjectId):member">
          <div class="msummary">
            Looks up an AgentInfo associated with the given object id.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/AgentPublic.html">AgentPublic</a> <b>FindAgent</b> (<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a> objectId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.ObjectId):Parameters">
            <dl>
              <dt>
                <i>objectId</i>
              </dt>
              <dd>The object to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.ObjectId):Returns">The AgentInfo, or null if an agent cannot be found with that id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.ObjectId):Remarks">Agents may leave at any time. Attempting to use an Agent or AgentInfo interface for an agent no longer in the scene will throw a NullReferenceException.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.ObjectId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.SessionId)">FindAgent Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.SessionId):member">
          <div class="msummary">
            Looks up an AgentInfo associated with the given session id, if they are in the scene.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/AgentPublic.html">AgentPublic</a> <b>FindAgent</b> (<a href="../Sansar.Script/SessionId.html">Sansar.Script.SessionId</a> sessionId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.SessionId):Parameters">
            <dl>
              <dt>
                <i>sessionId</i>
              </dt>
              <dd>The sessionId of the agent to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.SessionId):Returns">The AgentInfo, or null if an agent cannot be found with that id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.SessionId):Remarks">Agents may leave at any time. Attempting to use an Agent or AgentInfo interface for an agent no longer in the scene will throw a NullReferenceException.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.FindAgent(Sansar.Script.SessionId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePublic.FindAgent(System.Guid)">FindAgent Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePublic.FindAgent(System.Guid):member">
          <div class="msummary">
            Looks up an AgentInfo associated with the given persona id, if they are in the scene.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/AgentPublic.html">AgentPublic</a> <b>FindAgent</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Guid">Guid</a> personaId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.FindAgent(System.Guid):Parameters">
            <dl>
              <dt>
                <i>personaId</i>
              </dt>
              <dd>The personaId of the agent to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.FindAgent(System.Guid):Returns">The agent, or null if an agent cannot be found with that id in the scene.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.FindAgent(System.Guid):Remarks">Agents may leave at any time. Attempting to use an Agent or AgentInfo interface for an agent no longer in the scene will throw a NullReferenceException.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.FindAgent(System.Guid):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePublic.FindObject(Sansar.Script.ObjectId)">FindObject Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePublic.FindObject(Sansar.Script.ObjectId):member">
          <div class="msummary">
            Looks up a Object associated with the given object id.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/ObjectPublic.html">ObjectPublic</a> <b>FindObject</b> (<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a> objectId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.FindObject(Sansar.Script.ObjectId):Parameters">
            <dl>
              <dt>
                <i>objectId</i>
              </dt>
              <dd>The object to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.FindObject(Sansar.Script.ObjectId):Returns">The ObjectPublic, or null if a Object cannot be found with that id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.FindObject(Sansar.Script.ObjectId):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.FindObject(Sansar.Script.ObjectId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePublic.FindReflective``1(System.String)">FindReflective&lt;TInterface&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePublic.FindReflective``1(System.String):member">
          <div class="msummary">
            Looks up Reflective objects in the scene that match the interface type by class name.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[Sansar.Script.NonReflective]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerable`1">IEnumerable&lt;TInterface&gt;</a> <b>FindReflective&lt;TInterface&gt;</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> name)<br /> where TInterface : class</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.FindReflective``1(System.String):Type Parameters">
            <dl>
              <dt>
                <i>TInterface</i>
              </dt>
              <dd>The interface type to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.FindReflective``1(System.String):Parameters">
            <dl>
              <dt>
                <i>name</i>
              </dt>
              <dd>The type name of the object's class to find.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.FindReflective``1(System.String):Returns">An IEnumerable which contains all objects of the given type name that match the given interface.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.FindReflective``1(System.String):Remarks">The name given corresponds to the <a href="javascript:alert(&quot;Documentation not found.&quot;)">Type.FullName</a> of the object. Multiple scripts may define unrelated types of the same name, but only registered objects that match the interface of TInterface will be returned.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.FindReflective``1(System.String):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePublic.GetAgent(System.UInt32)">GetAgent Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePublic.GetAgent(System.UInt32):member">
          <div class="msummary">Get an <a href="../Sansar.Simulation/AgentPublic.html">Sansar.Simulation.AgentPublic</a> for a specific agent in the Scene.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/AgentPublic.html">AgentPublic</a> <b>GetAgent</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt32">uint</a> index)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.GetAgent(System.UInt32):Parameters">
            <dl>
              <dt>
                <i>index</i>
              </dt>
              <dd>The index of the agent to get.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.GetAgent(System.UInt32):Returns">Returns null if the index is larger than the number of agents in the Scene.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.GetAgent(System.UInt32):Remarks">Agents may appear at different indices as they join and part.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.GetAgent(System.UInt32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePublic.GetAgents()">GetAgents Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePublic.GetAgents():member">
          <div class="msummary">
            Returns the current list of Agents in the Scene.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IEnumerable`1">IEnumerable&lt;AgentPublic&gt;</a> <b>GetAgents</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.GetAgents():Returns">The current list of agents in the Scene</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.GetAgents():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.GetAgents():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePublic.GetPortalCreatorName(Sansar.Script.ObjectId)">GetPortalCreatorName Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePublic.GetPortalCreatorName(Sansar.Script.ObjectId):member">
          <div class="msummary">The name of the user who created the specified portal.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>GetPortalCreatorName</b> (<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a> objectId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.GetPortalCreatorName(Sansar.Script.ObjectId):Parameters">
            <dl>
              <dt>
                <i>objectId</i>
              </dt>
              <dd>
                <span class="NotEntered">Documentation for this section has not yet been entered.</span>
              </dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.GetPortalCreatorName(Sansar.Script.ObjectId):Returns">String</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.GetPortalCreatorName(Sansar.Script.ObjectId):Remarks">User who created the portal.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.GetPortalCreatorName(Sansar.Script.ObjectId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePublic.GetPortalDescription(Sansar.Script.ObjectId)">GetPortalDescription Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePublic.GetPortalDescription(Sansar.Script.ObjectId):member">
          <div class="msummary">Description for the specified portal.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>GetPortalDescription</b> (<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a> objectId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.GetPortalDescription(Sansar.Script.ObjectId):Parameters">
            <dl>
              <dt>
                <i>objectId</i>
              </dt>
              <dd>
                <span class="NotEntered">Documentation for this section has not yet been entered.</span>
              </dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.GetPortalDescription(Sansar.Script.ObjectId):Returns">String</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.GetPortalDescription(Sansar.Script.ObjectId):Remarks">The description of the portal.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.GetPortalDescription(Sansar.Script.ObjectId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePublic.GetPortalUri(Sansar.Script.ObjectId)">GetPortalUri Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePublic.GetPortalUri(Sansar.Script.ObjectId):member">
          <div class="msummary">URI destination for the specified portal.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>GetPortalUri</b> (<a href="../Sansar.Script/ObjectId.html">Sansar.Script.ObjectId</a> objectId)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.GetPortalUri(Sansar.Script.ObjectId):Parameters">
            <dl>
              <dt>
                <i>objectId</i>
              </dt>
              <dd>
                <span class="NotEntered">Documentation for this section has not yet been entered.</span>
              </dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.GetPortalUri(Sansar.Script.ObjectId):Returns">String</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.GetPortalUri(Sansar.Script.ObjectId):Remarks">The URI of the destination of the portal.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.GetPortalUri(Sansar.Script.ObjectId):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ScenePublic.SceneInfo">SceneInfo Property</h3>
        <blockquote id="P:Sansar.Simulation.ScenePublic.SceneInfo:member">
          <div class="msummary">
            Gets the SceneInfo for this Scene
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/SceneInfo.html">SceneInfo</a> <b>SceneInfo</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ScenePublic.SceneInfo:Value">The full SceneInfo Api</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePublic.SceneInfo:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePublic.SceneInfo:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.ScenePublic.ToString()">ToString Method</h3>
        <blockquote id="M:Sansar.Simulation.ScenePublic.ToString():member">
          <div class="msummary"> A string representation of this object. </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.ScenePublic.ToString():Returns">A string representation of this object.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.ToString():Remarks">The format of this string may change between releases.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.ScenePublic.ToString():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.ScenePublic.User">User Property</h3>
        <blockquote id="P:Sansar.Simulation.ScenePublic.User:member">
          <div class="msummary">
            Gets the User interface for this Scene
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/User.html">User</a> <b>User</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.ScenePublic.User:Value">The User Api</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePublic.User:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.ScenePublic.User:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>