<html>
  <head>
    <title>Sansar.Simulation.UIScoreBoard</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.UIScoreBoard">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.UIScoreBoard:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.UIScoreBoard:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.UIScoreBoard:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.UIScoreBoard">UIScoreBoard  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.UIScoreBoard:Summary">
            Manages a score board HUD element.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.UIScoreBoard:Signature">[Sansar.Script.Interface]<br />public class  <b>UIScoreBoard</b> : <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.UIScoreBoard:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.UIScoreBoard:Docs:Remarks">Used for competative games.</div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.UIScoreBoard:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.
							</p>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.UIScoreBoard.Id">Id</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>
                  </i>. 
            The board's ID.
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/InstanceInterface.html#P:Sansar.Script.InstanceInterface.IsValid">IsValid</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            Whether or not this interface is valid.
             (<i>Inherited from <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.UIScoreBoard.Score0">Score0</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. 
            The board's left score
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.UIScoreBoard.Score1">Score1</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. 
            The board's right score
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.UIScoreBoard.Paint(System.String,System.String,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color)">Paint</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar/Color.html">Sansar.Color</a>, <a href="../Sansar/Color.html">Sansar.Color</a>, <a href="../Sansar/Color.html">Sansar.Color</a>, <a href="../Sansar/Color.html">Sansar.Color</a>, <a href="../Sansar/Color.html">Sansar.Color</a>, <a href="../Sansar/Color.html">Sansar.Color</a>)<blockquote>
            Paint the score board
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.UIScoreBoard.Paint(System.String,System.String,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,System.Action{Sansar.Script.OperationCompleteEvent})">Paint</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar/Color.html">Sansar.Color</a>, <a href="../Sansar/Color.html">Sansar.Color</a>, <a href="../Sansar/Color.html">Sansar.Color</a>, <a href="../Sansar/Color.html">Sansar.Color</a>, <a href="../Sansar/Color.html">Sansar.Color</a>, <a href="../Sansar/Color.html">Sansar.Color</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>
            Paint the score board
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.UIScoreBoard.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote> A string representation of this object. </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.UIScoreBoard:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="P:Sansar.Simulation.UIScoreBoard.Id">Id Property</h3>
        <blockquote id="P:Sansar.Simulation.UIScoreBoard.Id:member">
          <div class="msummary">
            The board's ID.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>Id</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.UIScoreBoard.Id:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.UIScoreBoard.Id:Remarks">This ID is only unique to this user's particular session. It is not globally unique.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.UIScoreBoard.Id:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.UIScoreBoard.Paint(System.String,System.String,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color)">Paint Method</h3>
        <blockquote id="M:Sansar.Simulation.UIScoreBoard.Paint(System.String,System.String,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color):member">
          <div class="msummary">
            Paint the score board
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Paint</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> score0, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> score1, <a href="../Sansar/Color.html">Sansar.Color</a> colorScoreFg0, <a href="../Sansar/Color.html">Sansar.Color</a> colorScoreFg1, <a href="../Sansar/Color.html">Sansar.Color</a> colorScoreBg0, <a href="../Sansar/Color.html">Sansar.Color</a> colorScoreBg1, <a href="../Sansar/Color.html">Sansar.Color</a> colorFg, <a href="../Sansar/Color.html">Sansar.Color</a> colorBg)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.UIScoreBoard.Paint(System.String,System.String,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color):Parameters">
            <dl>
              <dt>
                <i>score0</i>
              </dt>
              <dd>Score of the left team.</dd>
              <dt>
                <i>score1</i>
              </dt>
              <dd>Score of the right team.</dd>
              <dt>
                <i>colorScoreFg0</i>
              </dt>
              <dd>The left score's text color.</dd>
              <dt>
                <i>colorScoreFg1</i>
              </dt>
              <dd>The right score's text color.</dd>
              <dt>
                <i>colorScoreBg0</i>
              </dt>
              <dd>The left score's background color.</dd>
              <dt>
                <i>colorScoreBg1</i>
              </dt>
              <dd>The right score's background color.</dd>
              <dt>
                <i>colorFg</i>
              </dt>
              <dd>The board's foreground color.</dd>
              <dt>
                <i>colorBg</i>
              </dt>
              <dd>The board's background color.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.UIScoreBoard.Paint(System.String,System.String,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.UIScoreBoard.Paint(System.String,System.String,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.UIScoreBoard.Paint(System.String,System.String,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,System.Action{Sansar.Script.OperationCompleteEvent})">Paint Method</h3>
        <blockquote id="M:Sansar.Simulation.UIScoreBoard.Paint(System.String,System.String,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Paint the score board
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Paint</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> score0, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> score1, <a href="../Sansar/Color.html">Sansar.Color</a> colorScoreFg0, <a href="../Sansar/Color.html">Sansar.Color</a> colorScoreFg1, <a href="../Sansar/Color.html">Sansar.Color</a> colorScoreBg0, <a href="../Sansar/Color.html">Sansar.Color</a> colorScoreBg1, <a href="../Sansar/Color.html">Sansar.Color</a> colorFg, <a href="../Sansar/Color.html">Sansar.Color</a> colorBg, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.UIScoreBoard.Paint(System.String,System.String,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>score0</i>
              </dt>
              <dd>Score of the left team.</dd>
              <dt>
                <i>score1</i>
              </dt>
              <dd>Score of the right team.</dd>
              <dt>
                <i>colorScoreFg0</i>
              </dt>
              <dd>The left score's text color.</dd>
              <dt>
                <i>colorScoreFg1</i>
              </dt>
              <dd>The right score's text color.</dd>
              <dt>
                <i>colorScoreBg0</i>
              </dt>
              <dd>The left score's background color.</dd>
              <dt>
                <i>colorScoreBg1</i>
              </dt>
              <dd>The right score's background color.</dd>
              <dt>
                <i>colorFg</i>
              </dt>
              <dd>The board's foreground color.</dd>
              <dt>
                <i>colorBg</i>
              </dt>
              <dd>The board's background color.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.UIScoreBoard.Paint(System.String,System.String,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.UIScoreBoard.Paint(System.String,System.String,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,Sansar.Color,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.UIScoreBoard.Score0">Score0 Property</h3>
        <blockquote id="P:Sansar.Simulation.UIScoreBoard.Score0:member">
          <div class="msummary">
            The board's left score
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>Score0</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.UIScoreBoard.Score0:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.UIScoreBoard.Score0:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.UIScoreBoard.Score0:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.UIScoreBoard.Score1">Score1 Property</h3>
        <blockquote id="P:Sansar.Simulation.UIScoreBoard.Score1:member">
          <div class="msummary">
            The board's right score
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[get: Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>Score1</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.UIScoreBoard.Score1:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.UIScoreBoard.Score1:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.UIScoreBoard.Score1:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.UIScoreBoard.ToString()">ToString Method</h3>
        <blockquote id="M:Sansar.Simulation.UIScoreBoard.ToString():member">
          <div class="msummary"> A string representation of this object. </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.UIScoreBoard.ToString():Returns">A string representation of this object.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.UIScoreBoard.ToString():Remarks">The format of this string may change between releases.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.UIScoreBoard.ToString():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>