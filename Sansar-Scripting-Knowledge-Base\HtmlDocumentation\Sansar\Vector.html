<html>
  <head>
    <title>Sansar.Vector</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Script</a> : <a href="index.html">Sansar Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Vector">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Vector:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Vector:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Vector:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Vector">Vector  Struct</h1>
    <p class="Summary" id="T:Sansar.Vector:Summary">
            The Vector class represents a 3 dimensional vector.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Vector:Signature">public struct  <b>Vector</b></div>
    </div>
    <div class="Remarks" id="T:Sansar.Vector:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Vector:Docs:Remarks">
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Vector:Docs:Version Information">
        <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.ValueType">ValueType</a>.
							</p>
        <h2 class="Section">Public Constructors</h2>
        <div class="SectionBox" id="Public Constructors">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <div>
                    <b>
                      <a href="#C:Sansar.Vector(Mono.Simd.Vector4f@)">Vector</a>
                    </b>(<i>ref</i> <a href="http://docs.go-mono.com/?link=T%3aMono.Simd.Vector4f">Mono.Simd.Vector4f</a>)</div>
                </td>
                <td>
            Creates a new vector with the supplied values.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <div>
                    <b>
                      <a href="#C:Sansar.Vector(System.Single)">Vector</a>
                    </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)</div>
                </td>
                <td>
            Creates a new vector with all values set to the given value.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <div>
                    <b>
                      <a href="#C:Sansar.Vector(System.Single,System.Single,System.Single,System.Single)">Vector</a>
                    </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)</div>
                </td>
                <td>
            Creates a new vector with the supplied values.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Fields</h2>
        <div class="SectionBox" id="Public Fields">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.Back">Back</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            Deprecated back vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.Down">Down</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            Deprecated down vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.Forward">Forward</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            Deprecated forward vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.Left">Left</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            Deprecated left vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.ObjectBack">ObjectBack</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            The default world back vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.ObjectDown">ObjectDown</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            The default world down vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.ObjectForward">ObjectForward</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            The default world forward vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.ObjectLeft">ObjectLeft</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            The default world left vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.ObjectRight">ObjectRight</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            The default world right vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.ObjectUp">ObjectUp</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            The default world up vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.One">One</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            A vector with all components 1.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.Right">Right</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            Deprecated right vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.Up">Up</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            Deprecated up Vector
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static readonly </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Vector.Zero">Zero</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar/Vector.html">Vector</a>
                  </i>. 
            A vector with all components 0.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                    <i>default property</i>
                  </div>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Vector.Item(System.Int32)">Item</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)</td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i>. 
            Allows getting coordinates by index.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Vector.W">W</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i>. 
            The W coordinate of the vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Vector.X">X</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i>. 
            The X coordinate of the vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Vector.Y">Y</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i>. 
            The Y coordinate of the vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Vector.Z">Z</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>
                  </i>. 
            The Z coordinate of the vector.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.AngleTo(Sansar.Vector@)">AngleTo</a>
                  </b>(<i>ref</i> <a href="../Sansar/Vector.html">Vector</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>
            Returns the angle in radians between this vector and the given vector.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.Cross(Sansar.Vector@)">Cross</a>
                  </b>(<i>ref</i> <a href="../Sansar/Vector.html">Vector</a>)<nobr> : <a href="../Sansar/Vector.html">Vector</a></nobr><blockquote>
            Calculates the cross product of 2 3D vectors.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.Dot(Sansar.Vector@)">Dot</a>
                  </b>(<i>ref</i> <a href="../Sansar/Vector.html">Vector</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>
            Performs a 3-component scalar or dot product.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.Dot3(Sansar.Vector@)">Dot3</a>
                  </b>(<i>ref</i> <a href="../Sansar/Vector.html">Vector</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>
            Performs a 3-component scalar or dot product.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.Dot4(Sansar.Vector@)">Dot4</a>
                  </b>(<i>ref</i> <a href="../Sansar/Vector.html">Vector</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>
            Performs a 4-component scalar or dot product.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.Length()">Length</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>
            Calculates the magnitude of this vector.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.LengthSquared()">LengthSquared</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a></nobr><blockquote>
            Calculates the square magnitude of this vector.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.Lerp(Sansar.Vector@,System.Single)">Lerp</a>
                  </b>(<i>ref</i> <a href="../Sansar/Vector.html">Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)<nobr> : <a href="../Sansar/Vector.html">Vector</a></nobr><blockquote>
            Performs a linear interpolation between two vectors.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.Normalized()">Normalized</a>
                  </b>()<nobr> : <a href="../Sansar/Vector.html">Vector</a></nobr><blockquote>
            Returns a vector with the same orientation and unit length.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.Orthonormalize(Sansar.Vector@,Sansar.Vector@)">Orthonormalize</a>
                  </b>(<i>ref</i> <a href="../Sansar/Vector.html">Vector</a>, <i>ref</i> <a href="../Sansar/Vector.html">Vector</a>)<nobr> : <a href="../Sansar/Vector.html">Vector</a></nobr><blockquote>
            Normalizes the two vectors and returns the normalized cross product.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.Parse(System.String)">Parse</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="../Sansar/Vector.html">Vector</a></nobr><blockquote>
            Parse a Vector from a string.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.Rotate(Sansar.Quaternion@)">Rotate</a>
                  </b>(<i>ref</i> <a href="../Sansar/Quaternion.html">Quaternion</a>)<nobr> : <a href="../Sansar/Vector.html">Vector</a></nobr><blockquote>
            Returns a new vector which is this vector rotated by the given quaternion.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>
            Generates a string representation of the vector.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.ToString(System.String)">ToString</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>
            Generates a string representation of the vector.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.ToString4(System.String)">ToString4</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>
            Generates a string representation of the vector, including the W parameter.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Vector.TryParse(System.String,Sansar.Vector@)">TryParse</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <i>out</i> <a href="../Sansar/Vector.html">Vector</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a></nobr><blockquote>
            Attempt to parse a Vector from a string
            </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Operators</h2>
        <div class="SectionBox" id="Public Operators">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td>
                  <b>
                    <a href="#M:Sansar.Vector.op_Addition(Sansar.Vector@,Sansar.Vector@)">Addition</a>
                  </b>(<i>ref</i> <a href="../Sansar/Vector.html">Vector</a>, <i>ref</i> <a href="../Sansar/Vector.html">Vector</a>)</td>
                <td>
            Performs vector addition.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td>
                  <b>
                    <a href="#M:Sansar.Vector.op_Division(Sansar.Vector@,System.Single)">Division</a>
                  </b>(<i>ref</i> <a href="../Sansar/Vector.html">Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)</td>
                <td>
            Divides vector components by a scalar.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td>
                  <b>
                    <a href="#M:Sansar.Vector.op_Multiply(Sansar.Vector@,System.Single)">Multiply</a>
                  </b>(<i>ref</i> <a href="../Sansar/Vector.html">Vector</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>)</td>
                <td>
            Performs a vector scalar multiplication.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td>
                  <b>
                    <a href="#M:Sansar.Vector.op_Multiply(System.Single,Sansar.Vector@)">Multiply</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a>, <i>ref</i> <a href="../Sansar/Vector.html">Vector</a>)</td>
                <td>
            Performs a vector scalar multiplication.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td>
                  <b>
                    <a href="#M:Sansar.Vector.op_Subtraction(Sansar.Vector@,Sansar.Vector@)">Subtraction</a>
                  </b>(<i>ref</i> <a href="../Sansar/Vector.html">Vector</a>, <i>ref</i> <a href="../Sansar/Vector.html">Vector</a>)</td>
                <td>
            Performs vector subtraction.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td>
                  <b>
                    <a href="#M:Sansar.Vector.op_Implicit(Mono.Simd.Vector4f@)~Sansar.Vector">Conversion to Sansar.Vector</a>
                  </b>(Implicit)</td>
                <td>
            Converts a Mono.Simd.Vector4f to a vector.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>static </div>
                </td>
                <td>
                  <b>
                    <a href="#M:Sansar.Vector.op_Implicit(Sansar.Vector@)~Mono.Simd.Vector4f">Conversion to Mono.Simd.Vector4f</a>
                  </b>(Implicit)</td>
                <td>
            Converts a vector to a Mono.Simd.Vector4f
            </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Vector:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="C:Sansar.Vector(Mono.Simd.Vector4f@)">Vector Constructor</h3>
        <blockquote id="C:Sansar.Vector(Mono.Simd.Vector4f@):member">
          <div class="msummary">
            Creates a new vector with the supplied values.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public  <b>Vector</b> ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="http://docs.go-mono.com/?link=T%3aMono.Simd.Vector4f">Mono.Simd.Vector4f</a> v)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="C:Sansar.Vector(Mono.Simd.Vector4f@):Parameters">
            <dl>
              <dt>
                <i>v</i>
              </dt>
              <dd>Initializes the vector from a <a href="http://docs.go-mono.com/?link=T%3aMono.Simd.Vector4f">Mono.Simd.Vector4f</a>. W should be 0 and is assumed to be 0 for most operations.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="C:Sansar.Vector(Mono.Simd.Vector4f@):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="C:Sansar.Vector(Mono.Simd.Vector4f@):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="C:Sansar.Vector(System.Single)">Vector Constructor</h3>
        <blockquote id="C:Sansar.Vector(System.Single):member">
          <div class="msummary">
            Creates a new vector with all values set to the given value.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public  <b>Vector</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> all)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="C:Sansar.Vector(System.Single):Parameters">
            <dl>
              <dt>
                <i>all</i>
              </dt>
              <dd>The value for all coordinates.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="C:Sansar.Vector(System.Single):Remarks">W will be set to 0.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="C:Sansar.Vector(System.Single):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="C:Sansar.Vector(System.Single,System.Single,System.Single,System.Single)">Vector Constructor</h3>
        <blockquote id="C:Sansar.Vector(System.Single,System.Single,System.Single,System.Single):member">
          <div class="msummary">
            Creates a new vector with the supplied values.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public  <b>Vector</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> x, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> y, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> z, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> w)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="C:Sansar.Vector(System.Single,System.Single,System.Single,System.Single):Parameters">
            <dl>
              <dt>
                <i>x</i>
              </dt>
              <dd>The x coordinate.</dd>
              <dt>
                <i>y</i>
              </dt>
              <dd>The y coordinate.</dd>
              <dt>
                <i>z</i>
              </dt>
              <dd>The z coordinate.</dd>
              <dt>
                <i>w</i>
              </dt>
              <dd>The w coordinate. W defaults to 0 and is assumed to be 0 for most operations.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="C:Sansar.Vector(System.Single,System.Single,System.Single,System.Single):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="C:Sansar.Vector(System.Single,System.Single,System.Single,System.Single):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.AngleTo(Sansar.Vector@)">AngleTo Method</h3>
        <blockquote id="M:Sansar.Vector.AngleTo(Sansar.Vector@):member">
          <div class="msummary">
            Returns the angle in radians between this vector and the given vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>AngleTo</b> ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.AngleTo(Sansar.Vector@):Parameters">
            <dl>
              <dt>
                <i>b</i>
              </dt>
              <dd>The angle to compare.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.AngleTo(Sansar.Vector@):Returns">The smallest angle in radians between the two vectors.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.AngleTo(Sansar.Vector@):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.AngleTo(Sansar.Vector@):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.Back">Back Field</h3>
        <blockquote id="F:Sansar.Vector.Back:member">
          <div class="msummary">
            Deprecated back vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[System.Obsolete("Use ObjectBack for correct back vector.", false)]<br />public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>Back</b> </div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="F:Sansar.Vector.Back:See Also">
            <div>
              <a href="../Sansar/Vector.html#F:Sansar.Vector.ObjectBack">Vector.ObjectBack</a>
            </div>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Back:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Back:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.Cross(Sansar.Vector@)">Cross Method</h3>
        <blockquote id="M:Sansar.Vector.Cross(Sansar.Vector@):member">
          <div class="msummary">
            Calculates the cross product of 2 3D vectors.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="../Sansar/Vector.html">Vector</a> <b>Cross</b> ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Cross(Sansar.Vector@):Parameters">
            <dl>
              <dt>
                <i>b</i>
              </dt>
              <dd>The second vector.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Cross(Sansar.Vector@):Returns">a X b</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Cross(Sansar.Vector@):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Cross(Sansar.Vector@):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.Dot(Sansar.Vector@)">Dot Method</h3>
        <blockquote id="M:Sansar.Vector.Dot(Sansar.Vector@):member">
          <div class="msummary">
            Performs a 3-component scalar or dot product.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>Dot</b> ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Dot(Sansar.Vector@):Parameters">
            <dl>
              <dt>
                <i>b</i>
              </dt>
              <dd>The second vector.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Dot(Sansar.Vector@):Returns">Returns X*b.X+Y*b.Y+Z*b.Z</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Dot(Sansar.Vector@):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Dot(Sansar.Vector@):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.Dot3(Sansar.Vector@)">Dot3 Method</h3>
        <blockquote id="M:Sansar.Vector.Dot3(Sansar.Vector@):member">
          <div class="msummary">
            Performs a 3-component scalar or dot product.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>Dot3</b> ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Dot3(Sansar.Vector@):Parameters">
            <dl>
              <dt>
                <i>b</i>
              </dt>
              <dd>The second vector.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Dot3(Sansar.Vector@):Returns">Returns X*b.X+Y*b.Y+Z*b.Z</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Dot3(Sansar.Vector@):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Dot3(Sansar.Vector@):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.Dot4(Sansar.Vector@)">Dot4 Method</h3>
        <blockquote id="M:Sansar.Vector.Dot4(Sansar.Vector@):member">
          <div class="msummary">
            Performs a 4-component scalar or dot product.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>Dot4</b> ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Dot4(Sansar.Vector@):Parameters">
            <dl>
              <dt>
                <i>b</i>
              </dt>
              <dd>The second vector.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Dot4(Sansar.Vector@):Returns">Returns X*b.X+Y*b.Y+Z*b.Z+W*b.W</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Dot4(Sansar.Vector@):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Dot4(Sansar.Vector@):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.Down">Down Field</h3>
        <blockquote id="F:Sansar.Vector.Down:member">
          <div class="msummary">
            Deprecated down vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[System.Obsolete("Use ObjectDown for correct down vector.", false)]<br />public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>Down</b> </div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="F:Sansar.Vector.Down:See Also">
            <div>
              <a href="../Sansar/Vector.html#F:Sansar.Vector.ObjectDown">Vector.ObjectDown</a>
            </div>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Down:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Down:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.Forward">Forward Field</h3>
        <blockquote id="F:Sansar.Vector.Forward:member">
          <div class="msummary">
            Deprecated forward vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[System.Obsolete("Use ObjectForward for correct forward vector.", false)]<br />public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>Forward</b> </div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="F:Sansar.Vector.Forward:See Also">
            <div>
              <a href="../Sansar/Vector.html#F:Sansar.Vector.ObjectForward">Vector.ObjectForward</a>
            </div>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Forward:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Forward:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Vector.Item(System.Int32)">Item Property</h3>
        <blockquote id="P:Sansar.Vector.Item(System.Int32):member">
          <div class="msummary">
            Allows getting coordinates by index.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">
            <p>
              <i>This is the default property for this class.</i>
            </p>public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> this [<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> index] { get; set; }</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Vector.Item(System.Int32):Parameters">
            <dl>
              <dt>
                <i>index</i>
              </dt>
              <dd>0=&gt;X, 1=&gt;Y, 2=&gt;Z, 3=&gt;W</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Vector.Item(System.Int32):Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Vector.Item(System.Int32):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Vector.Item(System.Int32):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.Left">Left Field</h3>
        <blockquote id="F:Sansar.Vector.Left:member">
          <div class="msummary">
            Deprecated left vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[System.Obsolete("Use ObjectLeft for correct left vector.", false)]<br />public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>Left</b> </div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="F:Sansar.Vector.Left:See Also">
            <div>
              <a href="../Sansar/Vector.html#F:Sansar.Vector.ObjectLeft">Vector.ObjectLeft</a>
            </div>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Left:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Left:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.Length()">Length Method</h3>
        <blockquote id="M:Sansar.Vector.Length():member">
          <div class="msummary">
            Calculates the magnitude of this vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>Length</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Length():Returns">The 3D vector length.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Length():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Length():Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.LengthSquared()">LengthSquared Method</h3>
        <blockquote id="M:Sansar.Vector.LengthSquared():member">
          <div class="msummary">
            Calculates the square magnitude of this vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>LengthSquared</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.LengthSquared():Returns">The square of the 3D vector length.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.LengthSquared():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.LengthSquared():Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.Lerp(Sansar.Vector@,System.Single)">Lerp Method</h3>
        <blockquote id="M:Sansar.Vector.Lerp(Sansar.Vector@,System.Single):member">
          <div class="msummary">
            Performs a linear interpolation between two vectors.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="../Sansar/Vector.html">Vector</a> <b>Lerp</b> ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> b, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> amount)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Lerp(Sansar.Vector@,System.Single):Parameters">
            <dl>
              <dt>
                <i>b</i>
              </dt>
              <dd>The second vector.</dd>
              <dt>
                <i>amount</i>
              </dt>
              <dd>Value from [0..1] indicating the weight for the second vector.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Lerp(Sansar.Vector@,System.Single):Returns">A vector that is linearly interpolated between the two sources.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Lerp(Sansar.Vector@,System.Single):Remarks">a + (b-a) * amount.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Lerp(Sansar.Vector@,System.Single):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.Normalized()">Normalized Method</h3>
        <blockquote id="M:Sansar.Vector.Normalized():member">
          <div class="msummary">
            Returns a vector with the same orientation and unit length.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="../Sansar/Vector.html">Vector</a> <b>Normalized</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Normalized():Returns">A vector with the same orientation and unit length.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Normalized():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Normalized():Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.ObjectBack">ObjectBack Field</h3>
        <blockquote id="F:Sansar.Vector.ObjectBack:member">
          <div class="msummary">
            The default world back vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>ObjectBack</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.ObjectBack:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.ObjectBack:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.ObjectDown">ObjectDown Field</h3>
        <blockquote id="F:Sansar.Vector.ObjectDown:member">
          <div class="msummary">
            The default world down vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>ObjectDown</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.ObjectDown:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.ObjectDown:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.ObjectForward">ObjectForward Field</h3>
        <blockquote id="F:Sansar.Vector.ObjectForward:member">
          <div class="msummary">
            The default world forward vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>ObjectForward</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.ObjectForward:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.ObjectForward:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.ObjectLeft">ObjectLeft Field</h3>
        <blockquote id="F:Sansar.Vector.ObjectLeft:member">
          <div class="msummary">
            The default world left vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>ObjectLeft</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.ObjectLeft:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.ObjectLeft:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.ObjectRight">ObjectRight Field</h3>
        <blockquote id="F:Sansar.Vector.ObjectRight:member">
          <div class="msummary">
            The default world right vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>ObjectRight</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.ObjectRight:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.ObjectRight:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.ObjectUp">ObjectUp Field</h3>
        <blockquote id="F:Sansar.Vector.ObjectUp:member">
          <div class="msummary">
            The default world up vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>ObjectUp</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.ObjectUp:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.ObjectUp:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.One">One Field</h3>
        <blockquote id="F:Sansar.Vector.One:member">
          <div class="msummary">
            A vector with all components 1.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>One</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.One:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.One:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.op_Addition(Sansar.Vector@,Sansar.Vector@)">op_Addition Method</h3>
        <blockquote id="M:Sansar.Vector.op_Addition(Sansar.Vector@,Sansar.Vector@):member">
          <div class="msummary">
            Performs vector addition.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="../Sansar/Vector.html">Vector</a> operator+ ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> a, [System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Addition(Sansar.Vector@,Sansar.Vector@):Parameters">
            <dl>
              <dt>
                <i>a</i>
              </dt>
              <dd>The first Vector.</dd>
              <dt>
                <i>b</i>
              </dt>
              <dd>The second Vector.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Addition(Sansar.Vector@,Sansar.Vector@):Returns">A new vector that is the sum of the arguments.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Addition(Sansar.Vector@,Sansar.Vector@):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Addition(Sansar.Vector@,Sansar.Vector@):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.op_Division(Sansar.Vector@,System.Single)">op_Division Method</h3>
        <blockquote id="M:Sansar.Vector.op_Division(Sansar.Vector@,System.Single):member">
          <div class="msummary">
            Divides vector components by a scalar.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="../Sansar/Vector.html">Vector</a> operator/ ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> a, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Division(Sansar.Vector@,System.Single):Parameters">
            <dl>
              <dt>
                <i>a</i>
              </dt>
              <dd>The vector.</dd>
              <dt>
                <i>b</i>
              </dt>
              <dd>The scalar.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Division(Sansar.Vector@,System.Single):Returns">Returns a new vector with value [a.X/b, a.Y/b, a.Z/b, a.W/b]</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Division(Sansar.Vector@,System.Single):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Division(Sansar.Vector@,System.Single):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.op_Implicit(Mono.Simd.Vector4f@)~Sansar.Vector">Conversion Method</h3>
        <blockquote id="M:Sansar.Vector.op_Implicit(Mono.Simd.Vector4f@)~Sansar.Vector:member">
          <div class="msummary">
            Converts a Mono.Simd.Vector4f to a vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static implicit operator <a href="../Sansar/Vector.html">Vector</a> ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="http://docs.go-mono.com/?link=T%3aMono.Simd.Vector4f">Mono.Simd.Vector4f</a> v)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Implicit(Mono.Simd.Vector4f@)~Sansar.Vector:Parameters">
            <dl>
              <dt>
                <i>v</i>
              </dt>
              <dd>The vector to convert</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Implicit(Mono.Simd.Vector4f@)~Sansar.Vector:Returns">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Implicit(Mono.Simd.Vector4f@)~Sansar.Vector:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Implicit(Mono.Simd.Vector4f@)~Sansar.Vector:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.op_Implicit(Sansar.Vector@)~Mono.Simd.Vector4f">Conversion Method</h3>
        <blockquote id="M:Sansar.Vector.op_Implicit(Sansar.Vector@)~Mono.Simd.Vector4f:member">
          <div class="msummary">
            Converts a vector to a Mono.Simd.Vector4f
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static implicit operator <a href="http://docs.go-mono.com/?link=T%3aMono.Simd.Vector4f">Mono.Simd.Vector4f</a> ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> v)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Implicit(Sansar.Vector@)~Mono.Simd.Vector4f:Parameters">
            <dl>
              <dt>
                <i>v</i>
              </dt>
              <dd>The vector to convert</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Implicit(Sansar.Vector@)~Mono.Simd.Vector4f:Returns">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Implicit(Sansar.Vector@)~Mono.Simd.Vector4f:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Implicit(Sansar.Vector@)~Mono.Simd.Vector4f:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.op_Multiply(Sansar.Vector@,System.Single)">op_Multiply Method</h3>
        <blockquote id="M:Sansar.Vector.op_Multiply(Sansar.Vector@,System.Single):member">
          <div class="msummary">
            Performs a vector scalar multiplication.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="../Sansar/Vector.html">Vector</a> operator* ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> a, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Multiply(Sansar.Vector@,System.Single):Parameters">
            <dl>
              <dt>
                <i>a</i>
              </dt>
              <dd>The vector.</dd>
              <dt>
                <i>b</i>
              </dt>
              <dd>The scalar.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Multiply(Sansar.Vector@,System.Single):Returns">Returns a new vector with value [a.X*b, a.Y*b, a.Z*b, a.W*b]</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Multiply(Sansar.Vector@,System.Single):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Multiply(Sansar.Vector@,System.Single):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.op_Multiply(System.Single,Sansar.Vector@)">op_Multiply Method</h3>
        <blockquote id="M:Sansar.Vector.op_Multiply(System.Single,Sansar.Vector@):member">
          <div class="msummary">
            Performs a vector scalar multiplication.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="../Sansar/Vector.html">Vector</a> operator* (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> a, [System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Multiply(System.Single,Sansar.Vector@):Parameters">
            <dl>
              <dt>
                <i>a</i>
              </dt>
              <dd>The scalar.</dd>
              <dt>
                <i>b</i>
              </dt>
              <dd>The vector.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Multiply(System.Single,Sansar.Vector@):Returns">Returns a new vector with value [a*b.X, a*b.Y, a*b.Z, a*b.W]</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Multiply(System.Single,Sansar.Vector@):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Multiply(System.Single,Sansar.Vector@):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.op_Subtraction(Sansar.Vector@,Sansar.Vector@)">op_Subtraction Method</h3>
        <blockquote id="M:Sansar.Vector.op_Subtraction(Sansar.Vector@,Sansar.Vector@):member">
          <div class="msummary">
            Performs vector subtraction.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="../Sansar/Vector.html">Vector</a> operator- ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> a, [System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> b)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Subtraction(Sansar.Vector@,Sansar.Vector@):Parameters">
            <dl>
              <dt>
                <i>a</i>
              </dt>
              <dd>The first Vector.</dd>
              <dt>
                <i>b</i>
              </dt>
              <dd>The second Vector.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.op_Subtraction(Sansar.Vector@,Sansar.Vector@):Returns">A new vector that is the difference of the arguments.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Subtraction(Sansar.Vector@,Sansar.Vector@):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.op_Subtraction(Sansar.Vector@,Sansar.Vector@):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.Orthonormalize(Sansar.Vector@,Sansar.Vector@)">Orthonormalize Method</h3>
        <blockquote id="M:Sansar.Vector.Orthonormalize(Sansar.Vector@,Sansar.Vector@):member">
          <div class="msummary">
            Normalizes the two vectors and returns the normalized cross product.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="../Sansar/Vector.html">Vector</a> <b>Orthonormalize</b> (<i>ref</i> <a href="../Sansar/Vector.html">Vector</a> forward, <i>ref</i> <a href="../Sansar/Vector.html">Vector</a> up)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Orthonormalize(Sansar.Vector@,Sansar.Vector@):Parameters">
            <dl>
              <dt>
                <i>forward</i>
              </dt>
              <dd>The facing ve</dd>
              <dt>
                <i>up</i>
              </dt>
              <dd>
              </dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Orthonormalize(Sansar.Vector@,Sansar.Vector@):Returns">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Orthonormalize(Sansar.Vector@,Sansar.Vector@):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Orthonormalize(Sansar.Vector@,Sansar.Vector@):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.Parse(System.String)">Parse Method</h3>
        <blockquote id="M:Sansar.Vector.Parse(System.String):member">
          <div class="msummary">
            Parse a Vector from a string.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="../Sansar/Vector.html">Vector</a> <b>Parse</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> vectorString)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Parse(System.String):Parameters">
            <dl>
              <dt>
                <i>vectorString</i>
              </dt>
              <dd>A string of the format &lt;X,Y,Z&gt; or &lt;X,Y,Z,W&gt;</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Parse(System.String):Returns">The Vector parsed from the string.</blockquote>
          <h4 class="Subsection">Exceptions</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Parse(System.String):Exceptions">
            <table class="TypeDocumentation">
              <tr>
                <th>Type</th>
                <th>Reason</th>
              </tr>
              <tr valign="top">
                <td>
                  <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.ArgumentException">ArgumentException</a>
                </td>
                <td>If vectorString is null.</td>
              </tr>
              <tr valign="top">
                <td>
                  <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.FormatException">FormatException</a>
                </td>
                <td>If the string is not a valid vector or its components are not valid floats.</td>
              </tr>
            </table>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Parse(System.String):Remarks">
          </div>
          <h2 class="Section">Example</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Parse(System.String):Example:1">
            <table class="CodeExampleTable">
              <tr>
                <td>
                  <b>
                    <font size="-1">C# Example</font>
                  </b>
                </td>
              </tr>
              <tr>
                <td>
                  <pre class="code-csharp">Vector myVector = Vector.Parse("&lt;1,2.34,5.6&gt;");</pre>
                </td>
              </tr>
            </table>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Parse(System.String):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.Right">Right Field</h3>
        <blockquote id="F:Sansar.Vector.Right:member">
          <div class="msummary">
            Deprecated right vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[System.Obsolete("Use ObjectRight for correct right vector.", false)]<br />public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>Right</b> </div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="F:Sansar.Vector.Right:See Also">
            <div>
              <a href="../Sansar/Vector.html#F:Sansar.Vector.ObjectRight">Vector.ObjectRight</a>
            </div>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Right:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Right:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.Rotate(Sansar.Quaternion@)">Rotate Method</h3>
        <blockquote id="M:Sansar.Vector.Rotate(Sansar.Quaternion@):member">
          <div class="msummary">
            Returns a new vector which is this vector rotated by the given quaternion.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="../Sansar/Vector.html">Vector</a> <b>Rotate</b> ([System.Runtime.CompilerServices.IsReadOnly] <i>ref</i> <a href="../Sansar/Quaternion.html">Quaternion</a> q)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Rotate(Sansar.Quaternion@):Parameters">
            <dl>
              <dt>
                <i>q</i>
              </dt>
              <dd>The quaternion rotation.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.Rotate(Sansar.Quaternion@):Returns">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Rotate(Sansar.Quaternion@):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.Rotate(Sansar.Quaternion@):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.ToString()">ToString Method</h3>
        <blockquote id="M:Sansar.Vector.ToString():member">
          <div class="msummary">
            Generates a string representation of the vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public override <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.ToString():Returns">The vector as a string.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.ToString():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.ToString():Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.ToString(System.String)">ToString Method</h3>
        <blockquote id="M:Sansar.Vector.ToString(System.String):member">
          <div class="msummary">
            Generates a string representation of the vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> format)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.ToString(System.String):Parameters">
            <dl>
              <dt>
                <i>format</i>
              </dt>
              <dd>Format to use for each of the coordinates.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.ToString(System.String):Returns">The vector as a string.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.ToString(System.String):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.ToString(System.String):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.ToString4(System.String)">ToString4 Method</h3>
        <blockquote id="M:Sansar.Vector.ToString4(System.String):member">
          <div class="msummary">
            Generates a string representation of the vector, including the W parameter.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ToString4</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> format)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.ToString4(System.String):Parameters">
            <dl>
              <dt>
                <i>format</i>
              </dt>
              <dd>Format to use for each of the coordinates.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.ToString4(System.String):Returns">The vector as a string.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.ToString4(System.String):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.ToString4(System.String):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Vector.TryParse(System.String,Sansar.Vector@)">TryParse Method</h3>
        <blockquote id="M:Sansar.Vector.TryParse(System.String,Sansar.Vector@):member">
          <div class="msummary">
            Attempt to parse a Vector from a string
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>TryParse</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> vectorString, <i>out</i> <a href="../Sansar/Vector.html">Vector</a> vector)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.TryParse(System.String,Sansar.Vector@):Parameters">
            <dl>
              <dt>
                <i>vectorString</i>
              </dt>
              <dd>A string of the format &lt;X,Y,Z&gt; or &lt;X,Y,Z,W&gt;</dd>
              <dt>
                <i>vector</i>
              </dt>
              <dd>The vector that will be set if vectorString represents a valid vector.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Vector.TryParse(System.String,Sansar.Vector@):Returns">True if successfully parsed a vector, false if not.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Vector.TryParse(System.String,Sansar.Vector@):Remarks">
          </div>
          <h2 class="Section">Example</h2>
          <div class="SectionBox" id="M:Sansar.Vector.TryParse(System.String,Sansar.Vector@):Example:1">
            <table class="CodeExampleTable">
              <tr>
                <td>
                  <b>
                    <font size="-1">C# Example</font>
                  </b>
                </td>
              </tr>
              <tr>
                <td>
                  <pre class="code-csharp">Vector myVector;
            if (Vector.Parse("&lt;1,2.34,5.6&gt;", out myVector)
            {
                // myVector is set
            }</pre>
                </td>
              </tr>
            </table>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Vector.TryParse(System.String,Sansar.Vector@):Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.Up">Up Field</h3>
        <blockquote id="F:Sansar.Vector.Up:member">
          <div class="msummary">
            Deprecated up Vector
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[System.Obsolete("Use ObjectUp for correct up vector.", false)]<br />public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>Up</b> </div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="F:Sansar.Vector.Up:See Also">
            <div>
              <a href="../Sansar/Vector.html#F:Sansar.Vector.ObjectUp">Vector.ObjectUp</a>
            </div>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Up:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Up:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Vector.W">W Property</h3>
        <blockquote id="P:Sansar.Vector.W:member">
          <div class="msummary">
            The W coordinate of the vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>W</b>  { get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Vector.W:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Vector.W:Remarks">W should be 0 and is assumed to be 0 for most operations.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Vector.W:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Vector.X">X Property</h3>
        <blockquote id="P:Sansar.Vector.X:member">
          <div class="msummary">
            The X coordinate of the vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>X</b>  { get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Vector.X:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Vector.X:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Vector.X:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Vector.Y">Y Property</h3>
        <blockquote id="P:Sansar.Vector.Y:member">
          <div class="msummary">
            The Y coordinate of the vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>Y</b>  { get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Vector.Y:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Vector.Y:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Vector.Y:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Vector.Z">Z Property</h3>
        <blockquote id="P:Sansar.Vector.Z:member">
          <div class="msummary">
            The Z coordinate of the vector.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Single">float</a> <b>Z</b>  { get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Vector.Z:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Vector.Z:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Vector.Z:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Vector.Zero">Zero Field</h3>
        <blockquote id="F:Sansar.Vector.Zero:member">
          <div class="msummary">
            A vector with all components 0.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public static readonly <a href="../Sansar/Vector.html">Vector</a> <b>Zero</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Zero:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Vector.Zero:Version Information">
            <b>Namespace: </b>Sansar<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>