<html>
  <head>
    <title>Sansar.Simulation.SimpleScript.OnChatOptionsAttribute</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute">SimpleScript.OnChatOptionsAttribute  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute:Summary">
            Set options for OnChat() overrides, or use to create more simple chat handlers.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute:Signature">[Sansar.Script.Interface]<br />[System.AttributeUsage(System.AttributeTargets.Method, AllowMultiple=true)]<br />[System.Obsolete("SimpleScript is deprecated and will not receive new features. Please use SceneObjectScript or ObjectScript.", false)]<br />protected class  <b>SimpleScript.OnChatOptionsAttribute</b> : <a href="../Sansar.Simulation/SimpleScript+SimpleScriptOptionsAttribute.html">SimpleScript.SimpleScriptOptionsAttribute</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute:Docs">
      <h4 class="Subsection">See Also</h4>
      <blockquote class="SubsectionBox" id="T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute:Docs:See Also">
        <div>
          <a href="../Sansar.Simulation/SimpleScript.html#M:Sansar.Simulation.SimpleScript.OnChat(Sansar.Simulation.ChatData)">SimpleScript.OnChat(ChatData)</a>
        </div>
      </blockquote>
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute:Docs:Remarks">
        <p>This attribute is optional when overriding OnChat which by default will respond to all chat on the default channel (Chat.DefaultChannel) and all sources.</p>
            Add this attribute to any method that matches Action&lt;ChatData&gt; to create extra chat event subscriptions.
            <table class="CodeExampleTable"><tr><td><b><font size="-1">C# Example</font></b></td></tr><tr><td><pre class="code-csharp">
            [OnChatOptions(Channel=5)]
            void ChatFrom5(ChatData data)
            {
               // chat from channel 5.
            }
              
            void OnChat(ChatData data)
            {
               // chat from the default channel (0)
            }</pre></td></tr></table></div>
      <h2 class="Section">Example</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute:Docs:Example:1">
        <table class="CodeExampleTable">
          <tr>
            <td>
              <b>
                <font size="-1">C# Example</font>
              </b>
            </td>
          </tr>
          <tr>
            <td>
              <pre class="code-csharp">
            // Only listen for chat on Channel 10 from scripts.
            [OnChatOptions(Channel=10, Source="script")]
            protected override OnChat(ChatData data)
            </pre>
            </td>
          </tr>
        </table>
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Simulation/SimpleScript+SimpleScriptOptionsAttribute.html">SimpleScript.SimpleScriptOptionsAttribute</a>.
							</p>
        <h2 class="Section">Public Constructors</h2>
        <div class="SectionBox" id="Public Constructors">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <div>
                    <b>
                      <a href="#C:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute()">SimpleScript.OnChatOptionsAttribute</a>
                    </b>()</div>
                </td>
                <td>Set options for OnChat events</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Channel">Channel</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>
                  </i>. 
            Specify a specific channel to listen for messages on.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Source">Source</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. 
            string source of the chat: system, user or script.
            </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="C:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute()">SimpleScript.OnChatOptionsAttribute Constructor</h3>
        <blockquote id="C:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute():member">
          <div class="msummary">Set options for OnChat events</div>
          <h2>Syntax</h2>
          <div class="Signature">public  <b>SimpleScript.OnChatOptionsAttribute</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="C:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="C:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Channel">Channel Property</h3>
        <blockquote id="P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Channel:member">
          <div class="msummary">
            Specify a specific channel to listen for messages on.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("SimpleScript is deprecated and will not receive new features. Please use SceneObjectScript or ObjectScript.", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>Channel</b>  { get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Channel:Value">The channel to listen for chat on.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Channel:Remarks">By default listens for chat on Chat.DefaultChannel (0).</div>
          <h2 class="Section">Example</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Channel:Example:1">
            <table class="CodeExampleTable">
              <tr>
                <td>
                  <b>
                    <font size="-1">C# Example</font>
                  </b>
                </td>
              </tr>
              <tr>
                <td>
                  <pre class="code-csharp">
            // Only listen for chat on Channel 10.
            [OnChatOptions(Channel=10)]
            protected override OnChat(ChatData data)
            </pre>
                </td>
              </tr>
            </table>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Channel:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Source">Source Property</h3>
        <blockquote id="P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Source:member">
          <div class="msummary">
            string source of the chat: system, user or script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("SimpleScript is deprecated and will not receive new features. Please use SceneObjectScript or ObjectScript.", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>Source</b>  { get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Source:Value">The source chat to listen for: "script", "user" or "system"</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Source:Remarks">Restricts which chat messages will trigger OnChat. [OnChatOptions(Source="user")] will only run OnChat for chat from users, while "script" will respond to other script messages, and "system" will only respond to system messages.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.SimpleScript.OnChatOptionsAttribute.Source:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>