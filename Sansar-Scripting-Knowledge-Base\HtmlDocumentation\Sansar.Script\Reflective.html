<html>
  <head>
    <title>Sansar.Script.Reflective</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Script</a> : <a href="index.html">Sansar.Script Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Script.Reflective">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Script.Reflective:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Script.Reflective:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Script.Reflective:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Script.Reflective">Reflective  Class</h1>
    <p class="Summary" id="T:Sansar.Script.Reflective:Summary">
            Base class which provides for simple reflection of methods through a given interface.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Script.Reflective:Signature">public class  <b>Reflective</b></div>
    </div>
    <div class="Remarks" id="T:Sansar.Script.Reflective:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Script.Reflective:Docs:Remarks">
            If the object already implements the interface it will return the object directly. Otherwise an facade class is created to map between
            public methods and properties. 
            Two scripts which use a Reflective object to communicate via script events are below:
            <table class="CodeExampleTable"><tr><td><b><font size="-1">C# Example</font></b></td></tr><tr><td><pre class="code-csharp">/* This content is licensed under the terms of the Creative Commons Attribution 4.0 International License.
 * When using this content, you must:
 * �    Acknowledge that the content is from the Sansar Knowledge Base.
 * �    Include our copyright notice: "2022 Sansar, Inc."
 * �    Indicate that the content is licensed under the Creative Commons Attribution-Share Alike 4.0 International License.
 * �    Include the URL for, or link to, the license summary at https://creativecommons.org/licenses/by-sa/4.0/deed.hi (and, if possible, to the complete license terms at https://creativecommons.org/licenses/by-sa/4.0/legalcode.
 * For example:
 * "This work uses content from the Sansar Knowledge Base. � 2022 Sansar, Inc." Licensed under the Creative Commons Attribution 4.0 International License (license summary available at https://creativecommons.org/licenses/by/4.0/ and complete license terms available at https://creativecommons.org/licenses/by/4.0/legalcode)."
 */
using Sansar.Simulation;
using Sansar.Script;
using System;
using System.Diagnostics;

// This class is a simple stopwatch that can respond to requests for the current elapsed time
// and resets. The elapsed time is also broadcast periodically.
public class SharedStopwatchExample : SceneObjectScript
{
    #region ResetTime
    // This is the event object. Setting the base class to Reflective allows access to the public 
    // properties and methods without needing a reference to this script
    public class ResetTime : Reflective
    {
        // The id of the script which made the last reset request.
        // The setter is made internal so that our script can call
        // it but other scripts cannot.
        public ScriptId SourceScriptId { get; internal set; }
        
        // The current start time.
        // The setter is made internal so that our script can call
        // it but other scripts cannot.
        public long StartTime { get; internal set; }

        // The calculated elapsed time
        public TimeSpan Elapsed
        {
            get
            {
                return TimeSpan.FromTicks(Stopwatch.GetTimestamp() - StartTime);
            }
        }
        // The count of times the timer has been reset
        // The setter is made internal so that our script can call
        // it but other scripts cannot.
        public int ResetCount { get; internal set; }
    }
    #endregion ResetTime

    #region EditorProperties
    [DefaultValue("request")]
    public readonly string requestCommand = "request";

    [DefaultValue("elapsed")]
    public readonly string elapsedCommand = "elapsed";

    [DefaultValue("reset")]
    public readonly string resetCommand = "reset";

    [DefaultValue(1)]
    public readonly double broadcastMinutes = .15;
    #endregion EditorProperties

    #region EventHandlers
    // Handler for the requestCommand, sends the current information back to the requesting script
    private void requestElapsed(ScriptEventData uptime)
    {
        sendElapsed(uptime.SourceScriptId);
    }

    // Handler for the resetCommand, resets the start time
    private void resetElapsed(ScriptEventData uptime)
    {
        resetElapsed(uptime.SourceScriptId);
    }
    #endregion EventHandlers

    #region Implementation

    // Object which tracks all the info we need
    private ResetTime resetTime = new ResetTime();

    // Posts the current time information to the given script id
    // targetScriptId will be AllScripts for the broadcast
    private void sendElapsed(ScriptId targetScriptId)
    {
        PostScriptEvent(targetScriptId, elapsedCommand, resetTime);
    }

    // Resets the elapsed time and tracks the id of the script making the request
    private void resetElapsed(ScriptId id)
    {
        Log.Write(LogLevel.Info, Script.ID.ToString(), $"reset requested by script {id}");
        resetTime.SourceScriptId = id;
        resetTime.StartTime = Stopwatch.GetTimestamp();
        resetTime.ResetCount++;
    }
    #endregion Implementation

    #region Overrides
    public override void Init()
    {
        // write this script id to the log to track messages
        Log.Write(LogLevel.Info, Script.ID.ToString(), nameof(SharedStopwatchExample));

        // sets the initial timer and script id to this script
        resetElapsed(Script.ID);

        // listen for direct requests for the elapsed time
        SubscribeToScriptEvent(requestCommand, requestElapsed);

        // listen for requests to reset the time
        SubscribeToScriptEvent(resetCommand, resetElapsed);

        // set up a timer that broadcasts the elapsed time
        Timer.Create(TimeSpan.FromMinutes(broadcastMinutes), 
            TimeSpan.FromMinutes(broadcastMinutes), 
            () =&gt; sendElapsed(ScriptId.AllScripts));
    }
    #endregion Overrides

}</pre></td></tr></table><table class="CodeExampleTable"><tr><td><b><font size="-1">C# Example</font></b></td></tr><tr><td><pre class="code-csharp">/* This content is licensed under the terms of the Creative Commons Attribution 4.0 International License.
 * When using this content, you must:
 * �    Acknowledge that the content is from the Sansar Knowledge Base.
 * �    Include our copyright notice: "2022 Sansar, Inc."
 * �    Indicate that the content is licensed under the Creative Commons Attribution-Share Alike 4.0 International License.
 * �    Include the URL for, or link to, the license summary at https://creativecommons.org/licenses/by-sa/4.0/deed.hi (and, if possible, to the complete license terms at https://creativecommons.org/licenses/by-sa/4.0/legalcode.
 * For example:
 * "This work uses content from the Sansar Knowledge Base. � 2022 Sansar, Inc." Licensed under the Creative Commons Attribution 4.0 International License (license summary available at https://creativecommons.org/licenses/by/4.0/ and complete license terms available at https://creativecommons.org/licenses/by/4.0/legalcode)."
 */
using Sansar.Simulation;
using Sansar.Script;
using System;

public class ScriptEventSourceExample : SceneObjectScript
{
    #region ResetTime
    // This is the event interface that this script will use. Only the public methods and properties
    // that will be referenced are used.
    public interface ResetTime 
    {
        // The current start time.
        // The setter is not public so this script will only ask for the getter
        DateTime StartTime { get; }

        // The calculated elapsed time
        TimeSpan Elapsed { get; }
    }
    #endregion ResetTime

    #region EditorProperties
    [DefaultValue("request")]
    public readonly string requestCommand = "request";

    [DefaultValue("elapsed")]
    public readonly string elapsedCommand = "elapsed";

    [DefaultValue("reset")]
    public readonly string resetCommand = "reset";

    [DefaultValue(3)]
    public readonly double resetMinutes = .2;
    #endregion EditorProperties

    #region EventHandlers

    private void elapsed(ScriptEventData elapsed)
    {
        if(elapsed.Data == null)
        {
            Log.Write(LogLevel.Warning, Script.ID.ToString(), "Expected non-null event data");
            return;
        }
        ResetTime resetTime = elapsed.Data.AsInterface&lt;ResetTime&gt;();

        if(resetTime == null)
        {
            Log.Write(LogLevel.Error, Script.ID.ToString(), "Unable to create interface, check logs for missing member(s)");
            return;
        }

        Log.Write(LogLevel.Info, Script.ID.ToString(), $"Elapsed time = {resetTime.Elapsed} since {resetTime.StartTime}");
    }

    #endregion EventHandlers

    public override void Init()
    {
        // write this script id to the log to track messages
        Log.Write(LogLevel.Info, Script.ID.ToString(), nameof(ScriptEventSourceExample));

        // Subscribe to elapsed messages
        SubscribeToScriptEvent(elapsedCommand, elapsed);

        // set up a timer to periodically reset the elapsed time
        Timer.Create(TimeSpan.FromMinutes(resetMinutes),
            TimeSpan.FromMinutes(resetMinutes),
            () =&gt; PostScriptEvent(resetCommand));
        
    }


}</pre></td></tr></table></div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Script.Reflective:Docs:Version Information">
        <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>.
							</p>
        <h2 class="Section">Protected Constructors</h2>
        <div class="SectionBox" id="Protected Constructors">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <div>
                    <b>
                      <a href="#C:Sansar.Script.Reflective()">Reflective</a>
                    </b>()</div>
                </td>
                <td>
            Default constructor.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Protected Properties</h2>
        <div class="SectionBox" id="Protected Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Script.Reflective.AllowedContexts">AllowedContexts</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/Reflective+Context.html">Reflective.Context</a>
                  </i>. Internal Use Only. Overridden by subclasses to return only those contexts requested which are allowed for that type of script.</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Script.Reflective.ReflectiveContexts">ReflectiveContexts</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/Reflective+Context.html">Reflective.Context</a>
                  </i>. 
            Override ReflectiveContexts to limit which contexts this Reflective interface is available in when registered with.
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Script.Reflective.ReflectiveName">ReflectiveName</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. 
            Override ReflectiveName to change which name this class will be registered as in the Reflective system.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.Reflective.AsInterface``1()">AsInterface&lt;TInterface&gt;</a>
                  </b>()<nobr> : <i title="The interface describing the methods and properties desired.">TInterface</i></nobr><blockquote>
            Returns a TInterface object if one can be created, null otherwise
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.Reflective.FullInterface(System.String)">FullInterface</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>
            Generates a string which shows all the members which can be reflected.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.Reflective.Register()">Register</a>
                  </b>()<blockquote>
            Register this object to be found with <a href="../Sansar.Simulation/ScenePrivate.html#M:Sansar.Simulation.ScenePrivate.FindReflective(System.String)">Sansar.Simulation.ScenePrivate.FindReflective(string)</a></blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Script.Reflective.Unregister()">Unregister</a>
                  </b>()<blockquote>
            Unregister this object so it will not be found with <a href="../Sansar.Simulation/ScenePrivate.html#M:Sansar.Simulation.ScenePrivate.FindReflective(System.String)">Sansar.Simulation.ScenePrivate.FindReflective(string)</a></blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Script.Reflective:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="C:Sansar.Script.Reflective()">Reflective Constructor</h3>
        <blockquote id="C:Sansar.Script.Reflective():member">
          <div class="msummary">
            Default constructor.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">protected  <b>Reflective</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="C:Sansar.Script.Reflective():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="C:Sansar.Script.Reflective():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Script.Reflective.AllowedContexts">AllowedContexts Property</h3>
        <blockquote id="P:Sansar.Script.Reflective.AllowedContexts:member">
          <div class="msummary">Internal Use Only. Overridden by subclasses to return only those contexts requested which are allowed for that type of script.</div>
          <h2>Syntax</h2>
          <div class="Signature">protected virtual <a href="../Sansar.Script/Reflective+Context.html">Reflective.Context</a> <b>AllowedContexts</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Script.Reflective.AllowedContexts:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Script.Reflective.AllowedContexts:Remarks">Used internally to prevent visiting guest scripts from registering as 'scene level' APIs.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Script.Reflective.AllowedContexts:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.Reflective.AsInterface``1()">AsInterface&lt;TInterface&gt; Generic Method</h3>
        <blockquote id="M:Sansar.Script.Reflective.AsInterface``1():member">
          <div class="msummary">
            Returns a TInterface object if one can be created, null otherwise
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />public <i title="The interface describing the methods and properties desired.">TInterface</i> <b>AsInterface&lt;TInterface&gt;</b> ()<br /> where TInterface : class</div>
          <h4 class="Subsection">Type Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.Reflective.AsInterface``1():Type Parameters">
            <dl>
              <dt>
                <i>TInterface</i>
              </dt>
              <dd>The interface describing the methods and properties desired.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.Reflective.AsInterface``1():Returns">A TInterface instance if this object is compatible. An object is compatible if it implements the interface or
            has public methods and properties which match TInterface.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.Reflective.AsInterface``1():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.Reflective.AsInterface``1():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.Reflective.FullInterface(System.String)">FullInterface Method</h3>
        <blockquote id="M:Sansar.Script.Reflective.FullInterface(System.String):member">
          <div class="msummary">
            Generates a string which shows all the members which can be reflected.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>FullInterface</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> interfaceName)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.Reflective.FullInterface(System.String):Parameters">
            <dl>
              <dt>
                <i>interfaceName</i>
              </dt>
              <dd>The name to give the generated interface class in the output.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Script.Reflective.FullInterface(System.String):Returns">The generated string. If no members can be reflected, returns the empty string</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.Reflective.FullInterface(System.String):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.Reflective.FullInterface(System.String):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Script.Reflective.ReflectiveContexts">ReflectiveContexts Property</h3>
        <blockquote id="P:Sansar.Script.Reflective.ReflectiveContexts:member">
          <div class="msummary">
            Override ReflectiveContexts to limit which contexts this Reflective interface is available in when registered with.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />protected virtual <a href="../Sansar.Script/Reflective+Context.html">Reflective.Context</a> <b>ReflectiveContexts</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Script.Reflective.ReflectiveContexts:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Script.Reflective.ReflectiveContexts:Remarks">This can be used to further restrict which contexts an interface is available in. It can not be used to register for a context not allowed by the running script type.
            SceneObjectScript allows ScenePrivate, ScenePublic, ObjectPrivate. ObjectScript allows ObjectPrivate.
            Overriding this will automatically register this Reflective </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Script.Reflective.ReflectiveContexts:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Script.Reflective.ReflectiveName">ReflectiveName Property</h3>
        <blockquote id="P:Sansar.Script.Reflective.ReflectiveName:member">
          <div class="msummary">
            Override ReflectiveName to change which name this class will be registered as in the Reflective system.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />protected virtual <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>ReflectiveName</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Script.Reflective.ReflectiveName:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Script.Reflective.ReflectiveName:Remarks">Defaults to GetType().FullName</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Script.Reflective.ReflectiveName:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.Reflective.Register()">Register Method</h3>
        <blockquote id="M:Sansar.Script.Reflective.Register():member">
          <div class="msummary">
            Register this object to be found with <a href="../Sansar.Simulation/ScenePrivate.html#M:Sansar.Simulation.ScenePrivate.FindReflective(System.String)">Sansar.Simulation.ScenePrivate.FindReflective(string)</a></div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Register</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.Reflective.Register():Remarks">Will register this reflective object with <a href="../Sansar.Script/Reflective.html#P:Sansar.Script.Reflective.AllowedContexts">Reflective.AllowedContexts</a> if overridden, otherwise with ScenePrivate and ObjectPrivate.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.Reflective.Register():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Script.Reflective.Unregister()">Unregister Method</h3>
        <blockquote id="M:Sansar.Script.Reflective.Unregister():member">
          <div class="msummary">
            Unregister this object so it will not be found with <a href="../Sansar.Simulation/ScenePrivate.html#M:Sansar.Simulation.ScenePrivate.FindReflective(System.String)">Sansar.Simulation.ScenePrivate.FindReflective(string)</a></div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Unregister</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Script.Reflective.Unregister():Remarks">If the Reflective object has not previously been registered nothing happens.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Script.Reflective.Unregister():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>