<html>
  <head>
    <title>Sansar.Simulation.Reactions</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.Reactions">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.Reactions:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.Reactions:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.Reactions:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.Reactions">Reactions  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.Reactions:Summary">The Reactions class subscriptions to user reaction events.</p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.Reactions:Signature">[Sansar.Script.Interface]<br />public class  <b>Reactions</b> : <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.Reactions:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.Reactions:Docs:Remarks">
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.Reactions:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.
							</p>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/InstanceInterface.html#P:Sansar.Script.InstanceInterface.IsValid">IsValid</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            Whether or not this interface is valid.
             (<i>Inherited from <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.</i>)</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Reactions.AddReaction(System.String,System.String,Sansar.Simulation.ThumbnailedClusterResource)">AddReaction</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Simulation/ThumbnailedClusterResource.html">ThumbnailedClusterResource</a>)<blockquote>Add a new reaction type to all clients</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Reactions.AddSystemReaction(System.String,System.String,System.String,Sansar.Simulation.ClusterResource)">AddSystemReaction</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Simulation/ClusterResource.html">ClusterResource</a>)<blockquote>Add a new system default reaction.  Intended for internal use by Sansar</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Reactions.DisableDefaultReactions()">DisableDefaultReactions</a>
                  </b>()<blockquote>Removes all the default reactions from the emote menu, leaving only custom ones.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Reactions.RemoveReaction(System.String)">RemoveReaction</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<blockquote>Remove the specified global reaction from all clients.</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Reactions.SubscribeToReaction(System.Action{Sansar.Simulation.ReactionData},System.Boolean)">SubscribeToReaction</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ReactionData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>Subscribes to Reaction Events.</blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.Reactions:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="M:Sansar.Simulation.Reactions.AddReaction(System.String,System.String,Sansar.Simulation.ThumbnailedClusterResource)">AddReaction Method</h3>
        <blockquote id="M:Sansar.Simulation.Reactions.AddReaction(System.String,System.String,Sansar.Simulation.ThumbnailedClusterResource):member">
          <div class="msummary">Add a new reaction type to all clients</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>AddReaction</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> reactionType, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> displayText, <a href="../Sansar.Simulation/ThumbnailedClusterResource.html">ThumbnailedClusterResource</a> resource)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Reactions.AddReaction(System.String,System.String,Sansar.Simulation.ThumbnailedClusterResource):Parameters">
            <dl>
              <dt>
                <i>reactionType</i>
              </dt>
              <dd>A string that gets passed to the callback.  Should be of a form like 'Developer.Type' to match the default, e.g. 'Sansar.Fire'</dd>
              <dt>
                <i>displayText</i>
              </dt>
              <dd>A short string that appears on the emotes panel</dd>
              <dt>
                <i>resource</i>
              </dt>
              <dd>A cluster source which has a thumbnail defined in inventory.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Reactions.AddReaction(System.String,System.String,Sansar.Simulation.ThumbnailedClusterResource):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Reactions.AddReaction(System.String,System.String,Sansar.Simulation.ThumbnailedClusterResource):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Reactions.AddSystemReaction(System.String,System.String,System.String,Sansar.Simulation.ClusterResource)">AddSystemReaction Method</h3>
        <blockquote id="M:Sansar.Simulation.Reactions.AddSystemReaction(System.String,System.String,System.String,Sansar.Simulation.ClusterResource):member">
          <div class="msummary">Add a new system default reaction.  Intended for internal use by Sansar</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>AddSystemReaction</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> reactionType, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> displayText, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> thumbnailPath, <a href="../Sansar.Simulation/ClusterResource.html">ClusterResource</a> resource)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Reactions.AddSystemReaction(System.String,System.String,System.String,Sansar.Simulation.ClusterResource):Parameters">
            <dl>
              <dt>
                <i>reactionType</i>
              </dt>
              <dd>A string that gets passed to the callback.  Should be of a form like 'Developer.Type' to match the default, e.g. 'Sansar.Fire'</dd>
              <dt>
                <i>displayText</i>
              </dt>
              <dd>A short string that appears on the emotes panel</dd>
              <dt>
                <i>thumbnailPath</i>
              </dt>
              <dd>Must be a path to a file included by client.  RUNTIME:/UI/Social/Images/*.png</dd>
              <dt>
                <i>resource</i>
              </dt>
              <dd>A cluster source that will be passed to the reaction callback.</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Reactions.AddSystemReaction(System.String,System.String,System.String,Sansar.Simulation.ClusterResource):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Reactions.AddSystemReaction(System.String,System.String,System.String,Sansar.Simulation.ClusterResource):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Reactions.DisableDefaultReactions()">DisableDefaultReactions Method</h3>
        <blockquote id="M:Sansar.Simulation.Reactions.DisableDefaultReactions():member">
          <div class="msummary">Removes all the default reactions from the emote menu, leaving only custom ones.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>DisableDefaultReactions</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Reactions.DisableDefaultReactions():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Reactions.DisableDefaultReactions():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Reactions.RemoveReaction(System.String)">RemoveReaction Method</h3>
        <blockquote id="M:Sansar.Simulation.Reactions.RemoveReaction(System.String):member">
          <div class="msummary">Remove the specified global reaction from all clients.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>RemoveReaction</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> reactionType)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Reactions.RemoveReaction(System.String):Parameters">
            <dl>
              <dt>
                <i>reactionType</i>
              </dt>
              <dd>A string that gets passed to the callback.  Should be of a form like 'Developer.Type' to match the default, e.g. 'Sansar.Fire'</dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Reactions.RemoveReaction(System.String):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Reactions.RemoveReaction(System.String):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Reactions.SubscribeToReaction(System.Action{Sansar.Simulation.ReactionData},System.Boolean)">SubscribeToReaction Method</h3>
        <blockquote id="M:Sansar.Simulation.Reactions.SubscribeToReaction(System.Action{Sansar.Simulation.ReactionData},System.Boolean):member">
          <div class="msummary">Subscribes to Reaction Events.</div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> <b>SubscribeToReaction</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;ReactionData&gt;</a> callback, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> persistent)</div>
          <h4 class="Subsection">See Also</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Reactions.SubscribeToReaction(System.Action{Sansar.Simulation.ReactionData},System.Boolean):See Also">
            <div>
              <a href="../Sansar.Simulation/ReactionData.html">ReactionData</a>
            </div>
            <div>
              <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a>
            </div>
          </blockquote>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Reactions.SubscribeToReaction(System.Action{Sansar.Simulation.ReactionData},System.Boolean):Parameters">
            <dl>
              <dt>
                <i>callback</i>
              </dt>
              <dd>Callback which is executed when the event completes.</dd>
              <dt>
                <i>persistent</i>
              </dt>
              <dd>Optional, set to false to unsubscribe after one event.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Reactions.SubscribeToReaction(System.Action{Sansar.Simulation.ReactionData},System.Boolean):Returns">An <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a> that can be used to cancel the subscription.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Reactions.SubscribeToReaction(System.Action{Sansar.Simulation.ReactionData},System.Boolean):Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Reactions.SubscribeToReaction(System.Action{Sansar.Simulation.ReactionData},System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>