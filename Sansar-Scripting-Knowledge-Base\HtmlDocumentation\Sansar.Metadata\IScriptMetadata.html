<html>
  <head>
    <title>Sansar.Metadata.IScriptMetadata</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Script</a> : <a href="index.html">Sansar.Metadata Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Metadata.IScriptMetadata">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Metadata.IScriptMetadata:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Metadata.IScriptMetadata:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Metadata.IScriptMetadata:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Metadata.IScriptMetadata">IScriptMetadata  Interface</h1>
    <p class="Summary" id="T:Sansar.Metadata.IScriptMetadata:Summary">
            Provides information about user scripts.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Metadata.IScriptMetadata:Signature">public interface  <b>IScriptMetadata</b></div>
    </div>
    <div class="Remarks" id="T:Sansar.Metadata.IScriptMetadata:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Metadata.IScriptMetadata:Docs:Remarks">internal use only</div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Metadata.IScriptMetadata:Docs:Version Information">
        <b>Namespace: </b>Sansar.Metadata<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Metadata.IScriptMetadata.Name">Name</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. 
            The full name of the script class as returned by <a href="http://www.go-mono.com/docs/monodoc.ashx?link=P:System.Type.FullName">Type.FullName</a>. 
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Metadata.IScriptMetadata.RegisterReflective">RegisterReflective</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            True if <a href="../Sansar.Script/RegisterReflectiveAttribute.html">Sansar.Script.RegisterReflectiveAttribute</a> is attached to this type.
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Metadata.IScriptMetadata.Type">Type</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Type">Type</a>
                  </i>. 
            The type of the script.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Metadata.IScriptMetadata.Create()">Create</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a></nobr><blockquote>
            Creates a new instance of the script.
            </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Metadata.IScriptMetadata:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="M:Sansar.Metadata.IScriptMetadata.Create()">Create Method</h3>
        <blockquote id="M:Sansar.Metadata.IScriptMetadata.Create():member">
          <div class="msummary">
            Creates a new instance of the script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a> <b>Create</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Metadata.IScriptMetadata.Create():Returns">The new script</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Metadata.IScriptMetadata.Create():Remarks">internal use only</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Metadata.IScriptMetadata.Create():Version Information">
            <b>Namespace: </b>Sansar.Metadata<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Metadata.IScriptMetadata.Name">Name Property</h3>
        <blockquote id="P:Sansar.Metadata.IScriptMetadata.Name:member">
          <div class="msummary">
            The full name of the script class as returned by <a href="http://www.go-mono.com/docs/monodoc.ashx?link=P:System.Type.FullName">Type.FullName</a>. 
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>Name</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Metadata.IScriptMetadata.Name:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Metadata.IScriptMetadata.Name:Remarks">internal use only</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Metadata.IScriptMetadata.Name:Version Information">
            <b>Namespace: </b>Sansar.Metadata<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Metadata.IScriptMetadata.RegisterReflective">RegisterReflective Property</h3>
        <blockquote id="P:Sansar.Metadata.IScriptMetadata.RegisterReflective:member">
          <div class="msummary">
            True if <a href="../Sansar.Script/RegisterReflectiveAttribute.html">Sansar.Script.RegisterReflectiveAttribute</a> is attached to this type.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>RegisterReflective</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Metadata.IScriptMetadata.RegisterReflective:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Metadata.IScriptMetadata.RegisterReflective:Remarks">internal use only</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Metadata.IScriptMetadata.RegisterReflective:Version Information">
            <b>Namespace: </b>Sansar.Metadata<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Metadata.IScriptMetadata.Type">Type Property</h3>
        <blockquote id="P:Sansar.Metadata.IScriptMetadata.Type:member">
          <div class="msummary">
            The type of the script.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Type">Type</a> <b>Type</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Metadata.IScriptMetadata.Type:Value">
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Metadata.IScriptMetadata.Type:Remarks">internal use only</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Metadata.IScriptMetadata.Type:Version Information">
            <b>Namespace: </b>Sansar.Metadata<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>