# Sansar Tetris Development - Key Lessons Learned

## Major Architecture Evolution

### From Multi-Script to Single Script
**Problem**: Started with 4+ separate scripts (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>t<PERSON><PERSON>roller, PieceLogic) communicating via events.

**Issues Encountered**:
- Event timing problems between scripts
- State synchronization issues
- Complex debugging across multiple scripts
- Inter-script communication delays

**Solution**: Unified everything into a single `TetrisGame.cs` script.

**Benefits**:
- Eliminated all inter-script communication
- Direct access to all game state
- Simplified debugging
- Immediate response for all operations

## Player Input - Critical Discovery

### What Doesn't Work
- `SubscribeToScriptEvent()` with keyboard events
- `SubscribeToAll()` method (doesn't exist)
- Using key names like "A", "D", "S", "W"
- `InteractionComponent` for keyboard input

### What Works (Tested and Confirmed)
```csharp
// 1. Get the agent when they sit down
currentPlayer = ScenePrivate.FindAgent(data.ObjectId);

// 2. Subscribe to <PERSON><PERSON>'s actual command names
currentPlayer.Client.SubscribeToCommand("Keypad4", CommandAction.Pressed, <PERSON>leMoveLef<PERSON>, null);
```

### Actual Sansar Command Mappings
Through extensive testing, we discovered these are the ONLY keyboard events Sansar passes to scripts:
- **Keypad4** = A key
- **Keypad6** = D key  
- **Keypad2** = S key
- **Keypad8** = W key
- **SecondaryAction** = R key
- **PrimaryAction** = F key
- **Action1** = 1 key

## Material Control Breakthrough

### Direct Mesh Access Pattern
```csharp
// Store MeshComponent references for direct control
private MeshComponent[,] blockMeshes = new MeshComponent[GRID_WIDTH, GRID_HEIGHT];

// Direct material manipulation for immediate visual feedback
foreach (var material in mesh.GetRenderMaterials())
{
    var props = material.GetProperties();
    props.Tint = color;
    props.EmissiveIntensity = intensity;
    material.SetProperties(props);
}
```

**Key Requirements**:
- Mesh must be marked as **Scriptable** in Sansar editor
- Material must support **Tint** and **Emissive** properties
- Use **Standard Plus Emissive** shader for best results

## Line Clearing Bug Fix

### The Bug
When lines were cleared, they appeared visually empty but collision detection still treated them as occupied.

### Root Cause
The line dropping logic processed cleared lines from bottom-to-top with complex index adjustment, causing `gridState` array to become inconsistent with visual representation.

### The Fix
Process cleared lines from **top-to-bottom** (highest Y to lowest Y):
```csharp
// Sort cleared lines from top to bottom
clearedLines.Sort();
clearedLines.Reverse();

// Process each line, copying lines above down
for (int y = clearedY; y < GRID_HEIGHT - 1; y++)
{
    gridState[x, y] = gridState[x, y + 1]; // Copy line above
}
```

## Collision Detection Consistency

### The Problem
Having `gridState` in one script and collision detection in another caused desynchronization.

### The Solution
Keep grid state and collision detection in the same script:
```csharp
private int?[,] gridState = new int?[GRID_WIDTH, GRID_HEIGHT];

public bool IsBlockOccupied(int x, int y)
{
    return gridState[x, y] != null;
}
```

## C# 5 Compatibility Gotchas

Sansar uses C# 5, so these modern C# features don't work:
- Inline variable declarations: `if (obj.TryGet(out var component))`
- String interpolation: `$"Value: {x}"`
- Expression bodied members: `public bool IsValid => true;`

## Performance Discoveries

### Batch Spawning Pattern
Spawning 200 blocks at once caused throttling. Solution:
```csharp
// Spawn in batches with delays
for (int i = 0; i < total; i += batchSize)
{
    SpawnBatch(i, batchSize);
    StartCoroutine(DelayedContinue, batchDelay);
}
```

### Rainbow Debug Mode
Critical for testing material control:
```csharp
if (DebugRainbowMode)
{
    StartCoroutine(RainbowDebugSequence);
}
```

This visual test immediately shows if material control is working correctly.

## Seat Detection Pattern

### Working Seat Detection
```csharp
// Get RigidBodyComponent from the seat object
private RigidBodyComponent rigidBody;

// In Init()
ObjectPrivate.TryGetFirstComponent(out rigidBody);
sitSubscription = rigidBody.SubscribeToSitObject(SitEventType.Start, OnPlayerSitDown);

private void OnPlayerSitDown(SitObjectData data)
{
    currentPlayer = ScenePrivate.FindAgent(data.ObjectId);
    // Now you can subscribe to their commands
}
```

## Key Success Factors

1. **Single Script Architecture**: Eliminates complexity and timing issues
2. **Proper Command Subscription**: Use `agent.Client.SubscribeToCommand` with correct Sansar command names
3. **Direct Material Control**: Store `MeshComponent` references for immediate visual updates
4. **Consistent State Management**: Keep related data structures in same script
5. **Proper Line Clearing Logic**: Process from top-to-bottom for consistent grid state
6. **Clean Visual Sync Architecture**: Separate game state from visual state with master sync method
7. **Debug Modes**: Visual testing (rainbow mode) crucial for verifying material control
8. **Batch Operations**: Prevent Sansar throttling with delayed batch processing

## Testing Insights

- **Visual confirmation is critical**: What looks right isn't always working correctly (line clearing bug)
- **Sansar's command system is limited**: Only specific events are passed to scripts
- **Material requirements are strict**: Mesh scriptability and shader properties must be correct
- **Logging is essential**: Detailed logs help identify timing and state issues
- **Iterative testing**: Small changes with immediate testing prevented larger architectural issues

## Visual State Management Breakthrough

The final major issue was inconsistent visual updates during line clearing. The solution was implementing a clean separation:

**Game State**: `gridState[,]` array = single source of truth for permanent blocks
**Visual State**: What's displayed on screen  
**Sync Method**: `SyncAllVisualsToGridState()` = master method that updates all 200 blocks

### The Problem
Incremental visual updates during line clearing caused inconsistencies:
- Some blocks appeared cleared but still acted as occupied in collision detection
- Some blocks visually remained but could be passed through
- Blocks above cleared lines didn't visually fall down

### The Solution
```csharp
// Update game state only
gridState[x, y] = pieceType;

// Then sync ALL visuals to match game state
SyncAllVisualsToGridState();
```

**Key Insight**: With only 200 blocks, updating all visuals is more reliable than trying to optimize with incremental updates.

This project demonstrated that sometimes the "simpler" unified approach is actually more robust than complex distributed architectures, especially in constrained environments like Sansar scripting.