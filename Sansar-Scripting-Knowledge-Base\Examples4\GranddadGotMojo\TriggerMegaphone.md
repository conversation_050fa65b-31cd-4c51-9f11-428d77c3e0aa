# Trigger Megaphone

This script triggers megaphones for everyone within a certain distance of the object that this script is in.

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/Megaphone.png)

**Detection Range** - distance in meters for the avatars that will be given megaphone.  It scans after receiving the Megaphone On Command.

**Megaphone On Cmd** - when it hears this in chat it will do the scan and assign everybody within the Detection Range the Megaphone.

**Megaphone Off Cmd** - turns off the Megaphone for everyone in the experience.

The following is how to send the Megaphone on command in chat.

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/Megaphone.png)
