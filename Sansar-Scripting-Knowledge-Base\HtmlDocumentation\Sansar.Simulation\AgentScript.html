<html>
  <head>
    <title>Sansar.Simulation.AgentScript</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.AgentScript">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.AgentScript:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.AgentScript:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.AgentScript:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.AgentScript">AgentScript  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.AgentScript:Summary">
            Extend AgentScript to create a script to be attached directly to an agent.
            For future use: Sansar does not currently support adding scripts to agents.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.AgentScript:Signature">[Sansar.Script.Interface]<br />public abstract class  <b>AgentScript</b> : <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.AgentScript:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.AgentScript:Docs:Remarks">
        <p>Override <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Init()">Sansar.Script.ScriptBase.Init()</a> for script initialization, primarily event subscriptions.</p>
        <p>Use <a href="../Sansar.Simulation/AgentScript.html#P:Sansar.Simulation.AgentScript.ObjectPrivate">AgentScript.ObjectPrivate</a> to access the ObjectPrivate the object that represents the Agent.</p>
        <p>Use <a href="../Sansar.Simulation/AgentScript.html#P:Sansar.Simulation.AgentScript.ScenePublic">AgentScript.ScenePublic</a> to access the a limited API for the Scene the agent is in.</p>
        <p>Use <a href="../Sansar.Simulation/AgentScript.html#P:Sansar.Simulation.AgentScript.AgentPrivate">AgentScript.AgentPrivate</a> to access the Agent the object is attached to.</p>
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.AgentScript:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.
							</p>
        <h2 class="Section">Protected Constructors</h2>
        <div class="SectionBox" id="Protected Constructors">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <div>
                    <b>
                      <a href="#C:Sansar.Simulation.AgentScript()">AgentScript</a>
                    </b>()</div>
                </td>
                <td>
            This constructor is called before any properties have been set. Override <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Init()">Sansar.Script.ScriptBase.Init()</a> to initialize the script after properties have been set.
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.AgentScript.AgentPrivate">AgentPrivate</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a>
                  </i>. 
            The Agent API for the agent this script is attached to.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#P:Sansar.Script.ScriptBase.Log">Log</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/Log.html">Sansar.Script.Log</a>
                  </i>. 
            Gets the script console.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#P:Sansar.Script.ScriptBase.Memory">Memory</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/Memory.html">Sansar.Script.Memory</a>
                  </i>. 
            Memory information for the pool this script is in.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.AgentScript.ObjectPrivate">ObjectPrivate</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/ObjectPrivate.html">ObjectPrivate</a>
                  </i>. 
            The ObjectPrivate this script is attached to if it is attached to an object.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Simulation.AgentScript.ScenePublic">ScenePublic</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/ScenePublic.html">ScenePublic</a>
                  </i>. 
            Basic scene information for the current scene.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#P:Sansar.Script.ScriptBase.Script">Script</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/ScriptHandle.html">Sansar.Script.ScriptHandle</a>
                  </i>. 
            Script handle to this script.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Protected Properties</h2>
        <div class="SectionBox" id="Protected Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/Reflective.html#P:Sansar.Script.Reflective.AllowedContexts">AllowedContexts</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/Reflective+Context.html">Sansar.Script.Reflective.Context</a>
                  </i>. Internal Use Only. Overridden by subclasses to return only those contexts requested which are allowed for that type of script. (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Sansar.Script.Reflective</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#P:Sansar.Script.ScriptBase.CurrentCoroutine">CurrentCoroutine</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/ICoroutine.html">Sansar.Script.ICoroutine</a>
                  </i>. 
            Gets the ICoroutine interface for the current coroutine.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#P:Sansar.Script.ScriptBase.MaxCoroutines">MaxCoroutines</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>
                  </i>. 
            The maximum number of coroutines that a single script can run.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#P:Sansar.Script.ScriptBase.PendingEventCount">PendingEventCount</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>
                  </i>. 
            The number of events currently waiting to be processed.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/Reflective.html#P:Sansar.Script.Reflective.ReflectiveContexts">ReflectiveContexts</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Script/Reflective+Context.html">Sansar.Script.Reflective.Context</a>
                  </i>. 
            Override ReflectiveContexts to limit which contexts this Reflective interface is available in when registered with.
             (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Sansar.Script.Reflective</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/Reflective.html#P:Sansar.Script.Reflective.ReflectiveName">ReflectiveName</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. 
            Override ReflectiveName to change which name this class will be registered as in the Reflective system.
             (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Sansar.Script.Reflective</a>.</i>)</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/Reflective.html#M:Sansar.Script.Reflective.AsInterface``1()">AsInterface&lt;TInterface&gt;</a>
                  </b>()<nobr> : <i title="The interface describing the methods and properties desired.">TInterface</i></nobr><blockquote>
            Returns a TInterface object if one can be created, null otherwise
             (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Sansar.Script.Reflective</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/Reflective.html#M:Sansar.Script.Reflective.FullInterface(System.String)">FullInterface</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>
            Generates a string which shows all the members which can be reflected.
             (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Sansar.Script.Reflective</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>abstract </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Init()">Init</a>
                  </b>()<blockquote> 
            Init() is called after all interfaces have been initialized.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/Reflective.html#M:Sansar.Script.Reflective.Register()">Register</a>
                  </b>()<blockquote>
            Register this object to be found with <a href="../Sansar.Simulation/ScenePrivate.html#M:Sansar.Simulation.ScenePrivate.FindReflective(System.String)">ScenePrivate.FindReflective(string)</a> (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Sansar.Script.Reflective</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/Reflective.html#M:Sansar.Script.Reflective.Unregister()">Unregister</a>
                  </b>()<blockquote>
            Unregister this object so it will not be found with <a href="../Sansar.Simulation/ScenePrivate.html#M:Sansar.Simulation.ScenePrivate.FindReflective(System.String)">ScenePrivate.FindReflective(string)</a> (<i>Inherited from <a href="../Sansar.Script/Reflective.html">Sansar.Script.Reflective</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Yield()">Yield</a>
                  </b>()<blockquote>
            Yield to let other coroutines or events run.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Protected Methods</h2>
        <div class="SectionBox" id="Protected Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.GetAllCoroutines()">GetAllCoroutines</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.IReadOnlyList`1">IReadOnlyList&lt;Sansar.Script.ICoroutine&gt;</a></nobr><blockquote>
            A list of all coroutines for this script.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.GetCoroutineCount()">GetCoroutineCount</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a></nobr><blockquote>
            The total number of coroutines running on this script.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Lock(System.Object)">Lock</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.IDisposable">IDisposable</a></nobr><blockquote>
            Use to create a critical section with a using statement, ensuring that no other coroutines or scripts (via Reflective) may enter this code section while one coroutine or script is in it.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(System.String)">PostScriptEvent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<blockquote>
            Post the event for all scripts.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(System.String,Sansar.Script.Reflective)">PostScriptEvent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/Reflective.html">Sansar.Script.Reflective</a>)<blockquote>
            Post the event for all scripts.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">PostScriptEvent</a>
                  </b>(<a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="../Sansar.Script/Reflective.html">Sansar.Script.Reflective</a>)<blockquote>
            Post the event for the target script.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(System.String,System.Object)">PostSimpleScriptEvent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>)<blockquote>
            Deprecated. Use <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId, string, Sansar.Script.Reflective)</a> (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostSimpleScriptEvent(Sansar.Script.ScriptId,System.String,System.Object)">PostSimpleScriptEvent</a>
                  </b>(<a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>)<blockquote>
            Deprecated. Use <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId,System.String,Sansar.Script.Reflective)">Sansar.Script.ScriptBase.PostScriptEvent(Sansar.Script.ScriptId, string, Sansar.Script.Reflective)</a> (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.ReleaseLock(System.Object)">ReleaseLock</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>)<blockquote>
            Release a lock if held.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.SetDefaultErrorMode()">SetDefaultErrorMode</a>
                  </b>()<blockquote>
            Write errors to the Script Debug Console instead of throwing exceptions for some common API errors while still throwing exceptions for the more serious errors.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.SetRelaxedErrorMode()">SetRelaxedErrorMode</a>
                  </b>()<blockquote>
            Write errors to the Script Debug Console instead of throw exceptions for almost all Sansar API errors.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.SetStrictErrorMode()">SetStrictErrorMode</a>
                  </b>()<blockquote>
            Throw exceptions for almost all Sansar API errors.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.StartCoroutine(System.Action,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<nobr> : <a href="../Sansar.Script/ICoroutine.html">Sansar.Script.ICoroutine</a></nobr><blockquote>
            Starts a coroutine on the current script.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.StartCoroutine``1(System.Action{``0},``0,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine&lt;T&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;T&gt;</a>, <i title="Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<nobr> : <a href="../Sansar.Script/ICoroutine.html">Sansar.Script.ICoroutine</a></nobr><blockquote>
            Starts a coroutine on the current script.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.StartCoroutine``2(System.Action{``0,``1},``0,``1,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine&lt;T,T1&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`2">Action&lt;T, T1&gt;</a>, <i title="Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T</i>, <i title="Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T1</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<nobr> : <a href="../Sansar.Script/ICoroutine.html">Sansar.Script.ICoroutine</a></nobr><blockquote>
            Starts a coroutine on the current script.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.StartCoroutine``3(System.Action{``0,``1,``2},``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine&lt;T,T1,T2&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`3">Action&lt;T, T1, T2&gt;</a>, <i title="Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T</i>, <i title="Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T1</i>, <i title="Type of the third parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T2</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<nobr> : <a href="../Sansar.Script/ICoroutine.html">Sansar.Script.ICoroutine</a></nobr><blockquote>
            Starts a coroutine on the current script.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.StartCoroutine``4(System.Action{``0,``1,``2,``3},``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent})">StartCoroutine&lt;T,T1,T2,T3&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`4">Action&lt;T, T1, T2, T3&gt;</a>, <i title="Type of the first parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T</i>, <i title="Type of the second parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T1</i>, <i title="Type of the third parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T2</i>, <i title="Type of the fourth parameter to pass to the coroutine when it is run. This can usually be derived from the Action.">T3</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<nobr> : <a href="../Sansar.Script/ICoroutine.html">Sansar.Script.ICoroutine</a></nobr><blockquote>
            Starts a coroutine on the current script.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean)">SubscribeToScriptEvent</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.ScriptEventData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>
            Subscribes to events sent by other scripts
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.SubscribeToScriptEvent(Sansar.Script.ScriptId,System.String,System.Action{Sansar.Script.ScriptEventData},System.Boolean)">SubscribeToScriptEvent</a>
                  </b>(<a href="../Sansar.Script/ScriptId.html">Sansar.Script.ScriptId</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.ScriptEventData&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)<nobr> : <a href="../Sansar.Script/IEventSubscription.html">Sansar.Script.IEventSubscription</a></nobr><blockquote>
            Subscribes to events sent only by a specific script.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Terminate(System.String)">Terminate</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>)<blockquote>
            Terminates this script immediately.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Wait(System.Double)">Wait</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Double">double</a>)<blockquote>
            Delays execution of the current coroutine for the specified time.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Wait(System.TimeSpan)">Wait</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.TimeSpan">TimeSpan</a>)<blockquote>
            Delays execution of the current coroutine for the specified time.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(Sansar.Script.ICoroutine)">WaitFor</a>
                  </b>(<a href="../Sansar.Script/ICoroutine.html">Sansar.Script.ICoroutine</a>)<blockquote>
            Block the current coroutine until otherCoroutine finishes.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Action{System.Action{Sansar.Script.OperationCompleteEvent}})">WaitFor</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Action&lt;Sansar.Script.OperationCompleteEvent&gt;&gt;</a>)<nobr> : <a href="../Sansar.Script/OperationCompleteEvent.html">Sansar.Script.OperationCompleteEvent</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription})">WaitFor</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`4">Func&lt;Action&lt;Sansar.Script.EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">WaitFor</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`3">Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},System.Action)">WaitFor</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`3">Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0)">WaitFor&lt;ARG1&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`5">Func&lt;ARG1, Action&lt;Sansar.Script.EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0)">WaitFor&lt;ARG1&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`4">Func&lt;ARG1, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``1(System.Func{``0,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,System.Action)">WaitFor&lt;ARG1&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`4">Func&lt;ARG1, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``1(System.Action{``0,System.Action{Sansar.Script.OperationCompleteEvent}},``0)">WaitFor&lt;T1&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`2">Action&lt;T1, Action&lt;Sansar.Script.OperationCompleteEvent&gt;&gt;</a>, <i title="The type of the argument to func.">T1</i>)<nobr> : <a href="../Sansar.Script/OperationCompleteEvent.html">Sansar.Script.OperationCompleteEvent</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1)">WaitFor&lt;ARG1,ARG2&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`6">Func&lt;ARG1, ARG2, Action&lt;Sansar.Script.EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1)">WaitFor&lt;ARG1,ARG2&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`5">Func&lt;ARG1, ARG2, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``2(System.Func{``0,``1,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,System.Action)">WaitFor&lt;ARG1,ARG2&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`5">Func&lt;ARG1, ARG2, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``2(System.Action{``0,``1,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1)">WaitFor&lt;T1,T2&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`3">Action&lt;T1, T2, Action&lt;Sansar.Script.OperationCompleteEvent&gt;&gt;</a>, <i title="The type of the argument to func.">T1</i>, <i title="The type of the argument to func.">T2</i>)<nobr> : <a href="../Sansar.Script/OperationCompleteEvent.html">Sansar.Script.OperationCompleteEvent</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2)">WaitFor&lt;ARG1,ARG2,ARG3&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`7">Func&lt;ARG1, ARG2, ARG3, Action&lt;Sansar.Script.EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2)">WaitFor&lt;ARG1,ARG2,ARG3&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`6">Func&lt;ARG1, ARG2, ARG3, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``3(System.Func{``0,``1,``2,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,System.Action)">WaitFor&lt;ARG1,ARG2,ARG3&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`6">Func&lt;ARG1, ARG2, ARG3, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``3(System.Action{``0,``1,``2,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2)">WaitFor&lt;T1,T2,T3&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`4">Action&lt;T1, T2, T3, Action&lt;Sansar.Script.OperationCompleteEvent&gt;&gt;</a>, <i title="The type of the argument to func.">T1</i>, <i title="The type of the argument to func.">T2</i>, <i title="The type of the argument to func.">T3</i>)<nobr> : <a href="../Sansar.Script/OperationCompleteEvent.html">Sansar.Script.OperationCompleteEvent</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`8">Func&lt;ARG1, ARG2, ARG3, ARG4, Action&lt;Sansar.Script.EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <i title="The type of the argument to func.">ARG4</i>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`7">Func&lt;ARG1, ARG2, ARG3, ARG4, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <i title="The type of the argument to func.">ARG4</i>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``4(System.Func{``0,``1,``2,``3,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,System.Action)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`7">Func&lt;ARG1, ARG2, ARG3, ARG4, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <i title="The type of the argument to func.">ARG4</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``4(System.Action{``0,``1,``2,``3,System.Action{Sansar.Script.OperationCompleteEvent}},``0,``1,``2,``3)">WaitFor&lt;T1,T2,T3,T4&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`5">Action&lt;T1, T2, T3, T4, Action&lt;Sansar.Script.OperationCompleteEvent&gt;&gt;</a>, <i title="The type of the argument to func.">T1</i>, <i title="The type of the argument to func.">T2</i>, <i title="The type of the argument to func.">T3</i>, <i title="The type of the argument to func.">T4</i>)<nobr> : <a href="../Sansar.Script/OperationCompleteEvent.html">Sansar.Script.OperationCompleteEvent</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Action{Sansar.Script.CancelData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4,ARG5&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`9">Func&lt;ARG1, ARG2, ARG3, ARG4, ARG5, Action&lt;Sansar.Script.EventData&gt;, Action&lt;Sansar.Script.CancelData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <i title="The type of the argument to func.">ARG4</i>, <i title="The type of the argument to func.">ARG5</i>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4,ARG5&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`8">Func&lt;ARG1, ARG2, ARG3, ARG4, ARG5, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <i title="The type of the argument to func.">ARG4</i>, <i title="The type of the argument to func.">ARG5</i>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor``5(System.Func{``0,``1,``2,``3,``4,System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription},``0,``1,``2,``3,``4,System.Action)">WaitFor&lt;ARG1,ARG2,ARG3,ARG4,ARG5&gt;</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Func`8">Func&lt;ARG1, ARG2, ARG3, ARG4, ARG5, Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;</a>, <i title="The type of the argument to func.">ARG1</i>, <i title="The type of the argument to func.">ARG2</i>, <i title="The type of the argument to func.">ARG3</i>, <i title="The type of the argument to func.">ARG4</i>, <i title="The type of the argument to func.">ARG5</i>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action">Action</a>)<nobr> : <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a></nobr><blockquote>
            Use to pause the script until an event happens.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitForLock(System.Object)">WaitForLock</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>)<blockquote>
            WaitFor and take a lock on Token
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitForSignal()">WaitForSignal</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a></nobr><blockquote>
            Wait the current coroutine until at least 1 signal is sent to it through the ICoroutine interface.
             (<i>Inherited from <a href="../Sansar.Script/ScriptBase.html">Sansar.Script.ScriptBase</a>.</i>)</blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.AgentScript:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="C:Sansar.Simulation.AgentScript()">AgentScript Constructor</h3>
        <blockquote id="C:Sansar.Simulation.AgentScript():member">
          <div class="msummary">
            This constructor is called before any properties have been set. Override <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Init()">Sansar.Script.ScriptBase.Init()</a> to initialize the script after properties have been set.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />protected  <b>AgentScript</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="C:Sansar.Simulation.AgentScript():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="C:Sansar.Simulation.AgentScript():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.AgentScript.AgentPrivate">AgentPrivate Property</h3>
        <blockquote id="P:Sansar.Simulation.AgentScript.AgentPrivate:member">
          <div class="msummary">
            The Agent API for the agent this script is attached to.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/AgentPrivate.html">AgentPrivate</a> <b>AgentPrivate</b>  { protected get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.AgentScript.AgentPrivate:Value">The Agent this script is attached to if the script is on an avatar, null otherwise.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.AgentScript.AgentPrivate:Remarks">The Agent will be set before <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.Init">Sansar.Script.ScriptBase.Init</a>  is called.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.AgentScript.AgentPrivate:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.AgentScript.ObjectPrivate">ObjectPrivate Property</h3>
        <blockquote id="P:Sansar.Simulation.AgentScript.ObjectPrivate:member">
          <div class="msummary">
            The ObjectPrivate this script is attached to if it is attached to an object.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/ObjectPrivate.html">ObjectPrivate</a> <b>ObjectPrivate</b>  { protected get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.AgentScript.ObjectPrivate:Value">The scene object this script is attached to if it is attached to an object, null otherwise.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.AgentScript.ObjectPrivate:Remarks"> For AgentScripts this is the Agent's ObjectPrivate of their avatar.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.AgentScript.ObjectPrivate:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Simulation.AgentScript.ScenePublic">ScenePublic Property</h3>
        <blockquote id="P:Sansar.Simulation.AgentScript.ScenePublic:member">
          <div class="msummary">
            Basic scene information for the current scene.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.NonReflective]<br />[get: Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/ScenePublic.html">ScenePublic</a> <b>ScenePublic</b>  { protected get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Simulation.AgentScript.ScenePublic:Value">The Scene this object is in.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.AgentScript.ScenePublic:Remarks">This value will be set before Init is called.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Simulation.AgentScript.ScenePublic:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>