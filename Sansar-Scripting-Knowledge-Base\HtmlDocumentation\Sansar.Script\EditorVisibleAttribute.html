<html>
  <head>
    <title>Sansar.Script.EditorVisibleAttribute</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Script</a> : <a href="index.html">Sansar.Script Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Script.EditorVisibleAttribute">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Script.EditorVisibleAttribute:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Script.EditorVisibleAttribute:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Script.EditorVisibleAttribute:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Script.EditorVisibleAttribute">EditorVisibleAttribute  Class</h1>
    <p class="Summary" id="T:Sansar.Script.EditorVisibleAttribute:Summary">
            Explicitly set the editor visibility of a script field or class.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Script.EditorVisibleAttribute:Signature">[System.AttributeUsage(System.AttributeTargets.Class | System.AttributeTargets.Field)]<br />public class  <b>EditorVisibleAttribute</b> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Attribute">Attribute</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Script.EditorVisibleAttribute:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Script.EditorVisibleAttribute:Docs:Remarks">By default only public fields are exposed in the editor. 
            Using this attribute, protected, private or internal fields may be exposed as well.
            Classes can be hidden setting this attribute to false. </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Script.EditorVisibleAttribute:Docs:Version Information">
        <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Attribute">Attribute</a>.
							</p>
        <h2 class="Section">Public Constructors</h2>
        <div class="SectionBox" id="Public Constructors">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <div>
                    <b>
                      <a href="#C:Sansar.Script.EditorVisibleAttribute()">EditorVisibleAttribute</a>
                    </b>()</div>
                </td>
                <td>
            Mark a non-public script field as visible in the editor.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <div>
                    <b>
                      <a href="#C:Sansar.Script.EditorVisibleAttribute(System.Boolean)">EditorVisibleAttribute</a>
                    </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>)</div>
                </td>
                <td>
            Explicitly set the visibility of a field or class
            </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#P:Sansar.Script.EditorVisibleAttribute.Visible">Visible</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            The visibility value.
            </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Script.EditorVisibleAttribute:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="C:Sansar.Script.EditorVisibleAttribute()">EditorVisibleAttribute Constructor</h3>
        <blockquote id="C:Sansar.Script.EditorVisibleAttribute():member">
          <div class="msummary">
            Mark a non-public script field as visible in the editor.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public  <b>EditorVisibleAttribute</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="C:Sansar.Script.EditorVisibleAttribute():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="C:Sansar.Script.EditorVisibleAttribute():Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="C:Sansar.Script.EditorVisibleAttribute(System.Boolean)">EditorVisibleAttribute Constructor</h3>
        <blockquote id="C:Sansar.Script.EditorVisibleAttribute(System.Boolean):member">
          <div class="msummary">
            Explicitly set the visibility of a field or class
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public  <b>EditorVisibleAttribute</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> visible)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="C:Sansar.Script.EditorVisibleAttribute(System.Boolean):Parameters">
            <dl>
              <dt>
                <i>visible</i>
              </dt>
              <dd>
              </dd>
            </dl>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="C:Sansar.Script.EditorVisibleAttribute(System.Boolean):Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="C:Sansar.Script.EditorVisibleAttribute(System.Boolean):Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Script.EditorVisibleAttribute.Visible">Visible Property</h3>
        <blockquote id="P:Sansar.Script.EditorVisibleAttribute.Visible:member">
          <div class="msummary">
            The visibility value.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>Visible</b>  { get; set; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Script.EditorVisibleAttribute.Visible:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Script.EditorVisibleAttribute.Visible:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Script.EditorVisibleAttribute.Visible:Version Information">
            <b>Namespace: </b>Sansar.Script<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>