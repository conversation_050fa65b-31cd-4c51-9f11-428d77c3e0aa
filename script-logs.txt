<2025-07-26T23:17:33.690Z> [KafkaMana<PERSON>] Got script console log level 1 tag '' timestamp 1753571851897 message TetrisGame: Player stood up - current state: GameOver
<2025-07-26T23:17:33.690Z> [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>] Got script console log level 1 tag '' timestamp 1753571851898 message TetrisGame: Unsubscribed from all player commands
<2025-07-26T23:17:33.690Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571851898 message TetrisGame: Calling EndGame() due to player stand up
<2025-07-26T23:17:33.707Z> [<PERSON><PERSON><PERSON>M<PERSON><PERSON>] Got script console log level 1 tag '' timestamp 1753571851898 message TetrisGame: Game ended. Score: 0, Lines: 0, Level: 1
<2025-07-26T23:17:33.707Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571851898 message TetrisGame: Player controls deactivated
<2025-07-26T23:17:33.707Z> [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>] Got script console log level 1 tag '' timestamp 1753571851898 message TetrisGame: Game ended - waiting for player to restart manually
<2025-07-26T23:17:33.707Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571851898 message TetrisGame: Player session cleanup complete
<2025-07-26T23:17:38.445Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856661 message TetrisGame: Player sat down - previous state: GameOver
<2025-07-26T23:17:38.462Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856661 message TetrisGame: Grid status - blocksSpawned: 200/200, gridInitialized: True
<2025-07-26T23:17:38.462Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856661 message TetrisGame: Grid ready - current state is GameOver, starting new game
<2025-07-26T23:17:38.462Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856661 message TetrisGame: Force clearing spawn area (Y=16-19) for clean restart
<2025-07-26T23:17:38.462Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856662 message TetrisGame: Game variables reset for clean restart
<2025-07-26T23:17:38.462Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856662 message TetrisGame: Grid cleared for new game
<2025-07-26T23:17:38.462Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856662 message TetrisGame: SubscribeToPlayerControls called
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856662 message TetrisGame: Player validation passed, proceeding with subscriptions
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856662 message TetrisGame: Starting command subscriptions
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856662 message TetrisGame: Subscribing to Keypad4 (A key)
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856663 message TetrisGame: Subscribing to Keypad6 (D key)
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856663 message TetrisGame: Subscribing to Keypad2 (S key)
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856663 message TetrisGame: Subscribing to Keypad8 (W key)
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856663 message TetrisGame: Subscribing to SecondaryAction (R key)
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856663 message TetrisGame: Subscribing to PrimaryAction (F key)
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856672 message TetrisGame: Subscribing to Action1 (1 key)
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856672 message TetrisGame: Subscribing to key releases for repeat system
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856672 message TetrisGame: Player controls activated using agent.Client.SubscribeToCommand
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856672 message TetrisGame: Controls: A=Left, D=Right, S=SoftDrop, W=SlowDown, R=Rotate, F=HardDrop, 1=RotateCCW
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856672 message TetrisGame: SpawnNewPiece - Starting
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856672 message TetrisGame: SpawnNewPiece - Selected piece type S
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856672 message TetrisGame: SpawnNewPiece - About to call CanPlacePiece
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856673 message TetrisGame: Checking spawn area (Y=16-19) before spawning...
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856673 message TetrisGame: Spawn area is clear - safe to spawn
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856673 message TetrisGame: SpawnNewPiece - CanPlacePiece returned true
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856673 message TetrisGame: Successfully validated spawn position for S at <5,18,0>
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856673 message TetrisGame: Spawned new piece: S at <5,18,0> with 4 blocks
<2025-07-26T23:17:38.478Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571856673 message TetrisGame: Game started
<2025-07-26T23:17:39.481Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571857689 message TetrisGame: GameLoop - Piece cannot move down, calling OnPieceLanded
<2025-07-26T23:17:39.481Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571857689 message TetrisGame: Piece landed - locking into grid state
<2025-07-26T23:17:39.497Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571857690 message TetrisGame: Game over detected - block found at (4, 18)
<2025-07-26T23:17:39.497Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571857690 message TetrisGame: Game ended. Score: 0, Lines: 0, Level: 1
<2025-07-26T23:17:39.497Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571857690 message TetrisGame: Marking final piece blocks for continuous game over flash
<2025-07-26T23:17:39.497Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571857690 message TetrisGame: Marked final piece block at (5, 18) for game over flash
<2025-07-26T23:17:39.497Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571857690 message TetrisGame: Marked final piece block at (4, 18) for game over flash
<2025-07-26T23:17:39.497Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571857690 message TetrisGame: Marked final piece block at (5, 19) for game over flash
<2025-07-26T23:17:39.497Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571857690 message TetrisGame: Marked final piece block at (6, 19) for game over flash
<2025-07-26T23:17:39.497Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571857690 message TetrisGame: Game over flash enabled for 4 blocks
<2025-07-26T23:17:39.497Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571857690 message TetrisGame: Player controls deactivated
<2025-07-26T23:17:39.497Z> [KafkaManager] Got script console log level 1 tag '' timestamp 1753571857690 message TetrisGame: Game ended - waiting for player to restart manually
