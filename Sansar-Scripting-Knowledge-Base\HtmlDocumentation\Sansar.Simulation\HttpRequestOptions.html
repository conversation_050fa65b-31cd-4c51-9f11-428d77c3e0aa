<html>
  <head>
    <title>Sansar.Simulation.HttpRequestOptions</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.HttpRequestOptions">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.HttpRequestOptions:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.HttpRequestOptions:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.HttpRequestOptions:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.HttpRequestOptions">HttpRequestOptions  Struct</h1>
    <p class="Summary" id="T:Sansar.Simulation.HttpRequestOptions:Summary">
            Options used for <a href="../Sansar.Simulation/HttpClient.html#M:Sansar.Simulation.HttpClient.Request(System.String,Sansar.Simulation.HttpRequestOptions,System.Action{Sansar.Simulation.HttpClient.RequestData})">HttpClient.Request(string, HttpRequestOptions, Action&lt;Sansar.Simulation.HttpClient.RequestData&gt;)</a></p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.HttpRequestOptions:Signature">[Sansar.Script.Interface]<br />public struct  <b>HttpRequestOptions</b></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.HttpRequestOptions:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.HttpRequestOptions:Docs:Remarks">
        <span class="NotEntered">Documentation for this section has not yet been entered.</span>
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.HttpRequestOptions:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.ValueType">ValueType</a>.
							</p>
        <h2 class="Section">Public Fields</h2>
        <div class="SectionBox" id="Public Fields">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.HttpRequestOptions.Body">Body</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. 
            Body for <a href="../Sansar.Simulation/HttpRequestMethod.html">Sansar.Simulation.HttpRequestMethod</a>s which use one.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.HttpRequestOptions.DisableRedirect">DisableRedirect</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            Disable automatic redirects.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.HttpRequestOptions.Headers">Headers</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.Dictionary`2">Dictionary&lt;string, string&gt;</a>
                  </i>. 
            Http headers.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.HttpRequestOptions.Method">Method</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="../Sansar.Simulation/HttpRequestMethod.html">HttpRequestMethod</a>
                  </i>. 
            The <a href="../Sansar.Simulation/HttpRequestMethod.html">Sansar.Simulation.HttpRequestMethod</a> to use for this request.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.HttpRequestOptions.Parameters">Parameters</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.Dictionary`2">Dictionary&lt;string, string&gt;</a>
                  </i>. 
            Request parameters.
            </td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <b>
                    <a href="#F:Sansar.Simulation.HttpRequestOptions.RetrieveAllHeaders">RetrieveAllHeaders</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            If false, minimal or no response headers will be returned.
            </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.HttpRequestOptions:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="F:Sansar.Simulation.HttpRequestOptions.Body">Body Field</h3>
        <blockquote id="F:Sansar.Simulation.HttpRequestOptions.Body:member">
          <div class="msummary">
            Body for <a href="../Sansar.Simulation/HttpRequestMethod.html">Sansar.Simulation.HttpRequestMethod</a>s which use one.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>Body</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.HttpRequestOptions.Body:Remarks">May be null.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.HttpRequestOptions.Body:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.HttpRequestOptions.DisableRedirect">DisableRedirect Field</h3>
        <blockquote id="F:Sansar.Simulation.HttpRequestOptions.DisableRedirect:member">
          <div class="msummary">
            Disable automatic redirects.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>DisableRedirect</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.HttpRequestOptions.DisableRedirect:Remarks">The maximum number of redirects is determined by the server</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.HttpRequestOptions.DisableRedirect:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.HttpRequestOptions.Headers">Headers Field</h3>
        <blockquote id="F:Sansar.Simulation.HttpRequestOptions.Headers:member">
          <div class="msummary">
            Http headers.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.Dictionary`2">Dictionary&lt;string, string&gt;</a> <b>Headers</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.HttpRequestOptions.Headers:Remarks">May be null.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.HttpRequestOptions.Headers:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.HttpRequestOptions.Method">Method Field</h3>
        <blockquote id="F:Sansar.Simulation.HttpRequestOptions.Method:member">
          <div class="msummary">
            The <a href="../Sansar.Simulation/HttpRequestMethod.html">Sansar.Simulation.HttpRequestMethod</a> to use for this request.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/HttpRequestMethod.html">HttpRequestMethod</a> <b>Method</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.HttpRequestOptions.Method:Remarks">Defaults to <a href="../Sansar.Simulation/HttpRequestMethod.html#F:Sansar.Simulation.HttpRequestMethod.GET">HttpRequestMethod.GET</a></div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.HttpRequestOptions.Method:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.HttpRequestOptions.Parameters">Parameters Field</h3>
        <blockquote id="F:Sansar.Simulation.HttpRequestOptions.Parameters:member">
          <div class="msummary">
            Request parameters.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Collections.Generic.Dictionary`2">Dictionary&lt;string, string&gt;</a> <b>Parameters</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.HttpRequestOptions.Parameters:Remarks">May be null. If non-null, the values will be url-encoded.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.HttpRequestOptions.Parameters:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="F:Sansar.Simulation.HttpRequestOptions.RetrieveAllHeaders">RetrieveAllHeaders Field</h3>
        <blockquote id="F:Sansar.Simulation.HttpRequestOptions.RetrieveAllHeaders:member">
          <div class="msummary">
            If false, minimal or no response headers will be returned.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>RetrieveAllHeaders</b> </div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.HttpRequestOptions.RetrieveAllHeaders:Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="F:Sansar.Simulation.HttpRequestOptions.RetrieveAllHeaders:Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>