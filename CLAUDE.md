# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Sansar Tetris implementation** using a unified single-script architecture with grid-based visibility control. The game creates 400 total blocks (200 game blocks + 200 background blocks) in a 10x20 grid and controls their visibility, color, and effects rather than moving physical objects. All functionality is contained in a single `TetrisGame.cs` script attached to a seat object, eliminating inter-script communication complexity.

## Build System

### Local Compilation Setup
Scripts are compiled locally using the .NET Framework C# compiler to catch errors before uploading to Sansar:

```bash
"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe" /target:library /reference:"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll" /reference:"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll" /out:"bin/[ScriptName].dll" [ScriptName].cs
```

### Quick Build Commands
```bash
# Build the unified Tetris game script
"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe" /target:library /reference:"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll" /reference:"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll" /out:"bin/TetrisGame.dll" TetrisGame.cs

# Test basic script compilation
"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe" /target:library /reference:"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll" /reference:"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll" /out:"bin/HelloSansarTest.dll" HelloSansarTest.cs
```

## Architecture Overview

### Unified Single-Script Architecture
The game uses a **single TetrisGame.cs script** that combines all functionality:

1. **Seat Detection** - Built-in player interaction via `RigidBodyComponent.SubscribeToSitObject`
2. **Game Logic** - Piece spawning, movement, rotation, and collision detection
3. **Grid Management** - 400 blocks total (200 game + 200 background) with direct references
4. **Material Control** - Direct mesh manipulation via `MeshComponent.GetRenderMaterials()`
5. **Input Handling** - Player controls via `agent.Client.SubscribeToCommand`
6. **Visual Updates** - Independent visual sync cycle for consistent rendering

### Communication Patterns

**Direct Method Calls Only:**
- No event-based messaging between scripts
- All methods called directly within the same class
- Immediate visual updates without event delays
- Direct array access to block objects and mesh components

### Grid-Based Architecture
Unlike traditional Tetris, this implementation:
- **Never moves objects** - only changes visibility and materials
- **Spawns 400 static blocks** at scene start using `ScenePrivate.CreateCluster()`
  - 200 game blocks for active gameplay
  - 200 background blocks for visual grid reference
- **Represents pieces as data patterns** that control which blocks are visible
- **Clears lines by hiding blocks** and shifting grid state arrays
- **Uses visibility control** via `MeshComponent.SetIsVisible()` for performance
- **Key repeat system** for smooth left/right movement without event spam

## Sansar Platform Constraints

### C# 5 Compatibility Requirements
The Sansar platform uses C# 5, requiring these patterns:

```csharp
// ❌ Don't use - C# 6+ features not supported
var message = $"Position: {x},{y}";
var dict = new Dictionary<string, int> { ["key"] = value };
public string Property { get; } = "value";

// ✅ Use - C# 5 compatible patterns  
var message = string.Format("Position: {0},{1}", x, y);
var dict = new Dictionary<string, int> { {"key", value} };
private readonly string _property = "value";
public string Property { get { return _property; } }
```

### Sansar API Patterns
**Event Data Classes:**
```csharp
public interface ISimpleData
{
    AgentInfo AgentInfo { get; }
    ObjectId ObjectId { get; }
    ObjectId SourceObjectId { get; }
    Reflective ExtraData { get; }
}

public class GameEventData : Reflective, ISimpleData
{
    private readonly Reflective _extraData;
    public GameEventData(ScriptBase script) { _extraData = script; }
    // ... property implementations
}
```

**Timing System:**
```csharp
// ❌ DateTime.UtcNow not available
// ✅ Use Stopwatch for timing
private long lastDropTime = Stopwatch.GetTimestamp();
double elapsedSeconds = (double)(Stopwatch.GetTimestamp() - lastDropTime) / Stopwatch.Frequency;
```

**Coroutine Structure:**
```csharp
private void GameLoop()
{
    while (true)
    {
        // Game logic here
        Wait(TimeSpan.FromSeconds(0.1)); // Must use TimeSpan, not float
    }
}
```

## Material Control System

### Block Appearance Control
Each of the 400 blocks (200 game + 200 background) supports dynamic material control:

```csharp
// In TetrisGame.cs - Direct visibility and material control
public void SetBlockVisible(int gridX, int gridY, int pieceType, bool isActivePiece = false)
{
    MeshComponent mesh = blockMeshes[gridX, gridY];
    if (mesh != null && mesh.IsScriptable)
    {
        // First make the block visible
        mesh.SetIsVisible(true);
        
        // Then set its color and properties
        foreach (var material in mesh.GetRenderMaterials())
        {
            var props = material.GetProperties();
            props.Tint = PieceColors[pieceType];
            props.EmissiveIntensity = isActivePiece ? 2.0f : 0.0f;
            material.SetProperties(props);
        }
    }
}

// Background blocks use similar control but with fixed black color
public void SetBackgroundBlockVisible(int gridX, int gridY)
{
    MeshComponent mesh = backgroundBlockMeshes[gridX, gridY];
    mesh.SetIsVisible(true);
    // Set to BackgroundColor (0.1f, 0.1f, 0.1f, 1.0f)
}
```

**Piece Colors:**
- I-piece: Cyan
- O-piece: Yellow  
- T-piece: Purple
- S-piece: Green
- Z-piece: Red
- J-piece: Blue
- L-piece: Orange

### Flash Effects for Line Clearing
```csharp
private void SetFlashVisual(int gridX, int gridY)
{
    MeshComponent mesh = blockMeshes[gridX, gridY];
    if (mesh != null && mesh.IsScriptable)
    {
        foreach (var material in mesh.GetRenderMaterials())
        {
            var props = material.GetProperties();
            props.Tint = Sansar.Color.White;
            props.EmissiveIntensity = 15.0f;  // High intensity for flash effect
            material.SetProperties(props);
        }
    }
}
```

### Background Grid System
The background grid provides visual reference:
- 200 additional blocks positioned slightly behind game blocks
- Always visible with dark gray color (0.1f, 0.1f, 0.1f)
- Same grid positions but offset on Y axis (forward/back)
- Helps players see the game boundaries

## Development Knowledge Base

### Sansar Examples and Documentation
The `Sansar-Scripting-Knowledge-Base/` directory contains:
- **Assemblies/**: Sansar.Script.dll and Sansar.Simulation.dll for compilation
- **Examples1-4/**: Working Sansar script examples for reference
- **HtmlDocumentation/**: Complete Sansar API documentation
- **Guides**: Material control and object spawning documentation

### Common Sansar Patterns
Reference these examples for standard patterns:
- **Event messaging**: `Examples3/SimpleSenderScript.cs`, `SimpleListenerScript.cs`
- **Coroutines**: `Examples2/CoroutineExample.cs`
- **Material control**: `Examples4/DarkfyreAlgoma/material-api/`
- **Object spawning**: `Examples4/binah/intro-to-quests/`

## Known Issues and Limitations

### Current Implementation
- **Fully functional**: TetrisGame.cs compiles and runs successfully
- **Key repeat system**: Implemented with counter-based approach for smooth movement
- **Background grid offset**: Currently using positive Y offset (forward) - may need adjustment based on camera angle

### Platform Upload Process
1. Compile locally to verify syntax
2. Upload .cs files directly to Sansar (not .dll files)
3. Sansar compiles scripts server-side with additional restrictions
4. Test in Sansar environment for final validation

## Testing and Validation

### Local Testing
```bash
# Verify basic compilation works
"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe" /target:library /reference:"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll" /reference:"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll" /out:"bin/test.dll" [ScriptFile].cs
```

### Sansar Testing Setup
Requires minimal scene configuration:
1. **TetrisBlock ClusterResource**: Block with scriptable materials supporting Tint and Emissive
2. **Seat Object**: Any chair/seat with sit points configured
3. **TetrisGame.cs**: Attach to the seat object with proper configuration:
   - GenericBlock = TetrisBlock resource
   - GridOrigin = bottom-left corner position
   - DebugRainbowMode = true for testing

The complete setup process is documented in README.md under "In-Game Setup Instructions."

## Historical: Previous Multi-Script Architecture

*Note: This section documents the original 5-script architecture for historical reference. The current implementation uses a single unified script.*

The original design used separate scripts with event-based communication:
1. **SeatController.cs** - Player detection and game initiation
2. **GameManager.cs** - Game logic, piece movement, and player input handling  
3. **GridManager.cs** - 200-block grid spawning and state management
4. **BlockController.cs** - Individual block material control (tint, emissivity, visibility)
5. **PieceLogic.cs** - Static piece calculations and collision detection

This architecture was replaced due to:
- Complex event timing issues between scripts
- Difficulty debugging inter-script communication
- Performance overhead of event messaging
- Sansar's limitations on script-to-script data passing

The unified single-script approach eliminates these issues while maintaining the same game functionality.