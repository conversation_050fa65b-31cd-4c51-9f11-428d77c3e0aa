<html>
  <head>
    <title>Sansar.Utility.JsonSerializationData</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Script</a> : <a href="index.html">Sansar.Utility Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Utility.JsonSerializationData">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Utility.JsonSerializationData:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Utility.JsonSerializationData:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Utility.JsonSerializationData:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Utility.JsonSerializationData">JsonSerializationData  Class</h1>
    <p class="Summary" id="T:Sansar.Utility.JsonSerializationData:Summary">
            The result of a serialization request.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Utility.JsonSerializationData:Signature">public class  <b>JsonSerializationData</b> : <a href="../Sansar.Script/OperationCompleteEvent.html">Sansar.Script.OperationCompleteEvent</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Utility.JsonSerializationData:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Utility.JsonSerializationData:Docs:Remarks">
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Utility.JsonSerializationData:Docs:Version Information">
        <b>Namespace: </b>Sansar.Utility<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/OperationCompleteEvent.html">Sansar.Script.OperationCompleteEvent</a>.
							</p>
        <h2 class="Section">Public Constructors</h2>
        <div class="SectionBox" id="Public Constructors">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td>
                  <div>
                    <b>
                      <a href="#C:Sansar.Utility.JsonSerializationData()">JsonSerializationData</a>
                    </b>()</div>
                </td>
                <td>
                  <span class="NotEntered">Documentation for this section has not yet been entered.</span>
                </td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/OperationCompleteEvent.html#P:Sansar.Script.OperationCompleteEvent.Exception">Exception</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Exception">Exception</a>
                  </i>. 
            If an operation threw an exception Success will be false and the exception will be stored here.
             (<i>Inherited from <a href="../Sansar.Script/OperationCompleteEvent.html">Sansar.Script.OperationCompleteEvent</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Utility.JsonSerializationData.JsonString">JsonString</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. 
            The Json string.
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/OperationCompleteEvent.html#P:Sansar.Script.OperationCompleteEvent.Message">Message</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a>
                  </i>. 
            If Success is false, Message may contain more information about the failure.
             (<i>Inherited from <a href="../Sansar.Script/OperationCompleteEvent.html">Sansar.Script.OperationCompleteEvent</a>.</i>)</td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="#P:Sansar.Utility.JsonSerializationData.Object">Object</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a>
                  </i>. 
            The object.
            </td>
              </tr>
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/OperationCompleteEvent.html#P:Sansar.Script.OperationCompleteEvent.Success">Success</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            Whether or not the operation completed successfully. 
             (<i>Inherited from <a href="../Sansar.Script/OperationCompleteEvent.html">Sansar.Script.OperationCompleteEvent</a>.</i>)</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>override </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="../Sansar.Script/EventData.html#M:Sansar.Script.EventData.ToString()">ToString</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>
            Generates a string representation of the EventData.
             (<i>Inherited from <a href="../Sansar.Script/EventData.html">Sansar.Script.EventData</a>.</i>)</blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Utility.JsonSerializationData:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="C:Sansar.Utility.JsonSerializationData()">JsonSerializationData Constructor</h3>
        <blockquote id="C:Sansar.Utility.JsonSerializationData():member">
          <div class="msummary">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2>Syntax</h2>
          <div class="Signature">public  <b>JsonSerializationData</b> ()</div>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="C:Sansar.Utility.JsonSerializationData():Remarks">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="C:Sansar.Utility.JsonSerializationData():Version Information">
            <b>Namespace: </b>Sansar.Utility<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Utility.JsonSerializationData.JsonString">JsonString Property</h3>
        <blockquote id="P:Sansar.Utility.JsonSerializationData.JsonString:member">
          <div class="msummary">
            The Json string.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>JsonString</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Utility.JsonSerializationData.JsonString:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Utility.JsonSerializationData.JsonString:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Utility.JsonSerializationData.JsonString:Version Information">
            <b>Namespace: </b>Sansar.Utility<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="P:Sansar.Utility.JsonSerializationData.Object">Object Property</h3>
        <blockquote id="P:Sansar.Utility.JsonSerializationData.Object:member">
          <div class="msummary">
            The object.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Object">object</a> <b>Object</b>  { get; }</div>
          <h4 class="Subsection">Value</h4>
          <blockquote class="SubsectionBox" id="P:Sansar.Utility.JsonSerializationData.Object:Value">
            <span class="NotEntered">Documentation for this section has not yet been entered.</span>
          </blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="P:Sansar.Utility.JsonSerializationData.Object:Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="P:Sansar.Utility.JsonSerializationData.Object:Version Information">
            <b>Namespace: </b>Sansar.Utility<br /><b>Assembly: </b>Sansar.Script (in Sansar.Script.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>