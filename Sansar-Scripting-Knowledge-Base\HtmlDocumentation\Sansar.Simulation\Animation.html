<html>
  <head>
    <title>Sansar.Simulation.Animation</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <style>
        a { text-decoration: none }

        body {margin-left: 20px;margin-right: 20px;}

        div.SideBar {
        padding-left: 1em;
        padding-right: 1em;
        right: 0;
        float: right;
        border: thin solid black;
        background-color: #f2f2f2;
        }

        .CollectionTitle { font-weight: bold }
        .PageTitle { font-size: 150%; font-weight: bold }

        .Summary { }
        .Signature { }
        .Remarks { }
        .Members { }
        .Copyright { }

        .Section { font-size: 125%; font-weight: bold }
        p.Summary {
        margin-left: 1em;
        }
        .SectionBox { margin-left: 2em }
        .NamespaceName { font-size: 105%; font-weight: bold }
        .NamespaceSumary { }
        .MemberName { font-size: 115%; font-weight: bold; margin-top: 1em }
        .Subsection { font-size: 105%; font-weight: bold }
        .SubsectionBox { margin-left: 2em; margin-bottom: 1em }

        .CodeExampleTable { background-color: #f5f5dd; border: thin solid black; padding: .25em; width: 60%;}

        .TypesListing {
        border-collapse: collapse;
        }

        td {
        vertical-align: top;
        }
        th {
        text-align: left;
        }

        .TypesListing td {
        margin: 0px;
        padding: .25em;
        border: solid gray 1px;
        }

        .TypesListing th {
        margin: 0px;
        padding: .25em;
        background-color: #f2f2f2;
        border: solid gray 1px;
        }

        div.Footer {
        border-top: 1px solid gray;
        margin-top: 1.5em;
        padding-top: 0.6em;
        text-align: center;
        color: gray;
        }

        span.NotEntered /* Documentation for this section has not yet been entered */ {
        font-style: italic;
        color: red;
        }

        [id$=Remarks] span.NotEntered {
        visibility: hidden;
        }

        div.Header {
        background: #B0C4DE;
        border: double;
        border-color: white;
        border-width: 7px;
        padding: 0.5em;
        }

        div.Header * {
        font-size: smaller;
        }

        div.Note {
        }

        i.ParamRef {
        }

        i.subtitle {
        }

        ul.TypeMembersIndex {
        text-align: left;
        background: #F8F8F8;
        }

        ul.TypeMembersIndex li {
        display: inline;
        margin:  0.5em;
        }

        table.HeaderTable {
        }

        table.SignatureTable {
        }

        table.Documentation, table.Enumeration, table.TypeDocumentation {
        border-collapse: collapse;
        width: 100%;
        }

        table.Documentation tr th, table.TypeMembers tr th, table.Enumeration tr th, table.TypeDocumentation tr th {
        background: whitesmoke;
        padding: 0.8em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: bottom;
        }

        table.Documentation tr td, table.TypeMembers tr td, table.Enumeration tr td, table.TypeDocumentation tr td {
        padding: 0.5em;
        border: 1px solid gray;
        text-align: left;
        vertical-align: top;
        }

        table.TypeMembers {
        border: 1px solid #C0C0C0;
        width: 100%;
        }

        table.TypeMembers tr td {
        background: #F8F8F8;
        border: white;
        }

        table.Documentation {
        }

        table.TypeMembers {
        }

        div.CodeExample {
        width: 100%;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;
        }

        div.CodeExample p {
        margin: 0.5em;
        border-bottom: 1px solid #DDDDDD;
        }

        div.CodeExample div {
        margin: 0.5em;
        }

        h4 {
        margin-bottom: 0;
        }

        div.Signature {
        border: 1px solid #C0C0C0;
        background: #F2F2F2;
        padding: 1em;
        }
    </style>
    <script type="text/JavaScript">
      function toggle_display (block) {
        var w = document.getElementById (block);
        var t = document.getElementById (block + ":toggle");
        if (w.style.display == "none") {
          w.style.display = "block";
          t.innerHTML = "⊟";
        } else {
          w.style.display = "none";
          t.innerHTML = "⊞";
        }
      }
    </script>
  </head>
  <body>
    <div class="CollectionTitle">
      <a href="../index.html">Sansar.Simulation</a> : <a href="index.html">Sansar.Simulation Namespace</a></div>
    <div class="SideBar">
      <p>
        <a href="#T:Sansar.Simulation.Animation">Overview</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.Animation:Signature">Signature</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.Animation:Docs">Remarks</a>
      </p>
      <p>
        <a href="#Members">Members</a>
      </p>
      <p>
        <a href="#T:Sansar.Simulation.Animation:Members">Member Details</a>
      </p>
    </div>
    <h1 class="PageTitle" id="T:Sansar.Simulation.Animation">Animation  Class</h1>
    <p class="Summary" id="T:Sansar.Simulation.Animation:Summary">
            Represents a scriptable Animation node.
            </p>
    <div>
      <h2>Syntax</h2>
      <div class="Signature" id="T:Sansar.Simulation.Animation:Signature">[Sansar.Script.Interface]<br />public class  <b>Animation</b> : <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a></div>
    </div>
    <div class="Remarks" id="T:Sansar.Simulation.Animation:Docs">
      <h2 class="Section">Remarks</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.Animation:Docs:Remarks">
      </div>
      <h2 class="Section">Requirements</h2>
      <div class="SectionBox" id="T:Sansar.Simulation.Animation:Docs:Version Information">
        <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
      <h2 class="Section" id="Members">Members</h2>
      <div class="SectionBox" id="_Members">
        <p>
								See Also: Inherited members from
								<a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.
							</p>
        <h2 class="Section">Public Properties</h2>
        <div class="SectionBox" id="Public Properties">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>[read-only]<div></div></td>
                <td>
                  <b>
                    <a href="../Sansar.Script/InstanceInterface.html#P:Sansar.Script.InstanceInterface.IsValid">IsValid</a>
                  </b>
                </td>
                <td>
                  <i>
                    <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a>
                  </i>. 
            Whether or not this interface is valid.
             (<i>Inherited from <a href="../Sansar.Script/InstanceInterface.html">Sansar.Script.InstanceInterface</a>.</i>)</td>
              </tr>
            </table>
          </div>
        </div>
        <h2 class="Section">Public Methods</h2>
        <div class="SectionBox" id="Public Methods">
          <div class="SubsectionBox">
            <table class="TypeMembers">
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.GetFrameCount()">GetFrameCount</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a></nobr><blockquote>
            Gets the frame count of the animation
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.GetName()">GetName</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a></nobr><blockquote>
            Gets the name of the animation
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.GetParameters()">GetParameters</a>
                  </b>()<nobr> : <a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a></nobr><blockquote>
            Gets the last applied parameters for the animation
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.IsPaused()">IsPaused</a>
                  </b>()<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a></nobr><blockquote>
            Gets whether or not the animation is paused.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.JumpToFrame(System.Int32)">JumpToFrame</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>)<blockquote>
            Jump to frame. The animation will be set to the specified frame, and paused.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,Sansar.Script.ScriptBase.OperationComplete)">JumpToFrame</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>
             Jump to frame. The animation will be set to the specified frame, and paused.
             </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent})">JumpToFrame</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>
            Jump to frame. The animation will be set to the specified frame, and paused.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Pause()">Pause</a>
                  </b>()<blockquote>
            Pauses the animation.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Pause(Sansar.Script.ScriptBase.OperationComplete)">Pause</a>
                  </b>(<a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>
            Pauses the animation.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Pause(System.Action{Sansar.Script.OperationCompleteEvent})">Pause</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>
            Pauses the animation.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Play()">Play</a>
                  </b>()<blockquote>
            Plays the animation. It will become the active animation for this object, and resume playing if it is paused.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Play(Sansar.Script.ScriptBase.OperationComplete)">Play</a>
                  </b>(<a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>
            Plays the animation, without changing the current animation frame.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters)">Play</a>
                  </b>(<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a>)<blockquote>
            Plays the animation and applies the provided parameters.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Play(System.Action{Sansar.Script.OperationCompleteEvent})">Play</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>
            Plays the animation. It will become the active animation for this object, and resume playing if it is paused.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete)">Play</a>
                  </b>(<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>
            Plays the animation and applies the provided parameters.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent})">Play</a>
                  </b>(<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>
            Plays the animation and applies the provided parameters.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Reset()">Reset</a>
                  </b>()<blockquote>
            Resets the animation. The animation is paused and reset to frame 0 (or the RangeStartFrame if clamping is enabled).
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Reset(Sansar.Script.ScriptBase.OperationComplete)">Reset</a>
                  </b>(<a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>
            Resets the animation. The animation is paused and reset to frame 0 (or the RangeStartFrame if clamping is enabled).
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters)">Reset</a>
                  </b>(<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a>)<blockquote>
            Resets the animation and applies the provided parameters.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Reset(System.Action{Sansar.Script.OperationCompleteEvent})">Reset</a>
                  </b>(<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>
            Resets the animation. The animation is paused and reset to frame 0 (or the RangeStartFrame if clamping is enabled).
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete)">Reset</a>
                  </b>(<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>
            Resets the animation and applies the provided parameters.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent})">Reset</a>
                  </b>(<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>
            Resets the animation and applies the provided parameters.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters)">SetParameters</a>
                  </b>(<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a>)<blockquote>
            Sets the parameters for the animation. The parameters are applied immediately.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete)">SetParameters</a>
                  </b>(<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a>, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a>)<nobr> : <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a></nobr><blockquote>
            Sets the parameters for the animation. The parameters are applied immediately.
            </blockquote></td>
              </tr>
              <tr valign="top">
                <td>
                  <div>
                  </div>
                </td>
                <td colspan="2">
                  <b>
                    <a href="#M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent})">SetParameters</a>
                  </b>(<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a>, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a>)<blockquote>
            Sets the parameters for the animation. The parameters are applied immediately.
            </blockquote></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="Members" id="T:Sansar.Simulation.Animation:Members">
      <h2 class="Section" id="MemberDetails">Member Details</h2>
      <div class="SectionBox" id="_MemberDetails">
        <h3 id="M:Sansar.Simulation.Animation.GetFrameCount()">GetFrameCount Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.GetFrameCount():member">
          <div class="msummary">
            Gets the frame count of the animation
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> <b>GetFrameCount</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.GetFrameCount():Returns">Returns the total number of frames in the animation.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.GetFrameCount():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.GetFrameCount():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.GetName()">GetName Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.GetName():member">
          <div class="msummary">
            Gets the name of the animation
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.String">string</a> <b>GetName</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.GetName():Returns">Returns the name of the animation.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.GetName():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.GetName():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.GetParameters()">GetParameters Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.GetParameters():member">
          <div class="msummary">
            Gets the last applied parameters for the animation
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a> <b>GetParameters</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.GetParameters():Returns">Returns the current parameters.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.GetParameters():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.GetParameters():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.IsPaused()">IsPaused Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.IsPaused():member">
          <div class="msummary">
            Gets whether or not the animation is paused.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Boolean">bool</a> <b>IsPaused</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.IsPaused():Returns">A bool value indicating the pause state.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.IsPaused():Remarks">
          </div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.IsPaused():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32)">JumpToFrame Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32):member">
          <div class="msummary">
            Jump to frame. The animation will be set to the specified frame, and paused.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>JumpToFrame</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> frame)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32):Parameters">
            <dl>
              <dt>
                <i>frame</i>
              </dt>
              <dd>Frame to jump to.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,Sansar.Script.ScriptBase.OperationComplete)">JumpToFrame Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">
             Jump to frame. The animation will be set to the specified frame, and paused.
             </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>JumpToFrame</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> frame, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>frame</i>
              </dt>
              <dd>Frame to jump to.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,Sansar.Script.ScriptBase.OperationComplete):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,Sansar.Script.ScriptBase.OperationComplete):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent})">JumpToFrame Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Jump to frame. The animation will be set to the specified frame, and paused.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>JumpToFrame</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Int32">int</a> frame, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>frame</i>
              </dt>
              <dd>Frame to jump to.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.JumpToFrame(System.Int32,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Pause()">Pause Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Pause():member">
          <div class="msummary">
            Pauses the animation.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Pause</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Pause():Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Pause():Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Pause():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Pause(Sansar.Script.ScriptBase.OperationComplete)">Pause Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Pause(Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">
            Pauses the animation.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>Pause</b> (<a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Pause(Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Pause(Sansar.Script.ScriptBase.OperationComplete):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Pause(Sansar.Script.ScriptBase.OperationComplete):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Pause(Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Pause(System.Action{Sansar.Script.OperationCompleteEvent})">Pause Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Pause(System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Pauses the animation.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Pause</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Pause(System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Pause(System.Action{Sansar.Script.OperationCompleteEvent}):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Pause(System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Pause(System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Play()">Play Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Play():member">
          <div class="msummary">
            Plays the animation. It will become the active animation for this object, and resume playing if it is paused.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Play</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Play():Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Play():Remarks">If the animation has PlaybackMode.PlayOnce and has finished playing, this will have no effect, the animation will have to be reset with Reset() first. To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Play():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Play(Sansar.Script.ScriptBase.OperationComplete)">Play Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Play(Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">
            Plays the animation, without changing the current animation frame.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>Play</b> (<a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Script.ScriptBase.OperationComplete):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Script.ScriptBase.OperationComplete):Remarks">If the animation has PlaybackMode.PlayOnce and has finished playing, this will have no effect, the animation will have to be reset with Reset() first. To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters)">Play Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters):member">
          <div class="msummary">
            Plays the animation and applies the provided parameters.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Play</b> (<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a> parameters)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters):Parameters">
            <dl>
              <dt>
                <i>parameters</i>
              </dt>
              <dd>Animation parameters to apply.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Play(System.Action{Sansar.Script.OperationCompleteEvent})">Play Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Play(System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Plays the animation. It will become the active animation for this object, and resume playing if it is paused.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Play</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Play(System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Play(System.Action{Sansar.Script.OperationCompleteEvent}):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Play(System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">If the animation has PlaybackMode.PlayOnce and has finished playing, this will have no effect, the animation will have to be reset with Reset() first. To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Play(System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete)">Play Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">
            Plays the animation and applies the provided parameters.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>Play</b> (<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a> parameters, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>parameters</i>
              </dt>
              <dd>Animation parameters to apply.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent})">Play Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Plays the animation and applies the provided parameters.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Play</b> (<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a> parameters, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>parameters</i>
              </dt>
              <dd>Animation parameters to apply.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Play(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Reset()">Reset Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Reset():member">
          <div class="msummary">
            Resets the animation. The animation is paused and reset to frame 0 (or the RangeStartFrame if clamping is enabled).
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Reset</b> ()</div>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Reset():Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Reset():Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Reset():Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Reset(Sansar.Script.ScriptBase.OperationComplete)">Reset Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Reset(Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">
            Resets the animation. The animation is paused and reset to frame 0 (or the RangeStartFrame if clamping is enabled).
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>Reset</b> (<a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Script.ScriptBase.OperationComplete):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Script.ScriptBase.OperationComplete):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters)">Reset Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters):member">
          <div class="msummary">
            Resets the animation and applies the provided parameters.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Reset</b> (<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a> parameters)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters):Parameters">
            <dl>
              <dt>
                <i>parameters</i>
              </dt>
              <dd>Animation parameters to apply.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Reset(System.Action{Sansar.Script.OperationCompleteEvent})">Reset Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Reset(System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Resets the animation. The animation is paused and reset to frame 0 (or the RangeStartFrame if clamping is enabled).
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Reset</b> (<a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Reset(System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Reset(System.Action{Sansar.Script.OperationCompleteEvent}):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Reset(System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Reset(System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete)">Reset Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">
            Resets the animation and applies the provided parameters.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>Reset</b> (<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a> parameters, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>parameters</i>
              </dt>
              <dd>Animation parameters to apply.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent})">Reset Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Resets the animation and applies the provided parameters.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>Reset</b> (<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a> parameters, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>parameters</i>
              </dt>
              <dd>Animation parameters to apply.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.Reset(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters)">SetParameters Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters):member">
          <div class="msummary">
            Sets the parameters for the animation. The parameters are applied immediately.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetParameters</b> (<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a> parameters)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters):Parameters">
            <dl>
              <dt>
                <i>parameters</i>
              </dt>
              <dd>Animation parameters to apply.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters):Remarks">The range start frame will be clamped to the length of the animation, and the range end frame will be clamped between the range start frame and the end of the animation. To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete)">SetParameters Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):member">
          <div class="msummary">
            Sets the parameters for the animation. The parameters are applied immediately.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />[System.Obsolete("Deprecated. Use the Action&lt;&gt; overload", false)]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.UInt64">ulong</a> <b>SetParameters</b> (<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a> parameters, <a href="../Sansar.Script/ScriptBase+OperationComplete.html">Sansar.Script.ScriptBase.OperationComplete</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):Parameters">
            <dl>
              <dt>
                <i>parameters</i>
              </dt>
              <dd>Animation parameters to apply.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):Remarks">The range start frame will be clamped to the length of the animation, and the range end frame will be clamped between the range start frame and the end of the animation. To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,Sansar.Script.ScriptBase.OperationComplete):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
        <h3 id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent})">SetParameters Method</h3>
        <blockquote id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):member">
          <div class="msummary">
            Sets the parameters for the animation. The parameters are applied immediately.
            </div>
          <h2>Syntax</h2>
          <div class="Signature">[Sansar.Script.Interface]<br />public <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Void">void</a> <b>SetParameters</b> (<a href="../Sansar.Simulation/AnimationParameters.html">AnimationParameters</a> parameters, <a href="http://www.go-mono.com/docs/monodoc.ashx?link=T:System.Action`1">Action&lt;Sansar.Script.OperationCompleteEvent&gt;</a> handler)</div>
          <h4 class="Subsection">Parameters</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):Parameters">
            <dl>
              <dt>
                <i>parameters</i>
              </dt>
              <dd>Animation parameters to apply.</dd>
              <dt>
                <i>handler</i>
              </dt>
              <dd>Handler to be called when the event completes.</dd>
            </dl>
          </blockquote>
          <h4 class="Subsection">Returns</h4>
          <blockquote class="SubsectionBox" id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):Returns">Returns an internal event id.</blockquote>
          <h2 class="Section">Remarks</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):Remarks">The range start frame will be clamped to the length of the animation, and the range end frame will be clamped between the range start frame and the end of the animation. To block until the method returns pass this to <a href="../Sansar.Script/ScriptBase.html#M:Sansar.Script.ScriptBase.WaitFor(System.Func{System.Action{Sansar.Script.EventData},System.Boolean,Sansar.Script.IEventSubscription})">Sansar.Script.ScriptBase.WaitFor(Func&lt;Action&lt;Sansar.Script.EventData&gt;,System.Boolean,Sansar.Script.IEventSubscription&gt;)</a> in a coroutine.</div>
          <h2 class="Section">Requirements</h2>
          <div class="SectionBox" id="M:Sansar.Simulation.Animation.SetParameters(Sansar.Simulation.AnimationParameters,System.Action{Sansar.Script.OperationCompleteEvent}):Version Information">
            <b>Namespace: </b>Sansar.Simulation<br /><b>Assembly: </b>Sansar.Simulation (in Sansar.Simulation.dll)<br /><b>Assembly Versions: </b>*******</div>
          <hr size="1" />
        </blockquote>
      </div>
    </div>
    <hr size="1" />
    <div class="Copyright">
    </div>
  </body>
</html>